# Cloud IDE Docker Environment Configuration
# This file contains environment variables for Docker Compose

# Global Configuration
COMPOSE_PROJECT_NAME=cloud-ide
NODE_ENV=production
LOG_LEVEL=info

# Container Manager Configuration
CONTAINER_MANAGER_PORT=3000
DOCKER_SOCKET=/var/run/docker.sock

# DNS Server Configuration
DNS_PORT=5354
DNS_API_PORT=8053
DNS_DOMAIN=ide.local
DNS_TTL=3600

# Redis Configuration
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# Nginx Configuration
NGINX_HTTP_PORT=80
NGINX_HTTPS_PORT=443

# Integration Configuration
EVENT_BUS_PORT=9000
HEALTH_CHECK_INTERVAL=30s
SYNC_INTERVAL=30s

# Security Configuration
NODEJS_USER_ID=1001
NODEJS_GROUP_ID=1001

# Network Configuration
NETWORK_SUBNET=**********/16

# Volume Configuration
REDIS_DATA_PATH=./data/redis
LOGS_PATH=./logs
CONFIG_PATH=./config
