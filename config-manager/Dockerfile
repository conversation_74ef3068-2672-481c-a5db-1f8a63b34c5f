FROM node:18-alpine

# Install Docker CLI for container management
RUN apk add --no-cache docker-cli curl

WORKDIR /app

# Copy package files and install deps
COPY package*.json ./
RUN npm install --production

# Copy source code
COPY src/ ./src/

# Copy shared services (will be mounted as volume in production)
COPY ../shared ./shared

# Create necessary directories
RUN mkdir -p /app/logs && \
    chmod 755 /app/logs

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nodejs -u 1001

# Change ownership of app directory
RUN chown -R nodejs:nodejs /app

# Switch to non-root user
USER nodejs

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:3000/health || exit 1

CMD ["npm", "start"]
