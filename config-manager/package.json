{"name": "config-manager", "version": "1.0.0", "main": "index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js"}, "keywords": [], "author": "<PERSON><PERSON><PERSON>", "license": "ISC", "description": "", "dependencies": {"axios": "^1.9.0", "dockerode": "^4.0.6", "dotenv": "^16.5.0", "express": "^5.1.0", "winston": "^3.17.0", "joi": "^17.11.0", "uuid": "^9.0.1"}, "devDependencies": {"nodemon": "^3.1.10"}}