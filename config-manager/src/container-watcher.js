const Docker = require("dockerode");
const fs = require("fs").promises;
const NginxManager = require("./nginx-manager");
const logger = require("./logger");
const axios = require("axios");
const { eventBus, EventTypes, errorHandler, configManager } = require("../../../shared");

class ContainerWatcher {
  constructor() {
    this.docker = new Docker(); // Connects to Docker socket
    this.nginxManager = new NginxManager(); // Manages NGiNX configuration
    this.dnsApiUrl = process.env.DNS_API_URL || "http://dns-server:8053";
    this.isRunning = false;
    this.eventStream = null;
  }
  async start() {
    if (this.isRunning) {
      logger.warn("Container watcher is already running");
      return;
    }

    try {
      // Initialize nginx manager
      await this.nginxManager.initialize();

      // Initialize configuration
      if (!configManager.initialized) {
        await configManager.initialize();
      }

      const config = configManager.getAll();
      this.dnsApiUrl = process.env.DNS_API_URL || `http://localhost:${config.dnsServer.apiPort}`;

      // Subscribe to integration events
      this.setupEventSubscriptions();

      // Start Docker event stream with error handling
      this.eventStream = await errorHandler.executeWithProtection('docker.getEvents', async () => {
        return await this.docker.getEvents();
      }, {
        component: 'reverse-proxy',
        operation: 'getEvents',
        retry: { maxRetries: 3, baseDelay: 2000 }
      });

      this.eventStream.on("data", async (chunk) => {
        try {
          const event = JSON.parse(chunk.toString()); // Parsing the event data
          await this.handleContainerEvent(event);
        } catch (error) {
          logger.error("❌ Error processing Docker event:", error);

          await eventBus.publish(EventTypes.SYSTEM_ERROR, {
            component: 'reverse-proxy',
            operation: 'process_docker_event',
            error: error.message
          });
        }
      });

      this.eventStream.on("error", async (error) => {
        logger.error("❌ Docker event stream error:", error);

        await eventBus.publish(EventTypes.SYSTEM_ERROR, {
          component: 'reverse-proxy',
          operation: 'docker_event_stream',
          error: error.message
        });

        // Attempt to restart the stream
        setTimeout(() => this.restart(), 5000);
      });

      await this.syncExistingContainers(); // Syncing existing containers with Nginx

      this.isRunning = true;
      logger.info("🚀 Docker event watcher started");

      // Publish startup event
      await eventBus.publish(EventTypes.SYSTEM_RECOVERY, {
        component: 'reverse-proxy',
        action: 'container_watcher_started',
        message: 'Container watcher started successfully'
      });

    } catch (err) {
      logger.error("❌ Failed to start watcher:", err.message);

      await eventBus.publish(EventTypes.SYSTEM_ERROR, {
        component: 'reverse-proxy',
        operation: 'start_watcher',
        error: err.message
      });

      throw err;
    }
  }
  async handleContainerEvent(event) {
    if (event.Type !== "container") return; // only handle container events

    try {
      const container = this.docker.getContainer(event.id);
      const info = await container.inspect();

      if (event.Action === "start") {
        await this.handleContainerStart(info);
      } else if (event.Action === "die") {
        await this.handleContainerStop(info);
      }
    } catch (err) {
      logger.error("❌ Failed to handle event:", err.message);
    }
  }

  async handleContainerStart(containerInfo) {
    const subdomain = containerInfo.Config.Labels["ide.subdomain"];
    if (!subdomain) return; // if no subdomain label, skippp

    /* Get the correct network name (from docker-compose),
     or fallback to the first network if not found */
    const networkName =
      Object.keys(containerInfo.NetworkSettings.Networks).find((name) =>
        name.includes("ide-network")
      ) || Object.keys(containerInfo.NetworkSettings.Networks)[0];

    const ipAddress =
      containerInfo.NetworkSettings.Networks[networkName]?.IPAddress;
    if (!ipAddress) {
      this.logger.error(`⚠️ Couldd not get IP for ${subdomain}`);
      return;
    }

    // Try to detect the exposed port, fallback to 80
    let port = 80;
    const exposedPorts = containerInfo.Config.ExposedPorts;
    if (exposedPorts) {
      const firstPort = Object.keys(exposedPorts)[0];
      if (firstPort) {
        port = parseInt(firstPort.split("/")[0], 10);
      }
    }

    await this.nginxManager.updateConfiguration(subdomain, ipAddress, port); // updates Nginx config

    logger.info(`🟢 Started: ${subdomain} → ${ipAddress}:${port}`);
  }

  async handleContainerStop(containerInfo) {
    const subdomain = containerInfo.Config.Labels["ide.subdomain"];
    if (!subdomain) return;

    try {
      // Remove Nginx config
      const configPath = `/etc/nginx/conf.d/${subdomain}.conf`;
      await fs.unlink(configPath);
      await this.nginxManager.reloadNginx();

      // Mark DNS record as inactive instead of removing
      await this.nginxManager.updateDnsRecord(subdomain, "0.0.0.0", true);

      logger.info(`🔴 Stopped: ${subdomain} → config removed, DNS marked inactive`);
    } catch (err) {
      logger.error("❌ Failed to handle container stop:", err.message);
    }
  }
  async syncExistingContainers() {
    try {
      const containers = await this.docker.listContainers({ all: true }); // listing all containers including stopped ones

      for (const container of containers) {
        if (container.Labels["ide.subdomain"]) {
          const info = await this.docker.getContainer(container.Id).inspect();
          await this.handleContainerStart(info);
        }
      }

      logger.info("🔁 Synced existing containers");
    } catch (err) {
      logger.error("❌ Sync failed:", err.message);
    }
  }
  async syncWithDnsServer() {
    try {
      const response = await axios.get(`${this.dnsApiUrl}/api/dns/subdomains`);
      const existingRecords = response.data.subdomains;
      
      // Sync existing DNS records with running containers
      for (const record of existingRecords) {
        const container = await this.findContainerBySubdomain(record.domain.split('.')[0]);
        if (!container) {
          // Container not running but DNS record exists
          await this.nginxManager.removeDnsRecord(record.domain.split('.')[0]);
        }
      }
      
      logger.info("🔄 Synced with DNS server records");
    } catch (err) {
      logger.error("❌ Failed to sync with DNS server:", err.message);
    }
  }

  setupEventSubscriptions() {
    // Subscribe to container events from the integration service
    eventBus.subscribe(EventTypes.CONTAINER_CREATED, async (event) => {
      logger.info(`📦 Received container created event: ${event.data.name}`);
    });

    eventBus.subscribe(EventTypes.CONTAINER_STARTED, async (event) => {
      logger.info(`▶️ Received container started event: ${event.data.name}`);
    });

    eventBus.subscribe(EventTypes.CONTAINER_STOPPED, async (event) => {
      logger.info(`⏹️ Received container stopped event: ${event.data.name}`);
    });

    eventBus.subscribe(EventTypes.CONTAINER_REMOVED, async (event) => {
      logger.info(`🗑️ Received container removed event: ${event.data.name}`);
    });

    // Subscribe to proxy reload requests
    eventBus.subscribe(EventTypes.PROXY_RELOAD_REQUESTED, async (event) => {
      logger.info(`🔄 Received proxy reload request`);
      await this.nginxManager.reloadNginx();
    });
  }

  async stop() {
    if (!this.isRunning) {
      return;
    }

    try {
      if (this.eventStream) {
        this.eventStream.destroy();
        this.eventStream = null;
      }

      this.isRunning = false;
      logger.info("🛑 Container watcher stopped");

      await eventBus.publish(EventTypes.SYSTEM_ERROR, {
        component: 'reverse-proxy',
        action: 'container_watcher_stopped',
        message: 'Container watcher stopped'
      });

    } catch (error) {
      logger.error("❌ Error stopping container watcher:", error);
    }
  }

  async restart() {
    logger.info("🔄 Restarting container watcher...");
    await this.stop();
    await new Promise(resolve => setTimeout(resolve, 2000));
    await this.start();
  }

  async getHealth() {
    try {
      // Test Docker connection
      await this.docker.ping();

      // Test Nginx manager health
      const nginxHealth = await this.nginxManager.getHealth();

      return {
        status: this.isRunning && nginxHealth.status === 'healthy' ? 'healthy' : 'unhealthy',
        timestamp: new Date().toISOString(),
        isRunning: this.isRunning,
        hasEventStream: !!this.eventStream,
        nginxManager: nginxHealth
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        error: error.message,
        isRunning: this.isRunning
      };
    }
  }
}

module.exports = ContainerWatcher;
