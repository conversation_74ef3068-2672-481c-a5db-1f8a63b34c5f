const fs = require("fs").promises;
const path = require("path");
const axios = require("axios");
const logger = require("./logger");
const { eventBus, EventTypes, errorHandler, configManager } = require("../../../shared");
require("dotenv").config();

class NginxManager {
  constructor() {
    this.templatePath = "/etc/nginx/manual-templates/upstream.conf.template"; // inside Docker
    this.configPath = "/etc/nginx/conf.d"; // live config dir
    this.dnsApiUrl = process.env.DNS_API_URL || "http://dns-server:8053";
    this.initialized = false;
  }

  async initialize() {
    if (this.initialized) return;

    try {
      // Wait for config manager to be initialized
      if (!configManager.initialized) {
        await configManager.initialize();
      }

      const config = configManager.getAll();
      this.templatePath = config.reverseProxy.nginxTemplatePath + "/upstream.conf.template";
      this.configPath = config.reverseProxy.nginxConfigPath;
      this.dnsApiUrl = process.env.DNS_API_URL || `http://localhost:${config.dnsServer.apiPort}`;

      this.initialized = true;
      logger.info("✅ Nginx Manager initialized successfully");
    } catch (error) {
      logger.error("❌ Failed to initialize Nginx Manager:", error);
      throw error;
    }
  }
  async updateConfiguration(subdomain, containerIp, port = 3000) {
    if (!this.initialized) {
      await this.initialize();
    }

    return await errorHandler.executeWithProtection('nginx.updateConfiguration', async () => {
      const template = await fs.readFile(this.templatePath, "utf8");
      const config = template //replacing string with regex. results in Nginx config block for that user/container.
        .replace(/{{subdomain}}/g, subdomain)
        .replace(/{{container_ip}}/g, containerIp)
        .replace(/{{port}}/g, port);

      const configFile = path.join(this.configPath, `${subdomain}.conf`); // building the path to the config file
      await fs.writeFile(configFile, config);

      // Reload nginx with error handling
      const reloadSuccess = await this.reloadNginx();
      if (!reloadSuccess) {
        throw new Error('Nginx reload failed');
      }

      // Update DNS record
      await this.updateDnsRecord(subdomain, containerIp);

      // Publish proxy config updated event
      await eventBus.publish(EventTypes.PROXY_CONFIG_UPDATED, {
        subdomain,
        containerIp,
        port,
        configFile
      });

      logger.info(`✅ NGINX config updated for ${subdomain}`);
    }, {
      component: 'reverse-proxy',
      operation: 'updateConfiguration',
      circuitBreaker: { failureThreshold: 3, resetTimeout: 60000 },
      retry: { maxRetries: 2, baseDelay: 1000 },
      fallback: async () => {
        // Publish failure event
        await eventBus.publish(EventTypes.PROXY_RELOAD_FAILED, {
          subdomain,
          containerIp,
          port,
          reason: 'configuration_update_failed'
        });
        throw new Error(`Failed to update Nginx configuration for ${subdomain}`);
      }
    });
  }
  async reloadNginx() {
    return await errorHandler.executeWithProtection('nginx.reload', async () => {
      // Create reload trigger file inside shared volume
      await fs.writeFile('/etc/nginx/conf.d/../trigger-reload', '');

      // Publish reload requested event
      await eventBus.publish(EventTypes.PROXY_RELOAD_REQUESTED, {
        timestamp: new Date().toISOString(),
        method: 'trigger_file'
      });

      logger.info(`🔄 Requested NGINX reload`);

      // Wait a moment for reload to process
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Publish reload completed event (optimistic)
      await eventBus.publish(EventTypes.PROXY_RELOAD_COMPLETED, {
        timestamp: new Date().toISOString(),
        success: true
      });

      return true;
    }, {
      component: 'reverse-proxy',
      operation: 'reloadNginx',
      retry: { maxRetries: 2, baseDelay: 500 },
      fallback: async () => {
        // Publish reload failed event
        await eventBus.publish(EventTypes.PROXY_RELOAD_FAILED, {
          timestamp: new Date().toISOString(),
          reason: 'trigger_file_creation_failed'
        });
        return false;
      }
    });
  }

  async updateDnsRecord(subdomain, ipAddress, isInactive = false) {
    if (!this.initialized) {
      await this.initialize();
    }

    return await errorHandler.executeWithProtection('nginx.updateDnsRecord', async () => {
      const config = configManager.getAll();
      const domain = config.dnsServer.domain;
      const ttl = config.dnsServer.ttl;

      const payload = {
        subdomain,
        domain,
        ipAddress: isInactive ? "0.0.0.0" : ipAddress,
        ttl,
        isPersistent: true,
        isInactive: isInactive || false
      };

      const response = await axios.post(`${this.dnsApiUrl}/api/dns/subdomains`, payload, {
        timeout: 5000,
        headers: {
          'Content-Type': 'application/json'
        }
      });

      logger.info(`📡 DNS updated for ${subdomain} (${isInactive ? 'inactive' : 'active'})`);
      return response.data;
    }, {
      component: 'reverse-proxy',
      operation: 'updateDnsRecord',
      retry: { maxRetries: 3, baseDelay: 1000 },
      fallback: async () => {
        logger.warn(`⚠️ DNS update failed for ${subdomain}, continuing without DNS update`);
        return null;
      }
    });
  }

  async removeDnsRecord(subdomain) {
    try {
      await axios.delete(`${this.dnsApiUrl}/api/dns/subdomains`, {
        data: {
          subdomain,
          domain: "ide.local",
          type: "all"  // Remove both persistent and temporary records
        }
      });
      logger.info(`🗑️ DNS record removed for ${subdomain}`);
    } catch (err) {
      logger.error("❌ DNS record removal failed:", err.message);
    }
  }

  async removeDnsRecord(subdomain) {
    if (!this.initialized) {
      await this.initialize();
    }

    return await errorHandler.executeWithProtection('nginx.removeDnsRecord', async () => {
      const response = await axios.delete(`${this.dnsApiUrl}/api/dns/subdomains/${subdomain}`, {
        timeout: 5000,
        headers: {
          'Content-Type': 'application/json'
        }
      });

      logger.info(`📡 DNS record removed for ${subdomain}`);
      return response.data;
    }, {
      component: 'reverse-proxy',
      operation: 'removeDnsRecord',
      retry: { maxRetries: 2, baseDelay: 1000 },
      fallback: async () => {
        logger.warn(`⚠️ DNS record removal failed for ${subdomain}, continuing without DNS cleanup`);
        return null;
      }
    });
  }

  async removeConfiguration(subdomain) {
    if (!this.initialized) {
      await this.initialize();
    }

    return await errorHandler.executeWithProtection('nginx.removeConfiguration', async () => {
      const configFile = path.join(this.configPath, `${subdomain}.conf`);

      try {
        await fs.unlink(configFile);
        logger.info(`🗑️ Removed Nginx config for ${subdomain}`);
      } catch (error) {
        if (error.code !== 'ENOENT') {
          throw error;
        }
        logger.info(`ℹ️ Config file for ${subdomain} already removed`);
      }

      // Reload nginx after removing config
      const reloadSuccess = await this.reloadNginx();
      if (!reloadSuccess) {
        throw new Error('Nginx reload failed after config removal');
      }

      // Remove DNS record
      await this.removeDnsRecord(subdomain);

      // Publish config removal event
      await eventBus.publish(EventTypes.PROXY_CONFIG_UPDATED, {
        subdomain,
        action: 'removed',
        configFile
      });

    }, {
      component: 'reverse-proxy',
      operation: 'removeConfiguration',
      retry: { maxRetries: 2, baseDelay: 1000 }
    });
  }

  async getHealth() {
    try {
      const configExists = await fs.access(this.configPath).then(() => true).catch(() => false);
      const templateExists = await fs.access(this.templatePath).then(() => true).catch(() => false);

      return {
        status: configExists && templateExists ? 'healthy' : 'unhealthy',
        timestamp: new Date().toISOString(),
        configPath: this.configPath,
        templatePath: this.templatePath,
        configExists,
        templateExists,
        initialized: this.initialized
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        error: error.message,
        initialized: this.initialized
      };
    }
  }
}

module.exports = NginxManager;
