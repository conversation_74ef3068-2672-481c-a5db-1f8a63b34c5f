const ContainerWatcher = require("./container-watcher");
const logger = require("./logger");
const { configManager, integrationService, eventBus, EventTypes } = require("../../../shared");

async function main() {
  try {
    // Initialize shared services first
    logger.info('Initializing shared services...');
    await configManager.initialize();
    await integrationService.initialize();

    // Start container watcher
    const watcher = new ContainerWatcher();
    await watcher.start();

    // Publish startup complete event
    await eventBus.publish(EventTypes.SYSTEM_RECOVERY, {
      component: 'reverse-proxy',
      action: 'startup_complete',
      message: 'Reverse proxy config manager started successfully'
    });

    // Graceful shutdown handling
    const gracefulShutdown = async (signal) => {
      logger.info(`Received ${signal}. Starting graceful shutdown...`);

      // Publish shutdown event
      await eventBus.publish(EventTypes.SYSTEM_ERROR, {
        component: 'reverse-proxy',
        action: 'shutdown',
        signal
      });

      // Stop watcher
      if (watcher.stop) {
        await watcher.stop();
      }

      logger.info("Config manager shut down gracefully");
      process.exit(0);
    };

    process.on("SIGINT", () => gracefulShutdown('SIGINT'));
    process.on("SIGTERM", () => gracefulShutdown('SIGTERM'));

  } catch (err) {
    logger.error("❌ Failed to start config manager:", err.message);

    // Publish startup failure event
    await eventBus.publish(EventTypes.SYSTEM_ERROR, {
      component: 'reverse-proxy',
      action: 'startup_failed',
      error: err.message
    });

    process.exit(1);
  }
}

main();
