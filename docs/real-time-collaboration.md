# Real-Time Collaboration Feature

## 🎯 Overview

The Cloud IDE now supports real-time collaboration, allowing multiple users to work on the same file simultaneously without authentication. This feature provides:

- **Session-based collaboration** - No login required
- **Real-time document synchronization** - See changes as they happen
- **Cursor and selection tracking** - See where others are working
- **User presence indicators** - Know who's collaborating
- **Nickname management** - Identify collaborators easily

## 🏗️ Architecture

### Backend Components

#### CollaborationService (`container-manager/src/services/collaboration.service.js`)
- Manages collaboration rooms and user sessions
- Handles document synchronization
- Tracks cursor positions and selections
- Manages user presence and nicknames

#### WebSocket Events (`container-manager/src/routes/terminal.routes.js`)
- `collab:join` - Join a collaboration room
- `collab:leave` - Leave a collaboration room
- `collab:document-change` - Send document changes
- `collab:cursor-change` - Send cursor position updates
- `collab:update-nickname` - Update user nickname
- `collab:get-room-info` - Get room information

### Frontend Components

#### CollaborationService (`frontend/src/services/collaboration.js`)
- Socket.IO client for real-time communication
- Event handling and state management
- Throttled cursor updates for performance

#### CollaborationPanel (`frontend/src/components/Collaboration/CollaborationPanel.jsx`)
- UI for managing collaboration sessions
- Shows active collaborators
- Nickname editing interface
- Join/leave collaboration controls

#### RemoteCursors (`frontend/src/components/Collaboration/RemoteCursors.jsx`)
- Renders remote user cursors in Monaco Editor
- Shows user selections with color coding
- Displays user labels with nicknames

#### Enhanced MonacoEditor (`frontend/src/components/Editor/MonacoEditor.jsx`)
- Integrated collaboration features
- Real-time change synchronization
- Cursor position tracking
- Remote cursor rendering

## 🚀 Usage

### Starting Collaboration

1. **Open a file** in the editor
2. **Click the collaboration button** (Users icon) in the top-right corner
3. **Click "Start Collaboration"** to create/join a room
4. **Share the project** with others to invite them

### Collaboration Features

#### Document Editing
- Changes are synchronized in real-time
- All users see updates immediately
- No conflicts - last change wins (simple approach)

#### Cursor Tracking
- See other users' cursor positions
- View selections with color coding
- User labels show nicknames

#### User Management
- Set custom nicknames for identification
- See when users join/leave
- View collaboration history

### Room Management

Rooms are automatically created based on:
```
Room ID = {projectId}:{filePath}
```

Examples:
- `project1:/src/index.js`
- `my-app:/components/Header.jsx`

## 🔧 Technical Implementation

### Session Management

```javascript
// User session structure
{
  userId: "uuid-v4",
  socketId: "socket-connection-id",
  nickname: "User 1",
  roomId: "project1:/test.js",
  lastActivity: Date,
  cursor: { line: 0, column: 0 },
  selection: null,
  color: "#FF6B6B"
}
```

### Room Structure

```javascript
// Room structure
{
  users: Map<userId, userInfo>,
  document: "current file content",
  cursors: Map<userId, cursorInfo>,
  lastActivity: Date,
  projectId: "project1",
  filePath: "/test.js"
}
```

### Event Flow

1. **User joins room**:
   ```
   Client -> collab:join -> Server
   Server -> collab:joined -> Client
   Server -> collab:user-joined -> Other Clients
   ```

2. **Document change**:
   ```
   Client -> collab:document-change -> Server
   Server -> collab:document-changed -> Other Clients
   ```

3. **Cursor update**:
   ```
   Client -> collab:cursor-change -> Server
   Server -> collab:cursor-changed -> Other Clients
   ```

## 🎨 UI Components

### Collaboration Panel
- **Location**: Right side of editor
- **Toggle**: Users icon button
- **Features**:
  - Active collaborators list
  - Nickname editing
  - Session information
  - Join/leave controls

### Remote Cursors
- **Rendering**: Monaco Editor decorations
- **Features**:
  - Colored cursor lines
  - Selection highlighting
  - User labels with nicknames
  - Smooth animations

## 🔒 Security & Privacy

### Session-Based Approach
- No authentication required
- Temporary user IDs (UUID v4)
- Sessions expire when disconnected
- No persistent user data

### Room Isolation
- Users can only access rooms they join
- Room data is temporary (in-memory)
- Automatic cleanup of empty rooms
- No cross-room data leakage

## ⚡ Performance Optimizations

### Throttling
- Cursor updates throttled to 100ms
- Prevents excessive network traffic
- Maintains smooth user experience

### Memory Management
- Automatic cleanup of inactive rooms (30 min)
- Session cleanup on disconnect
- Limited buffer sizes for document history

### Network Efficiency
- Only send changes, not full documents
- Compressed cursor position data
- Efficient Socket.IO event handling

## 🧪 Testing

### Backend Tests
```bash
cd container-manager
node test/test-collaboration.js
```

### Test Coverage
- ✅ Room creation and management
- ✅ User join/leave operations
- ✅ Document synchronization
- ✅ Cursor position tracking
- ✅ Nickname updates
- ✅ Error handling
- ✅ Memory cleanup

## 🔧 Configuration

### Environment Variables
```bash
# Socket.IO configuration (already configured)
SOCKET_IO_CORS_ORIGIN=*
SOCKET_IO_TRANSPORTS=websocket,polling
```

### Customization Options

#### Cursor Update Frequency
```javascript
// In collaboration.js
this.cursorUpdateDelay = 100; // milliseconds
```

#### Room Cleanup Interval
```javascript
// In collaboration.service.js
this.cleanupInterval = 60000; // 1 minute
```

#### User Colors
```javascript
// In collaboration.service.js
const colors = [
  '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
  '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'
];
```

## 🚀 Future Enhancements

### Planned Features
- [ ] Operational Transform for conflict resolution
- [ ] Voice/video chat integration
- [ ] File-level permissions
- [ ] Collaboration analytics
- [ ] Persistent collaboration history
- [ ] Mobile collaboration support

### Advanced Features
- [ ] Code review integration
- [ ] Collaborative debugging
- [ ] Shared terminal sessions
- [ ] Project-wide collaboration
- [ ] Integration with version control

## 🐛 Troubleshooting

### Common Issues

#### Connection Problems
- Check if Socket.IO server is running
- Verify CORS configuration
- Check network connectivity

#### Sync Issues
- Refresh the page to reconnect
- Check browser console for errors
- Verify WebSocket support

#### Performance Issues
- Reduce cursor update frequency
- Check for memory leaks
- Monitor network traffic

### Debug Mode
Enable debug logging:
```javascript
// In collaboration.service.js
logger.level = 'debug';
```

## 📚 API Reference

### Backend Events

#### `collab:join`
Join a collaboration room
```javascript
socket.emit('collab:join', {
  projectId: 'project1',
  filePath: '/src/index.js',
  nickname: 'Alice' // optional
});
```

#### `collab:document-change`
Send document changes
```javascript
socket.emit('collab:document-change', {
  content: 'new file content',
  changes: [] // optional change operations
});
```

#### `collab:cursor-change`
Send cursor position
```javascript
socket.emit('collab:cursor-change', {
  cursor: { line: 0, column: 10 },
  selection: { // optional
    startLine: 0, startColumn: 0,
    endLine: 0, endColumn: 10
  }
});
```

### Frontend API

#### Join Room
```javascript
await collaborationService.joinRoom('project1', '/test.js', 'Alice');
```

#### Send Changes
```javascript
collaborationService.sendDocumentChange(content);
```

#### Update Cursor
```javascript
collaborationService.sendCursorUpdate(cursor, selection);
```

---

*This feature enhances the Cloud IDE with modern real-time collaboration capabilities while maintaining simplicity and performance.*
