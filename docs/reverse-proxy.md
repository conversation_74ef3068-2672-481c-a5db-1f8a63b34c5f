# Reverse Proxy Service

The Reverse Proxy service provides dynamic HTTP routing and load balancing for the Cloud IDE project, automatically configuring Nginx to route subdomain requests to appropriate container instances.

## 🎯 Overview

**Port**: 80 (HTTP), 443 (HTTPS)  
**Technology**: Nginx, Node.js Config Manager  
**Purpose**: Dynamic routing and load balancing for containerized applications

## 🏗️ Architecture

### Core Components

```
Reverse Proxy
├── Nginx (Port 80/443)
│   ├── Main Configuration
│   ├── Dynamic Virtual Hosts
│   ├── SSL Termination
│   └── Health Checks
├── Config Manager (Node.js)
│   ├── Container Watcher
│   ├── Nginx Manager
│   ├── DNS Integration
│   └── Event Handler
└── Configuration Files
    ├── nginx.conf (Main)
    ├── conf.d/ (Generated)
    └── templates/ (Templates)
```

### Service Dependencies
- **Container Manager**: Container lifecycle events
- **DNS Server**: Subdomain registration
- **Docker Engine**: Container monitoring
- **Shared Services**: Event bus and configuration

## 📁 Directory Structure

```
reverse-proxy/
├── nginx/                   # Nginx configuration
│   ├── nginx.conf          # Main Nginx configuration
│   ├── conf.d/             # Generated virtual host configs
│   ├── manual-templates/   # Configuration templates
│   │   └── upstream.conf.template
│   ├── reload-watcher.sh   # Nginx reload script
│   └── Dockerfile         # Nginx container
├── config-manager/         # Dynamic configuration manager
│   ├── src/
│   │   ├── index.js       # Main application
│   │   ├── container-watcher.js  # Docker event monitoring
│   │   ├── nginx-manager.js      # Nginx configuration
│   │   └── logger.js      # Logging configuration
│   ├── Dockerfile         # Config manager container
│   └── package.json       # Dependencies
├── test-app/              # Test application
├── logs/                  # Log files
└── docker-compose.yml     # Service orchestration
```

## 🔧 Nginx Configuration

### Main Configuration (`nginx.conf`)

```nginx
user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

events {
    worker_connections 1024;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;

    sendfile on;
    keepalive_timeout 65;

    # Include all generated virtual hosts
    include /etc/nginx/conf.d/*.conf;

    # Default server block
    server {
        listen 80 default_server;
        server_name _;
        
        location /health {
            access_log off;
            return 200 'healthy\n';
            add_header Content-Type text/plain;
        }
        
        location / {
            return 404 'Not Found\n';
            add_header Content-Type text/plain;
        }
    }
}
```

### Virtual Host Template (`upstream.conf.template`)

```nginx
upstream {{subdomain}} {
    server {{container_ip}}:{{port}};
}

server {
    listen 80;
    server_name {{subdomain}}.ide.local;

    location / {
        proxy_pass http://{{subdomain}};
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket support
        proxy_read_timeout 86400;
        proxy_send_timeout 86400;
        
        # Buffer settings
        proxy_buffering off;
        proxy_request_buffering off;
    }

    location /health {
        access_log off;
        return 200 'healthy\n';
        add_header Content-Type text/plain;
    }
}
```

## 🔄 Dynamic Configuration Management

### Container Watcher

The Container Watcher monitors Docker events and automatically updates Nginx configuration:

#### Event Handling

```javascript
// Container start event
async handleContainerStart(containerInfo) {
  const subdomain = containerInfo.Config.Labels["ide.subdomain"];
  if (!subdomain) return;

  const networkName = Object.keys(containerInfo.NetworkSettings.Networks)
    .find(name => name.includes("ide-network")) || 
    Object.keys(containerInfo.NetworkSettings.Networks)[0];

  const ipAddress = containerInfo.NetworkSettings.Networks[networkName]?.IPAddress;
  const port = containerInfo.Config.Labels["ide.port"] || "3000";

  // Update Nginx configuration
  await this.nginxManager.updateConfiguration(subdomain, ipAddress, port);
}

// Container stop event
async handleContainerStop(containerInfo) {
  const subdomain = containerInfo.Config.Labels["ide.subdomain"];
  if (!subdomain) return;

  // Remove Nginx configuration
  await fs.unlink(`/etc/nginx/conf.d/${subdomain}.conf`);
  await this.nginxManager.reloadNginx();
  
  // Mark DNS record as inactive
  await this.nginxManager.updateDnsRecord(subdomain, "0.0.0.0", true);
}
```

### Nginx Manager

The Nginx Manager handles configuration generation and reloading:

#### Configuration Update Process

1. **Template Processing**: Load template and replace placeholders
2. **Configuration Generation**: Create virtual host configuration file
3. **Nginx Reload**: Trigger Nginx configuration reload
4. **DNS Update**: Update DNS record for the subdomain
5. **Event Publishing**: Publish configuration update events

```javascript
async updateConfiguration(subdomain, containerIp, port = 3000) {
  // Load template
  const template = await fs.readFile(this.templatePath, "utf8");
  
  // Replace placeholders
  const config = template
    .replace(/{{subdomain}}/g, subdomain)
    .replace(/{{container_ip}}/g, containerIp)
    .replace(/{{port}}/g, port);

  // Write configuration file
  const configFile = path.join(this.configPath, `${subdomain}.conf`);
  await fs.writeFile(configFile, config);

  // Reload Nginx
  await this.reloadNginx();

  // Update DNS record
  await this.updateDnsRecord(subdomain, containerIp);
}
```

## 🔄 Nginx Reload Mechanism

### Reload Watcher Script

```bash
#!/bin/sh
# reload-watcher.sh

echo "Starting Nginx reload watcher..."

while true; do
    if [ -f /etc/nginx/conf.d/../trigger-reload ]; then
        echo "Reload trigger detected, reloading Nginx..."
        nginx -s reload
        rm -f /etc/nginx/conf.d/../trigger-reload
        echo "Nginx reloaded successfully"
    fi
    sleep 1
done
```

### Reload Process

1. **Trigger Creation**: Config manager creates trigger file
2. **Watcher Detection**: Reload watcher detects trigger file
3. **Nginx Reload**: Execute `nginx -s reload`
4. **Cleanup**: Remove trigger file
5. **Event Publishing**: Publish reload completion event

## 🌐 Routing Examples

### Project Access Patterns

| Project | Subdomain | Container IP | Nginx Route |
|---------|-----------|--------------|-------------|
| my-react-app | myreactapp | ********** | myreactapp.ide.local → **********:3000 |
| python-api | pythonapi | ********** | pythonapi.ide.local → **********:8000 |
| java-service | javaservice | ********** | javaservice.ide.local → **********:8080 |

### Generated Configuration Example

```nginx
# Generated for project: my-react-app
upstream myreactapp {
    server **********:3000;
}

server {
    listen 80;
    server_name myreactapp.ide.local;

    location / {
        proxy_pass http://myreactapp;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /health {
        access_log off;
        return 200 'healthy\n';
    }
}
```

## 📊 Event Integration

### Subscribed Events

The Config Manager subscribes to container lifecycle events:

```javascript
// Container created
eventBus.subscribe(EventTypes.CONTAINER_CREATED, async (event) => {
  logger.info(`Container created: ${event.data.name}`);
});

// Container started
eventBus.subscribe(EventTypes.CONTAINER_STARTED, async (event) => {
  logger.info(`Container started: ${event.data.name}`);
  // Configuration is handled by Docker event watcher
});

// Container stopped
eventBus.subscribe(EventTypes.CONTAINER_STOPPED, async (event) => {
  logger.info(`Container stopped: ${event.data.name}`);
  // Configuration cleanup handled by Docker event watcher
});
```

### Published Events

```javascript
// Proxy configuration updated
await eventBus.publish(EventTypes.PROXY_CONFIG_UPDATED, {
  subdomain,
  containerIp,
  port,
  configFile
});

// Proxy reload requested
await eventBus.publish(EventTypes.PROXY_RELOAD_REQUESTED, {
  timestamp: new Date().toISOString(),
  method: 'trigger_file'
});

// Proxy reload completed
await eventBus.publish(EventTypes.PROXY_RELOAD_COMPLETED, {
  timestamp: new Date().toISOString(),
  success: true
});
```

## 🔧 Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `DNS_API_URL` | DNS server API URL | `http://dns-server:8053` |
| `DNS_DOMAIN` | Base domain for routing | `ide.local` |
| `DNS_TTL` | DNS record TTL | `3600` |
| `DNS_PERSISTENT` | Persistent DNS records | `true` |
| `CONTAINER_MANAGER_URL` | Container manager URL | `http://container-manager:3000` |
| `LOG_LEVEL` | Logging level | `info` |

### Docker Labels

Containers must include specific labels for automatic routing:

```yaml
labels:
  - "ide.subdomain=myproject"    # Required: subdomain name
  - "ide.enabled=true"           # Required: enable routing
  - "ide.port=3000"             # Optional: container port (default: 3000)
```

## 🔍 Health Monitoring

### Health Check Endpoints

#### Nginx Health
```http
GET /health
```

**Response:**
```
healthy
```

#### Config Manager Health
The config manager doesn't expose a direct health endpoint but publishes health events through the event bus.

### Monitoring Metrics

- **Active Configurations**: Number of active virtual hosts
- **Reload Frequency**: Nginx reload frequency
- **Configuration Errors**: Failed configuration updates
- **DNS Sync Status**: DNS record synchronization status

## 🚨 Error Handling

### Configuration Errors

- **Template Errors**: Invalid template syntax handling
- **File System Errors**: Permission and disk space issues
- **Nginx Errors**: Configuration validation and reload failures

### Recovery Mechanisms

- **Automatic Retry**: Failed operations automatically retried
- **Rollback**: Revert to previous working configuration
- **Fallback**: Default configuration for critical failures
- **Event Publishing**: Error events for monitoring

### Error Examples

```javascript
// Configuration update failure
await eventBus.publish(EventTypes.PROXY_RELOAD_FAILED, {
  subdomain,
  containerIp,
  port,
  reason: 'configuration_update_failed'
});

// DNS sync failure
await eventBus.publish(EventTypes.DNS_SYNC_FAILED, {
  subdomain,
  error: error.message
});
```

## 🔒 Security

### Nginx Security

- **Security Headers**: Standard security headers added
- **Rate Limiting**: Protection against abuse
- **Access Control**: IP-based access restrictions (configurable)
- **SSL/TLS**: HTTPS support for production

### Container Security

- **Network Isolation**: Containers communicate through dedicated networks
- **Non-root Execution**: Config manager runs as non-root user
- **Read-only Mounts**: Template directory mounted read-only

### Configuration Security

- **Template Validation**: Validate templates before processing
- **Path Sanitization**: Prevent directory traversal attacks
- **Input Validation**: Validate all configuration inputs

## 🧪 Testing

### Test Coverage

- **Configuration Generation**: Template processing and file generation
- **Nginx Integration**: Configuration validation and reload testing
- **Docker Integration**: Container event handling
- **DNS Integration**: DNS record management

### Running Tests

```bash
# Run integration tests
node test-docker-integration.js

# Test Nginx configuration
nginx -t

# Test container routing
curl -H "Host: myproject.ide.local" http://localhost/
```

## 📝 Logging

### Log Categories

- **Container Events**: Docker container lifecycle events
- **Configuration Changes**: Nginx configuration updates
- **Reload Operations**: Nginx reload attempts and results
- **DNS Operations**: DNS record management

### Log Format

```json
{
  "timestamp": "2025-06-18T10:30:00.000Z",
  "level": "info",
  "message": "NGINX config updated for myproject",
  "subdomain": "myproject",
  "containerIp": "**********",
  "port": 3000,
  "service": "reverse-proxy"
}
```

This comprehensive documentation covers all aspects of the Reverse Proxy service, from Nginx configuration to dynamic routing and security considerations.
