# Development Guide

Comprehensive guide for developers working on the Cloud IDE project, covering setup, architecture, coding standards, and contribution guidelines.

## 🎯 Development Overview

The Cloud IDE project is built with modern technologies and follows microservices architecture principles. This guide will help you understand the codebase, set up your development environment, and contribute effectively.

## 🛠️ Development Environment Setup

### Prerequisites

- **Node.js**: 18+ with npm
- **Docker**: 20.10+ with Docker Compose
- **Git**: Latest version
- **Code Editor**: VS Code recommended with extensions

### Recommended VS Code Extensions

```json
{
  "recommendations": [
    "ms-vscode.vscode-json",
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "ms-vscode.vscode-eslint",
    "ms-azuretools.vscode-docker",
    "ms-vscode.vscode-typescript-next"
  ]
}
```

### Initial Setup

1. **Clone Repository**
   ```bash
   git clone <repository-url>
   cd cloud-ide-project
   ```

2. **Install Dependencies**
   ```bash
   # Root dependencies
   npm install
   
   # Frontend dependencies
   cd frontend && npm install && cd ..
   
   # Container Manager dependencies
   cd container-manager && npm install && cd ..
   
   # DNS Server dependencies
   cd dns-server && npm install && cd ..
   
   # Shared services dependencies
   cd shared && npm install && cd ..
   ```

3. **Build Docker Images**
   ```bash
   # Windows
   build-docker-images.bat
   
   # Linux/macOS
   chmod +x build-docker-images.sh
   ./build-docker-images.sh
   ```

4. **Start Development Services**
   ```bash
   # Start backend services
   docker-compose -f docker-compose.simplified.yml up -d
   
   # Start frontend development server
   cd frontend && npm run dev
   ```

5. **Verify Setup**
   ```bash
   node test-comprehensive.js --quick
   ```

## 🏗️ Project Architecture

### Directory Structure

```
cloud-ide-project/
├── container-manager/       # Core container management service
│   ├── src/                # Source code
│   ├── test/               # Test files
│   ├── data/               # Persistent data
│   └── Dockerfile          # Container configuration
├── dns-server/             # Custom DNS server
│   ├── server/             # DNS server implementation
│   ├── api/                # HTTP API
│   ├── lib/                # DNS utilities
│   └── config/             # Configuration files
├── reverse-proxy/          # Nginx reverse proxy
│   ├── nginx/              # Nginx configuration
│   └── config-manager/     # Dynamic configuration
├── frontend/               # React web application
│   ├── src/                # Source code
│   ├── public/             # Static assets
│   └── dist/               # Build output
├── shared/                 # Common utilities
│   ├── config/             # Configuration management
│   ├── events/             # Event bus system
│   ├── integration/        # Service integration
│   └── utils/              # Utility functions
├── docker-images/          # Language-specific images
├── docs/                   # Documentation
└── tests/                  # Integration tests
```

### Service Communication

```mermaid
graph TD
    A[Frontend] --> B[Container Manager]
    B --> C[Docker Engine]
    B --> D[DNS Server]
    B --> E[Shared Services]
    D --> F[Redis]
    G[Config Manager] --> H[Nginx]
    G --> D
    G --> B
    E --> I[Event Bus]
```

## 💻 Development Workflow

### Feature Development

1. **Create Feature Branch**
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **Make Changes**
   - Follow coding standards
   - Write tests for new functionality
   - Update documentation

3. **Test Changes**
   ```bash
   # Run unit tests
   npm test
   
   # Run integration tests
   node test-comprehensive.js
   
   # Test specific service
   cd container-manager && npm test
   ```

4. **Commit Changes**
   ```bash
   git add .
   git commit -m "feat: add new feature description"
   ```

5. **Push and Create PR**
   ```bash
   git push origin feature/your-feature-name
   # Create pull request on GitHub
   ```

### Hot Reloading

#### Backend Services
- **Container Manager**: Uses nodemon for automatic restart
- **DNS Server**: File watching with automatic reload
- **Config Manager**: Watches for configuration changes

#### Frontend
- **Vite Dev Server**: Hot module replacement (HMR)
- **Tailwind CSS**: JIT compilation
- **React**: Fast refresh

### Debugging

#### Backend Debugging
```bash
# Enable debug logging
export LOG_LEVEL=debug

# Debug specific service
cd container-manager
npm run debug

# Docker logs
docker-compose logs -f container-manager
```

#### Frontend Debugging
```bash
# Start with source maps
npm run dev

# Browser DevTools
# React DevTools extension
# Network tab for API calls
```

## 📝 Coding Standards

### JavaScript/Node.js

#### Code Style
```javascript
// Use modern ES6+ syntax
const { configManager } = require('./shared');

// Async/await preferred over promises
async function createContainer(config) {
  try {
    const result = await dockerService.createContainer(config);
    return result;
  } catch (error) {
    logger.error('Failed to create container:', error);
    throw error;
  }
}

// Use destructuring
const { name, image, ports } = containerConfig;

// Use template literals
const message = `Container ${name} created successfully`;
```

#### Error Handling
```javascript
// Use try-catch for async operations
try {
  const result = await operation();
  return result;
} catch (error) {
  // Log error with context
  logger.error('Operation failed:', {
    operation: 'createContainer',
    error: error.message,
    context: { containerId }
  });
  
  // Re-throw or handle appropriately
  throw new Error(`Failed to create container: ${error.message}`);
}

// Use error handler for complex operations
const result = await errorHandler.executeWithProtection('operation.name', 
  async () => {
    return await complexOperation();
  },
  {
    component: 'service-name',
    operation: 'operation-name',
    retry: { maxRetries: 3 }
  }
);
```

### React/Frontend

#### Component Structure
```jsx
// Use functional components with hooks
import React, { useState, useEffect } from 'react';
import { useAppStore } from '../stores/appStore';

const ProjectCard = ({ project, onStart, onDelete }) => {
  const [loading, setLoading] = useState(false);
  const { addNotification } = useAppStore();

  const handleStart = async () => {
    setLoading(true);
    try {
      await onStart(project);
      addNotification({
        type: 'success',
        message: `Project ${project.name} started successfully`
      });
    } catch (error) {
      addNotification({
        type: 'error',
        message: `Failed to start project: ${error.message}`
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="project-card">
      {/* Component JSX */}
    </div>
  );
};

export default ProjectCard;
```

#### State Management
```javascript
// Use Zustand for state management
export const useAppStore = create(
  persist(
    (set, get) => ({
      // State
      projects: [],
      currentProject: null,
      
      // Actions
      setProjects: (projects) => set({ projects }),
      setCurrentProject: (project) => set({ currentProject: project }),
      
      // Computed values
      getProjectById: (id) => {
        const { projects } = get();
        return projects.find(p => p.id === id);
      }
    }),
    {
      name: 'app-store',
      partialize: (state) => ({
        // Only persist certain state
        theme: state.theme,
        settings: state.settings
      })
    }
  )
);
```

### CSS/Styling

#### Tailwind CSS Classes
```jsx
// Use semantic class combinations
<div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
  <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
    Project Title
  </h2>
  <button className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md transition-colors">
    Start Project
  </button>
</div>

// Use responsive design
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
  {/* Grid items */}
</div>
```

## 🧪 Testing Guidelines

### Unit Testing

#### Backend Tests
```javascript
// Use Jest for testing
describe('ProjectService', () => {
  let projectService;
  
  beforeEach(() => {
    projectService = new ProjectService(mockDocker);
  });
  
  test('should create project successfully', async () => {
    const projectConfig = {
      name: 'Test Project',
      template: 'react'
    };
    
    const result = await projectService.createProject(projectConfig);
    
    expect(result).toBeDefined();
    expect(result.name).toBe('Test Project');
    expect(result.id).toBeTruthy();
  });
  
  test('should handle creation errors', async () => {
    const invalidConfig = {};
    
    await expect(projectService.createProject(invalidConfig))
      .rejects.toThrow('Project name is required');
  });
});
```

#### Frontend Tests
```javascript
// Use React Testing Library
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import ProjectCard from '../components/ProjectCard';

describe('ProjectCard', () => {
  const mockProject = {
    id: 'project-1',
    name: 'Test Project',
    status: 'stopped'
  };
  
  test('renders project information', () => {
    render(
      <BrowserRouter>
        <ProjectCard project={mockProject} />
      </BrowserRouter>
    );
    
    expect(screen.getByText('Test Project')).toBeInTheDocument();
    expect(screen.getByText('stopped')).toBeInTheDocument();
  });
  
  test('handles start button click', async () => {
    const mockOnStart = jest.fn();
    
    render(
      <BrowserRouter>
        <ProjectCard project={mockProject} onStart={mockOnStart} />
      </BrowserRouter>
    );
    
    fireEvent.click(screen.getByText('Start'));
    
    await waitFor(() => {
      expect(mockOnStart).toHaveBeenCalledWith(mockProject);
    });
  });
});
```

### Integration Testing

```javascript
// Test service integration
describe('Container Integration', () => {
  test('should create container and update DNS', async () => {
    // Create container
    const container = await containerService.createContainer(config);
    
    // Verify DNS record created
    const dnsRecords = await dnsService.getSubdomains();
    expect(dnsRecords).toContainEqual(
      expect.objectContaining({
        domain: `${config.subdomain}.ide.local`
      })
    );
    
    // Cleanup
    await containerService.removeContainer(container.id);
  });
});
```

## 🔧 Adding New Features

### Adding a New Language Support

1. **Create Docker Image**
   ```dockerfile
   # docker-images/Dockerfile.newlang
   FROM newlang:latest
   
   RUN apk add --no-cache bash git curl
   
   WORKDIR /workspace
   
   RUN addgroup -g 1000 developer && \
       adduser -D -s /bin/bash -u 1000 -G developer developer
   
   USER developer
   
   CMD ["tail", "-f", "/dev/null"]
   ```

2. **Update Build Script**
   ```bash
   # Add to build-docker-images.sh
   echo "Building New Language image..."
   docker build -f Dockerfile.newlang -t cloud-ide/newlang:latest .
   ```

3. **Update Docker Service**
   ```javascript
   // container-manager/src/services/docker.service.js
   getImageForProject(project) {
     const imageMap = {
       // ... existing mappings
       newlang: 'cloud-ide/newlang:latest'
     };
     // ...
   }
   
   getRunCommand(language, projectType, fileName) {
     const commands = {
       // ... existing commands
       newlang: {
         general: fileName ? `newlang ${fileName}` : 'newlang main.ext'
       }
     };
     // ...
   }
   ```

4. **Update Project Templates**
   ```javascript
   // container-manager/src/routes/project.routes.js
   const templates = [
     // ... existing templates
     {
       id: 'newlang',
       name: 'New Language Project',
       description: 'New language project template',
       language: 'newlang',
       icon: '🔥',
       features: ['main.ext', 'package.config']
     }
   ];
   ```

### Adding a New API Endpoint

1. **Define Route**
   ```javascript
   // container-manager/src/routes/new.routes.js
   const express = require('express');
   const Joi = require('joi');
   
   const schema = Joi.object({
     // Define validation schema
   });
   
   function setupNewRoutes(app, dependencies) {
     app.post('/api/new-endpoint', async (req, res) => {
       try {
         const { error, value } = schema.validate(req.body);
         if (error) {
           return res.status(400).json({ error: error.details[0].message });
         }
         
         // Implementation
         const result = await service.performOperation(value);
         res.json(result);
       } catch (error) {
         res.status(500).json({ error: error.message });
       }
     });
   }
   
   module.exports = { setupNewRoutes };
   ```

2. **Register Route**
   ```javascript
   // container-manager/src/index.js
   const { setupNewRoutes } = require('./routes/new.routes');
   
   // Register routes
   setupNewRoutes(app, dependencies);
   ```

3. **Add Frontend Integration**
   ```javascript
   // frontend/src/services/api.js
   export const newService = {
     performOperation: (data) => containerAPI.post('/new-endpoint', data)
   };
   ```

4. **Write Tests**
   ```javascript
   // container-manager/test/new.test.js
   describe('New Endpoint', () => {
     test('should handle valid requests', async () => {
       const response = await request(app)
         .post('/api/new-endpoint')
         .send(validData)
         .expect(200);
       
       expect(response.body).toBeDefined();
     });
   });
   ```

## 🔍 Debugging Tips

### Common Issues

1. **Container Creation Fails**
   - Check Docker image availability
   - Verify port conflicts
   - Check resource limits

2. **DNS Resolution Issues**
   - Verify Redis connection
   - Check DNS server logs
   - Test with `nslookup`

3. **Frontend API Errors**
   - Check proxy configuration
   - Verify backend service status
   - Check CORS settings

### Debugging Tools

```bash
# Docker debugging
docker ps -a
docker logs container-name
docker exec -it container-name /bin/bash

# Service debugging
curl -v http://localhost:3000/health
curl -v http://localhost:8053/health

# Network debugging
docker network ls
docker network inspect cloud-ide-network
```

## 📚 Resources

### Documentation
- [Docker Documentation](https://docs.docker.com/)
- [React Documentation](https://reactjs.org/docs/)
- [Node.js Documentation](https://nodejs.org/docs/)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)

### Tools
- [Postman](https://www.postman.com/) - API testing
- [Docker Desktop](https://www.docker.com/products/docker-desktop) - Container management
- [Redis CLI](https://redis.io/topics/rediscli) - Redis debugging

This development guide provides comprehensive information for contributing to the Cloud IDE project effectively.
