const { DEFAULT_TTL } = require("./types");
const { redisHelpers } = require("../config/redis-config");

const dynamicSubdomains = new Map();

async function loadDynamicSubdomains() {
  try {
    const records = await redisHelpers.getAllRecords();
    for (const [domain, record] of Object.entries(records)) {
      dynamicSubdomains.set(domain, {
        ipAddress: record.ipAddress || record.value,
        expires: record.expires,
        isPersistent: record.isPersistent
      });
    }
    console.log('Loaded dynamic subdomains from Redis:', Array.from(dynamicSubdomains.keys()));
  } catch (error) {
    console.error('Error loading dynamic subdomains:', error);
  }
}

async function addDynamicSubdomain(sub, domain, ip, ttl = DEFAULT_TTL, isPersistent = false) {
  const full = `${sub}.${domain}`;
  const expires = isPersistent ? null : Date.now() + ttl * 1000;
  
  // Store in memory
  dynamicSubdomains.set(full, {
    ipAddress: ip,
    expires: expires,
    isPersistent: isPersistent
  });

  // Store in Redis with consistent format
  await redisHelpers.setRecord(full, {
    ipAddress: ip,
    expires: expires,
    isPersistent: isPersistent,
    ttl: isPersistent ? null : ttl
  });

  console.log(`Added dynamic subdomain: ${full} -> ${ip}`);
  return full;
}

async function removeDynamicSubdomain(sub, domain, type = 'all') {
  const full = `${sub}.${domain}`;
  const record = dynamicSubdomains.get(full);
  
  if (record) {
    if (record.isPersistent) {
      // For persistent records, mark as inactive instead of deleting
      await addDynamicSubdomain(sub, domain, "0.0.0.0", DEFAULT_TTL, true);
    } else {
      // For non-persistent records, delete completely
      dynamicSubdomains.delete(full);
      await redisHelpers.deleteRecord(full);
    }
  }
}

async function cleanupExpiredSubdomains() {
  const now = Date.now();
  for (const [key, val] of dynamicSubdomains.entries()) {
    if (!val.isPersistent && now > val.expires) {
      dynamicSubdomains.delete(key);
      // Remove from Redis
      await redisHelpers.deleteRecord(key, 'temp');
    }
  }
}

// Initialize by loading subdomains from Redis
loadDynamicSubdomains();

module.exports = {
  dynamicSubdomains,
  addDynamicSubdomain,
  removeDynamicSubdomain,
  cleanupExpiredSubdomains,
  loadDynamicSubdomains
};
