const { QR_MASK, AA_MASK, RD_MASK, RA_MASK, CLASS_IN, RCODE_NOERROR, RCODE_NXDOMAIN } = require("./types");
const { writeDomainName, writeAnswer } = require("./dns-writer");

// `buffer is a raw binary packet sent over UDP (comes from the `dgram` module)
// `offset`Is a pointer — tells us where we are in the buffer while reading bytes

function parseDomainName(buffer, offset) {
  const labels = [];
  let jumped = false;
  let jumpOffset = offset;
  let pointer = offset;

  while (true) {
    const length = buffer.readUInt8(pointer);
    if (length === 0) {
      // checking end of the domain name

      pointer += 1;
      break;
    }

    if ((length & 0xc0) === 0xc0) {
      // pointer to another name (compression)

      if (!jumped) jumpOffset = pointer + 2;
      pointer = ((length & 0x3f) << 8) | buffer.readUInt8(pointer + 1);
      jumped = true;
    } else {
      // normal label
      pointer += 1;
      labels.push(buffer.slice(pointer, pointer + length).toString("utf8"));
      pointer += length;
    }
  }

  return [labels.join("."), jumped ? jumpOffset : pointer];
}

//Takes The raw UDP packet and decodes it into a usable JS object
function parseQuery(buffer) {
  let offset = 0;
  const header = {
    id: buffer.readUInt16BE(offset),
    flags: buffer.readUInt16BE(offset + 2),
    qdcount: buffer.readUInt16BE(offset + 4),
  };
  offset += 12;
  // finish reading the header "12 bytes"

  const questions = [];
  for (let i = 0; i < header.qdcount; i++) {
    const [name, newOffset] = parseDomainName(buffer, offset);
    offset = newOffset;
    const type = buffer.readUInt16BE(offset);
    offset += 2;
    const cls = buffer.readUInt16BE(offset);
    offset += 2;
    questions.push({ name, type, class: cls });
  }

  return { header, questions };
}

function createResponse(query, answers) {
  const buffer = Buffer.alloc(4096);
  let offset = 0;

  // Write ID
  buffer.writeUInt16BE(query.header.id, 0);
  
  // Set response flags
  const flags = QR_MASK | AA_MASK | (query.header.flags & RD_MASK) | RA_MASK;
  const rcode = answers.length === 0 ? RCODE_NXDOMAIN : RCODE_NOERROR;
  
  // Combine flags and response code
  const responseFlags = flags | rcode;
  buffer.writeUInt16BE(responseFlags, 2);

  // Write counts
  buffer.writeUInt16BE(1, 4);  // QDCount
  buffer.writeUInt16BE(answers.length, 6);  // ANCount
  buffer.writeUInt16BE(0, 8);  // NSCount
  buffer.writeUInt16BE(0, 10); // ARCount
  offset = 12;

  // Write question
  const question = query.questions[0];
  offset = writeDomainName(buffer, offset, question.name);
  buffer.writeUInt16BE(question.type, offset);
  offset += 2;
  buffer.writeUInt16BE(question.class, offset);
  offset += 2;

  // Write answers if any
  for (const answer of answers) {
    offset = writeAnswer(buffer, offset, question.name, answer);
  }

  return buffer.slice(0, offset);
}

module.exports = {
  parseQuery,
  createResponse,
};
