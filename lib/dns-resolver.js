const { getRecords, cache } = require("./record-manager");
const {
  TYPE_A,
  TYPE_AAAA,
  TYPE_CNAME,
  TYPE_NS,
  TYPE_MX,
  TYPE_TXT,
  CLASS_IN,
  DEFAULT_TTL,
} = require("./types");
const { dynamicSubdomains } = require("./dynamic-records");

function getRecordsForDomain(domain, type) {
  const answers = [];
  const domainLower = domain.toLowerCase();

  // First check dynamic subdomains
  if (dynamicSubdomains.has(domainLower)) {
    const data = dynamicSubdomains.get(domainLower);
    if (!data.expires || Date.now() < data.expires) {
      if (type === TYPE_A || type === 0) {
        answers.push({
          type: TYPE_A,
          class: CLASS_IN,
          ttl: data.expires ? Math.floor((data.expires - Date.now()) / 1000) : DEFAULT_TTL,
          data: data.ipAddress
        });
        console.log(`Found dynamic record for ${domainLower}:`, data);
        return answers;
      }
    } else {
      console.log(`Removing expired record for ${domainLower}`);
      dynamicSubdomains.delete(domainLower);
    }
  }

  // Then check static records
  const records = getRecords();
  if (!records || !records.domains) {
    console.log(`No records found for domain ${domainLower} (type ${type})`);
    return [];
  }

  const match = records.domains[domainLower] || 
                records.domains[`*.${domainLower.split(".").slice(1).join(".")}`];

  if (match) {
    if (type === 0 || type === TYPE_A) {
      match.A?.forEach(ip => answers.push({
        type: TYPE_A,
        class: CLASS_IN,
        ttl: DEFAULT_TTL,
        data: ip
      }));
    }
    if (type === 0 || type === TYPE_AAAA) {
      match.AAAA?.forEach(ip => answers.push({
        type: TYPE_AAAA,
        class: CLASS_IN,
        ttl: DEFAULT_TTL,
        data: ip
      }));
    }
    if (type === 0 || type === TYPE_CNAME) {
      match.CNAME &&
        answers.push({
          type: TYPE_CNAME,
          class: CLASS_IN,
          ttl: DEFAULT_TTL,
          data: match.CNAME,
        });
    }
    if (type === 0 || type === TYPE_NS) {
      match.NS?.forEach(ns => answers.push({
        type: TYPE_NS,
        class: CLASS_IN,
        ttl: DEFAULT_TTL,
        data: ns
      }));
    }
    if (type === 0 || type === TYPE_MX) {
      match.MX?.forEach(mx => answers.push({
        type: TYPE_MX,
        class: CLASS_IN,
        ttl: DEFAULT_TTL,
        data: mx
      }));
    }
    if (type === 0 || type === TYPE_TXT) {
      match.TXT?.forEach(txt => answers.push({
        type: TYPE_TXT,
        class: CLASS_IN,
        ttl: DEFAULT_TTL,
        data: txt
      }));
    }
  } else {
    console.log(`No records found for domain ${domainLower} (type ${type})`);
    return [];
  }

  console.log(`DNS query for ${domainLower} (type ${type}):`, answers);
  return answers;
}

module.exports = {
  getRecordsForDomain,
};
