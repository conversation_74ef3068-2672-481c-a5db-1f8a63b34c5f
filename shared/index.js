/**
 * Cloud IDE Shared Module
 * Provides centralized configuration, event handling, and integration services
 */

// Configuration Management
const { ConfigManager, configManager } = require('./config');

// Event System
const { EventBus, EventTypes, EventSchemas, eventBus } = require('./events/event-bus');

// Integration Services
const { IntegrationService, integrationService } = require('./integration/integration-service');

// Error Handling
const { CircuitBreaker, RetryHandler, ErrorHandler, errorHandler } = require('./utils/error-handler');

module.exports = {
  // Configuration
  ConfigManager,
  configManager,
  
  // Events
  EventBus,
  EventTypes,
  EventSchemas,
  eventBus,
  
  // Integration
  IntegrationService,
  integrationService,
  
  // Error Handling
  CircuitBreaker,
  RetryHandler,
  ErrorHandler,
  errorHandler
};
