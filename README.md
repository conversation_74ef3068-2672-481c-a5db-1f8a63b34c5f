# Cloud IDE Project

A comprehensive, containerized cloud-based Integrated Development Environment (IDE) with dynamic DNS resolution, reverse proxy routing, and multi-language support.

## Overview

The Cloud IDE Project is a sophisticated development platform that provides isolated, containerized development environments accessible through web browsers. It features dynamic subdomain routing, real-time terminal access, file management, and support for multiple programming languages including JavaScript/Node.js, Python, Java, and more.

## Architecture

The system consists of several interconnected microservices:

- **Container Manager** - Core service managing Docker containers and project lifecycle
- **DNS Server** - Custom DNS server with dynamic subdomain management
- **Reverse Proxy** - Nginx-based routing with automatic configuration
- **Frontend** - React-based web interface with modern IDE features
- **Shared Services** - Common utilities, event bus, and configuration management

## Key Features

### Development Environment Management
- **Multi-language Support**: JavaScript/Node.js, Python, Java, React, Vue.js
- **Containerized Isolation**: Each project runs in its own Docker container
- **Template-based Project Creation**: Pre-configured templates for different languages
- **Resource Management**: CPU and memory allocation per container

### Dynamic Routing & DNS
- **Custom DNS Server**: Automatic subdomain creation for each project
- **Dynamic Reverse Proxy**: Nginx configuration updates in real-time
- **Subdomain Routing**: Access projects via `projectname.ide.local`
- **Redis Integration**: Persistent DNS record storage

### IDE Features
- **Web-based Terminal**: Full terminal access with WebSocket communication
- **File Management**: Complete file system operations
- **Real-time Collaboration**: Event-driven architecture for real-time updates
- **Project Templates**: Quick start templates for various frameworks
- **Resource Monitoring**: Real-time container stats and system health

### Integration & Communication
- **Event Bus System**: Centralized event management across all services
- **Health Monitoring**: Comprehensive health checks and error handling
- **Circuit Breaker Pattern**: Fault tolerance and graceful degradation
- **Retry Logic**: Automatic retry with exponential backoff

## Quick Start

### Prerequisites
- Docker and Docker Compose
- Node.js 18+ (for development)
- Git

### Installation

1. **Clone the repository:**
   ```bash
   git clone <repository-url>
   cd cloud-ide-project
   ```

2. **Build Docker images:**
   ```bash
   # Windows
   build-docker-images.bat
   
   # Linux/macOS
   ./build-docker-images.sh
   ```

3. **Start the services:**
   ```bash
   # For development (simplified setup)
   docker-compose -f docker-compose.simplified.yml up -d
   
   # For production (full setup with reverse proxy)
   docker-compose up -d
   ```

4. **Verify installation:**
   ```bash
   node test-comprehensive.js --quick
   ```

### Access Points

- **Frontend**: http://localhost:5173 (development) or http://localhost:80 (production)
- **Container Manager API**: http://localhost:3000
- **DNS Server API**: http://localhost:8053
- **Redis**: localhost:6379

## 📚 Documentation

Comprehensive documentation is available in the `/docs` directory:

- [Project Overview](docs/overview.md) - Detailed system architecture and design
- [Container Manager](docs/container-manager.md) - Core container management service
- [DNS Server](docs/dns-server.md) - Dynamic DNS resolution system
- [Reverse Proxy](docs/reverse-proxy.md) - Nginx-based routing configuration
- [Frontend](docs/frontend.md) - React-based web interface
- [Shared Services](docs/shared-services.md) - Common utilities and event system
- [API Reference](docs/api-reference.md) - Complete API documentation
- [Deployment Guide](docs/deployment.md) - Production deployment instructions
- [Development Guide](docs/development.md) - Development setup and guidelines
- [Testing Guide](docs/testing.md) - Testing strategies and test suites

## Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `NODE_ENV` | Environment mode | `development` |
| `PORT` | Container Manager port | `3000` |
| `DNS_PORT` | DNS server UDP port | `5354` |
| `DNS_API_PORT` | DNS server HTTP API port | `8053` |
| `DNS_DOMAIN` | Base domain for subdomains | `ide.local` |
| `REDIS_HOST` | Redis server host | `redis` |
| `REDIS_PORT` | Redis server port | `6379` |

### Docker Compose Configurations

- **docker-compose.simplified.yml**: Development setup with core services only
- **docker-compose.yml**: Full production setup with reverse proxy and monitoring

## Testing

The project includes comprehensive testing suites:

```bash
# Run all tests
node test-comprehensive.js

# Quick health check
node test-comprehensive.js --quick

# Docker integration tests
node test-docker-integration.js

# Frontend health checks
node test-frontend-health.js
```

## Development

### Project Structure
```
cloud-ide-project/
├── container-manager/     # Core container management service
├── dns-server/           # Custom DNS server
├── reverse-proxy/        # Nginx reverse proxy setup
├── frontend/            # React-based web interface
├── shared/              # Common utilities and services
├── docker-images/       # Language-specific Docker images
├── docs/               # Comprehensive documentation
└── tests/              # Test suites and utilities
```

### Adding New Languages

1. Create a new Dockerfile in `docker-images/`
2. Update the build scripts
3. Add language configuration in `container-manager/src/services/docker.service.js`
4. Update project templates in `container-manager/src/routes/project.routes.js`

## Security

- **Container Isolation**: Each project runs in isolated Docker containers
- **Non-root Users**: All containers run with non-privileged users
- **Resource Limits**: CPU and memory constraints prevent resource exhaustion
- **Network Isolation**: Services communicate through dedicated Docker networks

## Monitoring & Health

- **Health Endpoints**: All services expose `/health` endpoints
- **Event Tracking**: Comprehensive event logging and monitoring
- **Error Handling**: Circuit breaker pattern with automatic recovery
- **Resource Monitoring**: Real-time container and system metrics

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Acknowledgments

- Built with modern containerization and microservices architecture
- Inspired by cloud development platforms like Replit and CodeSandbox
- Uses industry-standard tools: Docker, Nginx, Redis, React, Node.js

## Support

For support and questions:
- Check the [documentation](docs/)
- Run the test suite to diagnose issues
- Review logs in the `logs/` directories of each service

---

**Author**: Owais Shaikh  
**Version**: 1.0.0  
**Last Updated**: 2025-06-18
