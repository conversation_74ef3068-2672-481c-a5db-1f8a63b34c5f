const fs = require('fs');
const path = require('path');

// Component mapping
const componentMap = {
  'container-manager': {
    name: 'Container Manager Service',
    description: 'Core container orchestration and project management'
  },
  'frontend': {
    name: 'Frontend Application',
    description: 'React-based web interface and IDE'
  },
  'dns-server': {
    name: 'DNS Server',
    description: 'Custom DNS resolution service'
  },
  'reverse-proxy': {
    name: 'Reverse Proxy',
    description: 'Nginx-based dynamic routing'
  },
  'shared': {
    name: 'Shared Services',
    description: 'Common utilities and cross-service communication'
  },
  'docs': {
    name: 'Documentation',
    description: 'Comprehensive project documentation'
  },
  'docker-images': {
    name: 'Docker Images',
    description: 'Language-specific container definitions'
  }
};

function analyzeComponent(componentPath, componentName) {
  const stats = {
    name: componentName,
    path: componentPath,
    files: 0,
    totalLines: 0,
    codeLines: 0,
    commentLines: 0,
    blankLines: 0,
    subdirectories: {},
    fileTypes: {},
    largestFiles: []
  };

  function scanDir(dirPath, relativePath = '') {
    try {
      const items = fs.readdirSync(dirPath);
      
      for (const item of items) {
        const itemPath = path.join(dirPath, item);
        const relativeItemPath = path.join(relativePath, item);
        const stat = fs.statSync(itemPath);
        
        if (stat.isDirectory()) {
          if (!['node_modules', '.git', 'dist', 'build'].includes(item)) {
            if (!stats.subdirectories[item]) {
              stats.subdirectories[item] = {
                files: 0,
                totalLines: 0,
                codeLines: 0
              };
            }
            scanDir(itemPath, relativeItemPath);
          }
        } else if (stat.isFile()) {
          const ext = path.extname(item).toLowerCase();
          const fileName = path.basename(item).toLowerCase();
          
          // Skip certain files
          if (item.includes('package-lock.json') || 
              item.includes('.log') || 
              item.includes('.tmp')) {
            continue;
          }
          
          // Count if it's a source file
          if (['.js', '.jsx', '.ts', '.tsx', '.json', '.yaml', '.yml', 
               '.md', '.css', '.scss', '.html', '.sh', '.bat'].includes(ext) ||
               fileName.startsWith('dockerfile')) {
            
            const lineCount = countLines(itemPath);
            
            stats.files++;
            stats.totalLines += lineCount.totalLines;
            stats.codeLines += lineCount.codeLines;
            stats.commentLines += lineCount.commentLines;
            stats.blankLines += lineCount.blankLines;
            
            // Track by file type
            const fileType = ext || 'dockerfile';
            if (!stats.fileTypes[fileType]) {
              stats.fileTypes[fileType] = { files: 0, lines: 0 };
            }
            stats.fileTypes[fileType].files++;
            stats.fileTypes[fileType].lines += lineCount.totalLines;
            
            // Track by subdirectory
            const topDir = relativePath.split(path.sep)[0] || 'root';
            if (!stats.subdirectories[topDir]) {
              stats.subdirectories[topDir] = {
                files: 0,
                totalLines: 0,
                codeLines: 0
              };
            }
            stats.subdirectories[topDir].files++;
            stats.subdirectories[topDir].totalLines += lineCount.totalLines;
            stats.subdirectories[topDir].codeLines += lineCount.codeLines;
            
            // Track largest files
            stats.largestFiles.push({
              path: relativeItemPath,
              lines: lineCount.totalLines,
              codeLines: lineCount.codeLines
            });
          }
        }
      }
    } catch (error) {
      console.warn(`Error scanning ${dirPath}:`, error.message);
    }
  }

  if (fs.existsSync(componentPath)) {
    scanDir(componentPath);
    
    // Sort largest files
    stats.largestFiles.sort((a, b) => b.lines - a.lines);
    stats.largestFiles = stats.largestFiles.slice(0, 10); // Top 10
  }

  return stats;
}

function countLines(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const lines = content.split('\n');
    
    let totalLines = lines.length;
    let codeLines = 0;
    let commentLines = 0;
    let blankLines = 0;
    
    for (const line of lines) {
      const trimmed = line.trim();
      
      if (trimmed === '') {
        blankLines++;
      } else if (trimmed.startsWith('//') || trimmed.startsWith('#') || 
                 trimmed.startsWith('/*') || trimmed.startsWith('*') ||
                 trimmed.startsWith('<!--') || trimmed.startsWith('---')) {
        commentLines++;
      } else {
        codeLines++;
      }
    }
    
    return { totalLines, codeLines, commentLines, blankLines };
  } catch (error) {
    return { totalLines: 0, codeLines: 0, commentLines: 0, blankLines: 0 };
  }
}

function generateDetailedReport() {
  console.log('🔍 Detailed Component Analysis\n');
  console.log('=' .repeat(80));
  
  const projectRoot = process.cwd();
  const componentStats = {};
  let grandTotal = {
    files: 0,
    totalLines: 0,
    codeLines: 0,
    commentLines: 0,
    blankLines: 0
  };

  // Analyze each component
  for (const [componentDir, componentInfo] of Object.entries(componentMap)) {
    const componentPath = path.join(projectRoot, componentDir);
    const stats = analyzeComponent(componentPath, componentInfo.name);
    componentStats[componentDir] = { ...stats, ...componentInfo };
    
    grandTotal.files += stats.files;
    grandTotal.totalLines += stats.totalLines;
    grandTotal.codeLines += stats.codeLines;
    grandTotal.commentLines += stats.commentLines;
    grandTotal.blankLines += stats.blankLines;
  }

  // Generate report
  for (const [componentDir, stats] of Object.entries(componentStats)) {
    console.log(`\n📦 ${stats.name.toUpperCase()}`);
    console.log(`   ${stats.description}`);
    console.log(`   Path: ${componentDir}/`);
    console.log(`   Files: ${stats.files}`);
    console.log(`   Total Lines: ${stats.totalLines.toLocaleString()}`);
    console.log(`   Code Lines: ${stats.codeLines.toLocaleString()}`);
    
    if (stats.files > 0) {
      console.log(`   Avg Lines/File: ${Math.round(stats.totalLines / stats.files)}`);
      
      // Show subdirectories
      if (Object.keys(stats.subdirectories).length > 0) {
        console.log(`   📁 Subdirectories:`);
        for (const [subdir, substats] of Object.entries(stats.subdirectories)) {
          if (substats.files > 0) {
            console.log(`      ${subdir}: ${substats.files} files, ${substats.totalLines} lines`);
          }
        }
      }
      
      // Show file types
      if (Object.keys(stats.fileTypes).length > 0) {
        console.log(`   📄 File Types:`);
        for (const [type, typestats] of Object.entries(stats.fileTypes)) {
          console.log(`      ${type}: ${typestats.files} files, ${typestats.lines} lines`);
        }
      }
      
      // Show largest files
      if (stats.largestFiles.length > 0) {
        console.log(`   📈 Largest Files:`);
        stats.largestFiles.slice(0, 5).forEach(file => {
          console.log(`      ${file.path}: ${file.lines} lines`);
        });
      }
    }
    
    console.log('   ' + '-'.repeat(60));
  }

  console.log(`\n🎯 GRAND TOTALS:`);
  console.log(`   Total Files: ${grandTotal.files.toLocaleString()}`);
  console.log(`   Total Lines: ${grandTotal.totalLines.toLocaleString()}`);
  console.log(`   Code Lines: ${grandTotal.codeLines.toLocaleString()}`);
  console.log(`   Comment Lines: ${grandTotal.commentLines.toLocaleString()}`);
  console.log(`   Blank Lines: ${grandTotal.blankLines.toLocaleString()}`);

  return componentStats;
}

// Run the analysis
if (require.main === module) {
  generateDetailedReport();
}

module.exports = { generateDetailedReport };
