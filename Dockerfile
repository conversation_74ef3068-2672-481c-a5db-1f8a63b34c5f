FROM node:18-alpine

# Install wget for health checks
RUN apk add --no-cache wget curl

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm install --production

# Copy source code
COPY . .

# Create necessary directories for shared services mount point
RUN mkdir -p /app/shared

# Create necessary directories
RUN mkdir -p /app/config /app/logs && \
    chmod 755 /app/config /app/logs

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nodejs -u 1001

# Change ownership of app directory
RUN chown -R nodejs:nodejs /app

# Switch to non-root user
USER nodejs

# Expose ports
EXPOSE 5354/udp
EXPOSE 8053

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD wget --spider -q http://localhost:8053/health || exit 1

# Start the server
CMD ["node", "index.js"]
