version: '3.8'

services:
  # Container Manager Service (Core)
  container-manager:
    build:
      context: .
      dockerfile: ./container-manager/Dockerfile
    container_name: cloud-ide-container-manager
    ports:
      - "3000:3000"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - container-manager-logs:/app/logs
      - cloud-ide-projects:/var/lib/cloud-ide/projects
      - cloud-ide-database:/var/lib/cloud-ide/database
    environment:
      - NODE_ENV=development
      - PORT=3000
      - DOCKER_SOCKET=/var/run/docker.sock
      - LOG_LEVEL=info
      - DNS_API_URL=http://dns-server:8053
      - PROJECTS_PATH=/var/lib/cloud-ide/projects
      - PROJECTS_DATA_PATH=/var/lib/cloud-ide/database
    depends_on:
      dns-server:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - cloud-ide-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "node", "-e", "require('http').get('http://localhost:3000/health', (res) => process.exit(res.statusCode === 200 ? 0 : 1)).on('error', () => process.exit(1))"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # DNS Server Service (Core)
  dns-server:
    build:
      context: ./dns-server
      dockerfile: Dockerfile
    container_name: cloud-ide-dns-server
    ports:
      - "5354:5354/udp"  # DNS UDP port
      - "8053:8053"      # HTTP API port
    volumes:
      - ./shared:/app/shared:ro
      - ./dns-server/config:/app/config
      - dns-server-logs:/app/logs
    environment:
      - NODE_ENV=development
      - DNS_PORT=5354
      - DNS_API_PORT=8053
      - DNS_DOMAIN=ide.local
      - DNS_TTL=3600
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=
      - REDIS_DB=0
      - LOG_LEVEL=info
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - cloud-ide-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--spider", "-q", "http://localhost:8053/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # Redis for DNS Server (Core)
  redis:
    image: redis:7-alpine
    container_name: cloud-ide-redis
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
    networks:
      - cloud-ide-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

networks:
  cloud-ide-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  redis-data:
    driver: local
  container-manager-logs:
    driver: local
  dns-server-logs:
    driver: local
  cloud-ide-projects:
    driver: local
  cloud-ide-database:
    driver: local
