const fs = require('fs');
const path = require('path');

// File extensions to count
const sourceExtensions = ['.js', '.jsx', '.ts', '.tsx'];
const configExtensions = ['.json', '.yaml', '.yml', '.conf'];
const dockerExtensions = ['.dockerfile'];
const scriptExtensions = ['.sh', '.bat'];
const styleExtensions = ['.css', '.scss', '.sass'];
const markdownExtensions = ['.md'];
const htmlExtensions = ['.html'];

// Directories to exclude
const excludeDirs = ['node_modules', '.git', 'dist', 'build', 'coverage', '.next'];

// Function to check if file should be counted
function shouldCountFile(filePath) {
  const ext = path.extname(filePath).toLowerCase();
  const fileName = path.basename(filePath).toLowerCase();
  
  // Check for Dockerfile
  if (fileName.startsWith('dockerfile')) return true;
  
  // Check extensions
  return [...sourceExtensions, ...configExtensions, ...dockerExtensions, 
          ...scriptExtensions, ...styleExtensions, ...markdownExtensions, 
          ...htmlExtensions].includes(ext);
}

// Function to check if directory should be excluded
function shouldExcludeDir(dirPath) {
  const dirName = path.basename(dirPath);
  return excludeDirs.includes(dirName);
}

// Function to count lines in a file
function countLinesInFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const lines = content.split('\n');
    
    let totalLines = lines.length;
    let codeLines = 0;
    let commentLines = 0;
    let blankLines = 0;
    
    for (const line of lines) {
      const trimmed = line.trim();
      
      if (trimmed === '') {
        blankLines++;
      } else if (trimmed.startsWith('//') || trimmed.startsWith('#') || 
                 trimmed.startsWith('/*') || trimmed.startsWith('*') ||
                 trimmed.startsWith('<!--')) {
        commentLines++;
      } else {
        codeLines++;
      }
    }
    
    return { totalLines, codeLines, commentLines, blankLines };
  } catch (error) {
    console.warn(`Error reading file ${filePath}:`, error.message);
    return { totalLines: 0, codeLines: 0, commentLines: 0, blankLines: 0 };
  }
}

// Function to recursively scan directory
function scanDirectory(dirPath, stats = {}) {
  try {
    const items = fs.readdirSync(dirPath);
    
    for (const item of items) {
      const itemPath = path.join(dirPath, item);
      const stat = fs.statSync(itemPath);
      
      if (stat.isDirectory()) {
        if (!shouldExcludeDir(itemPath)) {
          scanDirectory(itemPath, stats);
        }
      } else if (stat.isFile() && shouldCountFile(itemPath)) {
        const ext = path.extname(itemPath).toLowerCase();
        const fileName = path.basename(itemPath).toLowerCase();
        
        // Categorize file
        let category = 'other';
        if (sourceExtensions.includes(ext)) category = 'source';
        else if (configExtensions.includes(ext)) category = 'config';
        else if (fileName.startsWith('dockerfile')) category = 'docker';
        else if (scriptExtensions.includes(ext)) category = 'scripts';
        else if (styleExtensions.includes(ext)) category = 'styles';
        else if (markdownExtensions.includes(ext)) category = 'docs';
        else if (htmlExtensions.includes(ext)) category = 'html';
        
        if (!stats[category]) {
          stats[category] = {
            files: 0,
            totalLines: 0,
            codeLines: 0,
            commentLines: 0,
            blankLines: 0,
            fileList: []
          };
        }
        
        const lineCount = countLinesInFile(itemPath);
        stats[category].files++;
        stats[category].totalLines += lineCount.totalLines;
        stats[category].codeLines += lineCount.codeLines;
        stats[category].commentLines += lineCount.commentLines;
        stats[category].blankLines += lineCount.blankLines;
        stats[category].fileList.push({
          path: itemPath,
          ...lineCount
        });
      }
    }
  } catch (error) {
    console.warn(`Error scanning directory ${dirPath}:`, error.message);
  }
  
  return stats;
}

// Main function
function calculateLOC() {
  console.log('🔍 Calculating Lines of Code for Cloud IDE Project...\n');
  
  const projectRoot = process.cwd();
  const stats = scanDirectory(projectRoot);
  
  // Calculate totals
  let totalFiles = 0;
  let totalLines = 0;
  let totalCodeLines = 0;
  let totalCommentLines = 0;
  let totalBlankLines = 0;
  
  console.log('📊 Lines of Code Analysis by Category:\n');
  console.log('=' .repeat(80));
  
  for (const [category, data] of Object.entries(stats)) {
    totalFiles += data.files;
    totalLines += data.totalLines;
    totalCodeLines += data.codeLines;
    totalCommentLines += data.commentLines;
    totalBlankLines += data.blankLines;
    
    console.log(`\n📁 ${category.toUpperCase()}`);
    console.log(`   Files: ${data.files}`);
    console.log(`   Total Lines: ${data.totalLines.toLocaleString()}`);
    console.log(`   Code Lines: ${data.codeLines.toLocaleString()}`);
    console.log(`   Comment Lines: ${data.commentLines.toLocaleString()}`);
    console.log(`   Blank Lines: ${data.blankLines.toLocaleString()}`);
  }
  
  console.log('\n' + '=' .repeat(80));
  console.log('\n🎯 TOTAL PROJECT STATISTICS:');
  console.log(`   Total Files: ${totalFiles.toLocaleString()}`);
  console.log(`   Total Lines: ${totalLines.toLocaleString()}`);
  console.log(`   Code Lines: ${totalCodeLines.toLocaleString()}`);
  console.log(`   Comment Lines: ${totalCommentLines.toLocaleString()}`);
  console.log(`   Blank Lines: ${totalBlankLines.toLocaleString()}`);
  
  // Calculate percentages
  const codePercentage = ((totalCodeLines / totalLines) * 100).toFixed(1);
  const commentPercentage = ((totalCommentLines / totalLines) * 100).toFixed(1);
  const blankPercentage = ((totalBlankLines / totalLines) * 100).toFixed(1);
  
  console.log('\n📈 COMPOSITION:');
  console.log(`   Code: ${codePercentage}%`);
  console.log(`   Comments: ${commentPercentage}%`);
  console.log(`   Blank Lines: ${blankPercentage}%`);
  
  return {
    totalFiles,
    totalLines,
    totalCodeLines,
    totalCommentLines,
    totalBlankLines,
    categories: stats,
    percentages: {
      code: codePercentage,
      comments: commentPercentage,
      blank: blankPercentage
    }
  };
}

// Run the calculation
if (require.main === module) {
  calculateLOC();
}

module.exports = { calculateLOC };
