#!/bin/bash

# Cloud IDE - Start and Test Script
# Simple script to start services and validate everything works

set -e  # Exit on any error

echo "🚀 Cloud IDE - Start and Test"
echo "============================="
echo ""

# Function to check if a service is responding
check_service() {
    local name=$1
    local url=$2
    local max_attempts=30
    local attempt=1
    
    echo "⏳ Waiting for $name to be ready..."
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s -f "$url" > /dev/null 2>&1; then
            echo "✅ $name is ready!"
            return 0
        fi
        
        echo "   Attempt $attempt/$max_attempts - waiting..."
        sleep 2
        attempt=$((attempt + 1))
    done
    
    echo "❌ $name failed to start after $max_attempts attempts"
    return 1
}

# Step 1: Check prerequisites
echo "📋 Step 1: Checking Prerequisites"
echo "--------------------------------"

# Check Docker
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed"
    exit 1
fi

if ! docker info &> /dev/null; then
    echo "❌ Docker is not running"
    exit 1
fi

echo "✅ Docker is installed and running"

# Check Node.js
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed"
    exit 1
fi

echo "✅ Node.js is installed ($(node --version))"
echo ""

# Step 2: Start services
echo "📋 Step 2: Starting Services"
echo "----------------------------"

# Use simplified docker-compose if it exists, otherwise use main one
if [ -f "docker-compose.simplified.yml" ]; then
    echo "🐳 Starting services with simplified configuration..."
    docker-compose -f docker-compose.simplified.yml up -d
else
    echo "🐳 Starting core services..."
    docker-compose up -d redis dns-server container-manager
fi

echo ""

# Step 3: Wait for services to be ready
echo "📋 Step 3: Waiting for Services"
echo "-------------------------------"

check_service "Redis" "http://localhost:6379" || exit 1
check_service "DNS Server" "http://localhost:8053/health" || exit 1
check_service "Container Manager" "http://localhost:3000/health" || exit 1

echo ""

# Step 4: Run validation tests
echo "📋 Step 4: Running Validation Tests"
echo "-----------------------------------"

if [ -f "test-comprehensive.js" ]; then
    echo "🧪 Running comprehensive test suite..."
    node test-comprehensive.js --quick
else
    echo "🧪 Running Docker integration tests..."
    cd reverse-proxy && node test-docker-integration.js
fi

echo ""

# Step 5: Show status and next steps
echo "📋 Step 5: System Status"
echo "------------------------"

echo "🐳 Docker Services:"
docker-compose ps

echo ""
echo "🌐 Service URLs:"
echo "  • Container Manager: http://localhost:3000/health"
echo "  • DNS Server API:    http://localhost:8053/health"
echo "  • DNS Records:       http://localhost:8053/api/dns/subdomains"
echo "  • Container API:     http://localhost:3000/api/containers"

echo ""
echo "🎉 Cloud IDE is ready!"
echo ""
echo "📚 Next Steps:"
echo "  • Run full tests:    node test-comprehensive.js"
echo "  • Check logs:        docker-compose logs -f"
echo "  • Stop services:     docker-compose down"
echo ""
