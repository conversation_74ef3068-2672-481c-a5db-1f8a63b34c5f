#!/usr/bin/env node

/**
 * Cloud IDE Performance Testing Suite
 * Comprehensive performance metrics collection and analysis
 */

const axios = require('axios');
const { performance } = require('perf_hooks');
const { spawn, exec } = require('child_process');
const fs = require('fs').promises;
const path = require('path');
const { promisify } = require('util');
const execAsync = promisify(exec);

class PerformanceTestSuite {
  constructor() {
    this.results = {
      responseTime: {},
      throughput: {},
      containerMetrics: {},
      networkLatency: {},
      resourceUtilization: {},
      reliability: {}
    };
    
    this.services = {
      containerManager: 'http://localhost:3000',
      dnsServer: 'http://localhost:8053',
      nginx: 'http://localhost:80'
    };
    
    this.testConfig = {
      iterations: 100,
      concurrentUsers: 10,
      testDuration: 60000, // 60 seconds
      warmupTime: 5000     // 5 seconds
    };
  }

  async runAllTests() {
    console.log('🚀 Cloud IDE Performance Test Suite');
    console.log('===================================\n');

    const testSuites = [
      { name: 'Response Time Analysis', fn: () => this.testResponseTimes() },
      { name: 'Throughput Testing', fn: () => this.testThroughput() },
      { name: 'Container Performance', fn: () => this.testContainerPerformance() },
      { name: 'Network Latency Analysis', fn: () => this.testNetworkLatency() },
      { name: 'Resource Utilization', fn: () => this.testResourceUtilization() },
      { name: 'Reliability Metrics', fn: () => this.testReliability() }
    ];

    for (const suite of testSuites) {
      console.log(`📊 Running: ${suite.name}`);
      console.log('─'.repeat(50));
      
      try {
        await suite.fn();
        console.log(`✅ ${suite.name} - COMPLETED\n`);
      } catch (error) {
        console.log(`❌ ${suite.name} - ERROR: ${error.message}\n`);
      }
    }

    await this.generateReport();
  }

  async testResponseTimes() {
    console.log('  📈 Testing API endpoint response times...');
    
    const endpoints = [
      { name: 'Container Manager Health', url: `${this.services.containerManager}/health` },
      { name: 'DNS Server Health', url: `${this.services.dnsServer}/health` },
      { name: 'Container List', url: `${this.services.containerManager}/api/containers` },
      { name: 'DNS Records', url: `${this.services.dnsServer}/api/dns/records` },
      { name: 'DNS Subdomains', url: `${this.services.dnsServer}/api/dns/subdomains` }
    ];

    for (const endpoint of endpoints) {
      const times = [];
      
      for (let i = 0; i < this.testConfig.iterations; i++) {
        const start = performance.now();
        try {
          await axios.get(endpoint.url, { timeout: 5000 });
          const end = performance.now();
          times.push(end - start);
        } catch (error) {
          console.log(`    ⚠️  ${endpoint.name} failed: ${error.message}`);
        }
      }

      if (times.length > 0) {
        const stats = this.calculateStats(times);
        this.results.responseTime[endpoint.name] = stats;
        console.log(`    ✅ ${endpoint.name}: avg=${stats.average.toFixed(2)}ms, min=${stats.min.toFixed(2)}ms, max=${stats.max.toFixed(2)}ms`);
      }
    }

    // Test WebSocket handshake time
    await this.testWebSocketHandshake();
  }

  async testWebSocketHandshake() {
    console.log('  🔌 Testing WebSocket handshake time...');
    
    const io = require('socket.io-client');
    const handshakeTimes = [];

    for (let i = 0; i < 10; i++) {
      const start = performance.now();
      
      try {
        const socket = io(`${this.services.containerManager}`, {
          timeout: 5000,
          forceNew: true
        });

        await new Promise((resolve, reject) => {
          socket.on('connect', () => {
            const end = performance.now();
            handshakeTimes.push(end - start);
            socket.disconnect();
            resolve();
          });
          
          socket.on('connect_error', reject);
          
          setTimeout(() => reject(new Error('Timeout')), 5000);
        });
      } catch (error) {
        console.log(`    ⚠️  WebSocket handshake failed: ${error.message}`);
      }
    }

    if (handshakeTimes.length > 0) {
      const stats = this.calculateStats(handshakeTimes);
      this.results.responseTime['WebSocket Handshake'] = stats;
      console.log(`    ✅ WebSocket Handshake: avg=${stats.average.toFixed(2)}ms`);
    }
  }

  async testThroughput() {
    console.log('  🚄 Testing throughput (requests/sec)...');
    
    const endpoints = [
      { name: 'Health Check', url: `${this.services.containerManager}/health` },
      { name: 'Container List', url: `${this.services.containerManager}/api/containers` }
    ];

    for (const endpoint of endpoints) {
      const startTime = Date.now();
      const requests = [];
      let completedRequests = 0;
      let errors = 0;

      // Concurrent request simulation
      const promises = [];
      for (let i = 0; i < this.testConfig.concurrentUsers; i++) {
        promises.push(this.runConcurrentRequests(endpoint.url, this.testConfig.testDuration));
      }

      const results = await Promise.all(promises);
      
      // Aggregate results
      results.forEach(result => {
        completedRequests += result.completed;
        errors += result.errors;
      });

      const totalTime = (Date.now() - startTime) / 1000; // seconds
      const throughput = completedRequests / totalTime;
      const errorRate = (errors / (completedRequests + errors)) * 100;

      this.results.throughput[endpoint.name] = {
        requestsPerSecond: throughput,
        totalRequests: completedRequests,
        errors: errors,
        errorRate: errorRate,
        testDuration: totalTime
      };

      console.log(`    ✅ ${endpoint.name}: ${throughput.toFixed(2)} req/sec, ${errorRate.toFixed(2)}% error rate`);
    }
  }

  async runConcurrentRequests(url, duration) {
    const startTime = Date.now();
    let completed = 0;
    let errors = 0;

    while (Date.now() - startTime < duration) {
      try {
        await axios.get(url, { timeout: 2000 });
        completed++;
      } catch (error) {
        errors++;
      }
      
      // Small delay to prevent overwhelming
      await new Promise(resolve => setTimeout(resolve, 10));
    }

    return { completed, errors };
  }

  async testContainerPerformance() {
    console.log('  🐳 Testing container performance metrics...');
    
    try {
      // Test container creation time
      const containerStartTimes = [];
      const testImage = 'nginx:alpine';
      
      for (let i = 0; i < 5; i++) {
        const containerName = `perf-test-${Date.now()}-${i}`;
        const start = performance.now();
        
        try {
          const createResponse = await axios.post(`${this.services.containerManager}/api/containers`, {
            name: containerName,
            image: testImage,
            ports: { '80/tcp': {} },
            portBindings: { '80/tcp': [{ HostPort: `${8100 + i}`, HostIp: '0.0.0.0' }] }
          });
          
          if (createResponse.data.containerId) {
            const end = performance.now();
            containerStartTimes.push(end - start);
            
            // Cleanup
            setTimeout(async () => {
              try {
                await axios.delete(`${this.services.containerManager}/api/containers/${createResponse.data.containerId}`);
              } catch (e) {
                // Ignore cleanup errors
              }
            }, 1000);
          }
        } catch (error) {
          console.log(`    ⚠️  Container creation failed: ${error.message}`);
        }
      }

      if (containerStartTimes.length > 0) {
        const stats = this.calculateStats(containerStartTimes);
        this.results.containerMetrics['Container Start Time'] = stats;
        console.log(`    ✅ Container Start Time: avg=${stats.average.toFixed(2)}ms`);
      }

      // Test container resource usage
      await this.testContainerResourceUsage();
      
    } catch (error) {
      console.log(`    ⚠️  Container performance test limited: ${error.message}`);
    }
  }

  async testContainerResourceUsage() {
    console.log('  📊 Testing container resource usage...');
    
    try {
      // Get current containers
      const containersResponse = await axios.get(`${this.services.containerManager}/api/containers`);
      const containers = containersResponse.data;
      
      if (containers.length > 0) {
        const resourceMetrics = [];
        
        for (const container of containers.slice(0, 3)) { // Test first 3 containers
          try {
            const statsResponse = await axios.get(`${this.services.containerManager}/api/containers/${container.Id}/stats`);
            const stats = statsResponse.data;
            
            if (stats) {
              resourceMetrics.push({
                containerId: container.Id.substring(0, 12),
                cpuUsage: stats.cpu_usage || 0,
                memoryUsage: stats.memory_usage || 0,
                networkIO: stats.network_io || 0
              });
            }
          } catch (error) {
            console.log(`    ⚠️  Stats unavailable for container ${container.Id.substring(0, 12)}`);
          }
        }
        
        this.results.containerMetrics['Resource Usage'] = resourceMetrics;
        console.log(`    ✅ Resource metrics collected for ${resourceMetrics.length} containers`);
      }
    } catch (error) {
      console.log(`    ⚠️  Resource usage test failed: ${error.message}`);
    }
  }

  async testNetworkLatency() {
    console.log('  🌐 Testing network latency components...');

    // DNS Resolution Time
    await this.testDNSResolution();

    // HTTP + Reverse Proxy Latency
    await this.testHTTPProxyLatency();

    // WebSocket Round-trip Time
    await this.testWebSocketRTT();

    // Calculate Total Perceived Latency
    this.calculateTotalLatency();
  }

  async testDNSResolution() {
    const dns = require('dns').promises;
    const dnsResolutionTimes = [];

    const testDomains = ['ide.local', 'localhost'];

    for (const domain of testDomains) {
      for (let i = 0; i < 10; i++) {
        const start = performance.now();
        try {
          await dns.lookup(domain);
          const end = performance.now();
          dnsResolutionTimes.push(end - start);
        } catch (error) {
          console.log(`    ⚠️  DNS resolution failed for ${domain}: ${error.message}`);
        }
      }
    }

    if (dnsResolutionTimes.length > 0) {
      const stats = this.calculateStats(dnsResolutionTimes);
      this.results.networkLatency['DNS Resolution'] = stats;
      console.log(`    ✅ DNS Resolution: avg=${stats.average.toFixed(2)}ms`);
    }
  }

  async testHTTPProxyLatency() {
    const httpLatencyTimes = [];

    // Test direct container manager vs nginx proxy
    const directUrl = `${this.services.containerManager}/health`;

    for (let i = 0; i < 20; i++) {
      const start = performance.now();
      try {
        await axios.get(directUrl, { timeout: 5000 });
        const end = performance.now();
        httpLatencyTimes.push(end - start);
      } catch (error) {
        console.log(`    ⚠️  HTTP request failed: ${error.message}`);
      }
    }

    if (httpLatencyTimes.length > 0) {
      const stats = this.calculateStats(httpLatencyTimes);
      this.results.networkLatency['HTTP + Proxy'] = stats;
      console.log(`    ✅ HTTP + Proxy: avg=${stats.average.toFixed(2)}ms`);
    }
  }

  async testWebSocketRTT() {
    console.log('    🔄 Testing WebSocket round-trip time...');

    const io = require('socket.io-client');
    const rttTimes = [];

    try {
      const socket = io(`${this.services.containerManager}`, {
        timeout: 10000,
        forceNew: true
      });

      await new Promise((resolve, reject) => {
        socket.on('connect', async () => {
          console.log('    📡 WebSocket connected, measuring RTT...');

          for (let i = 0; i < 10; i++) {
            const start = performance.now();

            socket.emit('ping', { timestamp: start });

            await new Promise((pingResolve) => {
              socket.once('pong', () => {
                const end = performance.now();
                rttTimes.push(end - start);
                pingResolve();
              });

              setTimeout(pingResolve, 1000); // Timeout after 1 second
            });
          }

          socket.disconnect();
          resolve();
        });

        socket.on('connect_error', reject);
        setTimeout(() => reject(new Error('Connection timeout')), 10000);
      });

      if (rttTimes.length > 0) {
        const stats = this.calculateStats(rttTimes);
        this.results.networkLatency['WebSocket RTT'] = stats;
        console.log(`    ✅ WebSocket RTT: avg=${stats.average.toFixed(2)}ms`);
      }
    } catch (error) {
      console.log(`    ⚠️  WebSocket RTT test failed: ${error.message}`);
    }
  }

  calculateTotalLatency() {
    const dns = this.results.networkLatency['DNS Resolution']?.average || 0;
    const http = this.results.networkLatency['HTTP + Proxy']?.average || 0;
    const ws = this.results.networkLatency['WebSocket RTT']?.average || 0;

    const totalLatency = dns + http + ws;

    this.results.networkLatency['Total Perceived Latency'] = {
      T_dns: dns,
      T_http_proxy: http,
      T_ws: ws,
      T_total: totalLatency
    };

    console.log(`    📊 Total Perceived Latency: ${totalLatency.toFixed(2)}ms`);
    console.log(`       T_dns: ${dns.toFixed(2)}ms + T_proxy: ${http.toFixed(2)}ms + T_ws: ${ws.toFixed(2)}ms`);
  }

  async testResourceUtilization() {
    console.log('  💻 Testing resource utilization...');

    try {
      // Get system resource usage
      const systemStats = await this.getSystemResourceUsage();
      this.results.resourceUtilization['System'] = systemStats;

      // Get Docker resource usage
      const dockerStats = await this.getDockerResourceUsage();
      this.results.resourceUtilization['Docker'] = dockerStats;

      console.log(`    ✅ System CPU: ${systemStats.cpuUsage.toFixed(2)}%, Memory: ${systemStats.memoryUsage.toFixed(2)}%`);
      console.log(`    ✅ Docker containers monitored: ${dockerStats.containerCount}`);

    } catch (error) {
      console.log(`    ⚠️  Resource utilization test failed: ${error.message}`);
    }
  }

  async getSystemResourceUsage() {
    const os = require('os');

    // CPU usage calculation
    const cpus = os.cpus();
    let totalIdle = 0;
    let totalTick = 0;

    cpus.forEach(cpu => {
      for (const type in cpu.times) {
        totalTick += cpu.times[type];
      }
      totalIdle += cpu.times.idle;
    });

    const cpuUsage = 100 - (totalIdle / totalTick * 100);

    // Memory usage
    const totalMem = os.totalmem();
    const freeMem = os.freemem();
    const memoryUsage = ((totalMem - freeMem) / totalMem) * 100;

    return {
      cpuUsage,
      memoryUsage,
      totalMemoryGB: (totalMem / (1024 * 1024 * 1024)).toFixed(2),
      freeMemoryGB: (freeMem / (1024 * 1024 * 1024)).toFixed(2),
      loadAverage: os.loadavg()
    };
  }

  async getDockerResourceUsage() {
    try {
      const { stdout } = await execAsync('docker stats --no-stream --format "table {{.Container}}\\t{{.CPUPerc}}\\t{{.MemUsage}}"');
      const lines = stdout.trim().split('\n').slice(1); // Remove header

      const containers = lines.map(line => {
        const parts = line.split('\t');
        return {
          container: parts[0],
          cpuPercent: parts[1],
          memUsage: parts[2]
        };
      });

      return {
        containerCount: containers.length,
        containers: containers
      };
    } catch (error) {
      return {
        containerCount: 0,
        containers: [],
        error: error.message
      };
    }
  }

  async testReliability() {
    console.log('  🛡️  Testing reliability metrics...');

    // Test session success rate
    await this.testSessionSuccessRate();

    // Test error rates
    await this.testErrorRates();

    // Test service availability
    await this.testServiceAvailability();
  }

  async testSessionSuccessRate() {
    console.log('    📊 Testing session success rate...');

    const totalSessions = 50;
    let successfulSessions = 0;
    let failedSessions = 0;
    const sessionTimes = [];

    for (let i = 0; i < totalSessions; i++) {
      const sessionStart = performance.now();

      try {
        // Simulate a complete session workflow
        // 1. Health check
        await axios.get(`${this.services.containerManager}/health`, { timeout: 5000 });

        // 2. Get containers
        await axios.get(`${this.services.containerManager}/api/containers`, { timeout: 5000 });

        // 3. DNS query
        await axios.get(`${this.services.dnsServer}/api/dns/records`, { timeout: 5000 });

        const sessionEnd = performance.now();
        sessionTimes.push(sessionEnd - sessionStart);
        successfulSessions++;

      } catch (error) {
        failedSessions++;
      }
    }

    const successRate = (successfulSessions / totalSessions) * 100;
    const avgSessionTime = sessionTimes.length > 0 ?
      sessionTimes.reduce((a, b) => a + b, 0) / sessionTimes.length : 0;

    this.results.reliability['Session Success Rate'] = {
      totalSessions,
      successfulSessions,
      failedSessions,
      successRate,
      averageSessionTime: avgSessionTime
    };

    console.log(`    ✅ Session Success Rate: ${successRate.toFixed(2)}% (${successfulSessions}/${totalSessions})`);
    console.log(`    📈 Average Session Time: ${avgSessionTime.toFixed(2)}ms`);
  }

  async testErrorRates() {
    console.log('    🚨 Testing error rates...');

    const endpoints = [
      `${this.services.containerManager}/health`,
      `${this.services.containerManager}/api/containers`,
      `${this.services.dnsServer}/health`,
      `${this.services.dnsServer}/api/dns/records`
    ];

    const errorRates = {};

    for (const endpoint of endpoints) {
      let requests = 0;
      let errors = 0;

      for (let i = 0; i < 20; i++) {
        requests++;
        try {
          await axios.get(endpoint, { timeout: 3000 });
        } catch (error) {
          errors++;
        }
      }

      const errorRate = (errors / requests) * 100;
      const endpointName = endpoint.split('/').pop() || 'unknown';
      errorRates[endpointName] = {
        requests,
        errors,
        errorRate
      };
    }

    this.results.reliability['Error Rates'] = errorRates;

    Object.entries(errorRates).forEach(([endpoint, data]) => {
      console.log(`    📊 ${endpoint}: ${data.errorRate.toFixed(2)}% error rate (${data.errors}/${data.requests})`);
    });
  }

  async testServiceAvailability() {
    console.log('    🔍 Testing service availability...');

    const services = [
      { name: 'Container Manager', url: `${this.services.containerManager}/health` },
      { name: 'DNS Server', url: `${this.services.dnsServer}/health` }
    ];

    const availabilityResults = {};

    for (const service of services) {
      const checks = 10;
      let available = 0;
      const responseTimes = [];

      for (let i = 0; i < checks; i++) {
        const start = performance.now();
        try {
          const response = await axios.get(service.url, { timeout: 5000 });
          if (response.status === 200) {
            available++;
            const end = performance.now();
            responseTimes.push(end - start);
          }
        } catch (error) {
          // Service unavailable
        }

        // Wait between checks
        await new Promise(resolve => setTimeout(resolve, 1000));
      }

      const availability = (available / checks) * 100;
      const avgResponseTime = responseTimes.length > 0 ?
        responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length : 0;

      availabilityResults[service.name] = {
        availability,
        avgResponseTime,
        checks,
        available
      };

      console.log(`    ✅ ${service.name}: ${availability.toFixed(2)}% availability, ${avgResponseTime.toFixed(2)}ms avg response`);
    }

    this.results.reliability['Service Availability'] = availabilityResults;
  }

  async generateReport() {
    console.log('📋 Generating Performance Report...');

    const reportData = {
      timestamp: new Date().toISOString(),
      testConfig: this.testConfig,
      results: this.results,
      summary: this.generateSummary()
    };

    // Save JSON report
    const jsonReport = JSON.stringify(reportData, null, 2);
    await fs.writeFile('performance-report.json', jsonReport);

    // Generate Markdown report
    const markdownReport = this.generateMarkdownReport(reportData);
    await fs.writeFile('performance-analysis-report.md', markdownReport);

    console.log('✅ Reports generated:');
    console.log('   📄 performance-report.json');
    console.log('   📄 performance-analysis-report.md');

    // Print summary
    this.printSummary();
  }

  generateSummary() {
    const summary = {
      responseTime: {},
      throughput: {},
      reliability: {},
      recommendations: []
    };

    // Response time summary
    Object.entries(this.results.responseTime).forEach(([endpoint, stats]) => {
      summary.responseTime[endpoint] = {
        average: stats.average?.toFixed(2),
        p95: stats.p95?.toFixed(2)
      };
    });

    // Throughput summary
    Object.entries(this.results.throughput).forEach(([endpoint, data]) => {
      summary.throughput[endpoint] = {
        requestsPerSecond: data.requestsPerSecond?.toFixed(2),
        errorRate: data.errorRate?.toFixed(2)
      };
    });

    // Reliability summary
    if (this.results.reliability['Session Success Rate']) {
      summary.reliability.sessionSuccessRate =
        this.results.reliability['Session Success Rate'].successRate?.toFixed(2);
    }

    // Generate recommendations
    summary.recommendations = this.generateRecommendations();

    return summary;
  }

  generateRecommendations() {
    const recommendations = [];

    // Response time recommendations
    Object.entries(this.results.responseTime).forEach(([endpoint, stats]) => {
      if (stats.average > 1000) {
        recommendations.push(`⚠️  ${endpoint} has high response time (${stats.average.toFixed(2)}ms) - consider optimization`);
      }
    });

    // Throughput recommendations
    Object.entries(this.results.throughput).forEach(([endpoint, data]) => {
      if (data.errorRate > 5) {
        recommendations.push(`🚨 ${endpoint} has high error rate (${data.errorRate.toFixed(2)}%) - investigate stability`);
      }
      if (data.requestsPerSecond < 10) {
        recommendations.push(`📈 ${endpoint} has low throughput (${data.requestsPerSecond.toFixed(2)} req/sec) - consider scaling`);
      }
    });

    // Network latency recommendations
    if (this.results.networkLatency['Total Perceived Latency']?.T_total > 500) {
      recommendations.push('🌐 High total network latency detected - optimize DNS resolution and proxy configuration');
    }

    return recommendations;
  }

  generateMarkdownReport(reportData) {
    const { results, summary, timestamp } = reportData;

    let markdown = `# Cloud IDE Performance Analysis Report

**Generated:** ${new Date(timestamp).toLocaleString()}

## Executive Summary

This report provides comprehensive performance metrics for the Cloud IDE project, including response times, throughput analysis, network latency measurements, resource utilization, and reliability metrics.

## 📊 Key Performance Indicators

### Response Time Metrics
`;

    // Response Time Table
    markdown += `
| Endpoint | Average (ms) | Min (ms) | Max (ms) | P95 (ms) | P99 (ms) |
|----------|--------------|----------|----------|----------|----------|
`;

    Object.entries(results.responseTime).forEach(([endpoint, stats]) => {
      markdown += `| ${endpoint} | ${stats.average?.toFixed(2) || 'N/A'} | ${stats.min?.toFixed(2) || 'N/A'} | ${stats.max?.toFixed(2) || 'N/A'} | ${stats.p95?.toFixed(2) || 'N/A'} | ${stats.p99?.toFixed(2) || 'N/A'} |\n`;
    });

    // Throughput Analysis
    markdown += `
### Throughput Analysis

| Endpoint | Requests/sec | Total Requests | Error Rate (%) | Test Duration (s) |
|----------|--------------|----------------|----------------|-------------------|
`;

    Object.entries(results.throughput).forEach(([endpoint, data]) => {
      markdown += `| ${endpoint} | ${data.requestsPerSecond?.toFixed(2) || 'N/A'} | ${data.totalRequests || 'N/A'} | ${data.errorRate?.toFixed(2) || 'N/A'} | ${data.testDuration?.toFixed(2) || 'N/A'} |\n`;
    });

    // Network Latency Analysis
    markdown += `
### Network Latency Analysis

The total perceived latency is calculated as: **T_total = T_dns + T_proxy + T_container + T_ws**

`;

    if (results.networkLatency['Total Perceived Latency']) {
      const latency = results.networkLatency['Total Perceived Latency'];
      markdown += `
| Component | Latency (ms) |
|-----------|--------------|
| DNS Resolution (T_dns) | ${latency.T_dns?.toFixed(2) || 'N/A'} |
| HTTP + Proxy (T_http + T_proxy) | ${latency.T_http_proxy?.toFixed(2) || 'N/A'} |
| WebSocket RTT (T_ws) | ${latency.T_ws?.toFixed(2) || 'N/A'} |
| **Total Perceived Latency** | **${latency.T_total?.toFixed(2) || 'N/A'}** |

`;
    }

    // Resource Utilization
    markdown += `
### Resource Utilization

`;

    if (results.resourceUtilization.System) {
      const sys = results.resourceUtilization.System;
      markdown += `
**System Resources:**
- CPU Usage: ${sys.cpuUsage?.toFixed(2) || 'N/A'}%
- Memory Usage: ${sys.memoryUsage?.toFixed(2) || 'N/A'}%
- Total Memory: ${sys.totalMemoryGB || 'N/A'} GB
- Free Memory: ${sys.freeMemoryGB || 'N/A'} GB
- Load Average: [${sys.loadAverage?.map(l => l.toFixed(2)).join(', ') || 'N/A'}]

`;
    }

    if (results.resourceUtilization.Docker) {
      const docker = results.resourceUtilization.Docker;
      markdown += `
**Docker Resources:**
- Active Containers: ${docker.containerCount || 0}

`;
      if (docker.containers && docker.containers.length > 0) {
        markdown += `
| Container | CPU % | Memory Usage |
|-----------|-------|--------------|
`;
        docker.containers.forEach(container => {
          markdown += `| ${container.container} | ${container.cpuPercent} | ${container.memUsage} |\n`;
        });
      }
    }

    // Reliability Metrics
    markdown += `
### Reliability Metrics

`;

    if (results.reliability['Session Success Rate']) {
      const session = results.reliability['Session Success Rate'];
      markdown += `
**Session Success Rate:**
- Success Rate: ${session.successRate?.toFixed(2) || 'N/A'}%
- Successful Sessions: ${session.successfulSessions || 'N/A'}/${session.totalSessions || 'N/A'}
- Average Session Time: ${session.averageSessionTime?.toFixed(2) || 'N/A'}ms

`;
    }

    if (results.reliability['Service Availability']) {
      markdown += `
**Service Availability:**

| Service | Availability (%) | Avg Response Time (ms) | Checks |
|---------|------------------|------------------------|--------|
`;
      Object.entries(results.reliability['Service Availability']).forEach(([service, data]) => {
        markdown += `| ${service} | ${data.availability?.toFixed(2) || 'N/A'} | ${data.avgResponseTime?.toFixed(2) || 'N/A'} | ${data.available || 'N/A'}/${data.checks || 'N/A'} |\n`;
      });
    }

    // Container Performance
    if (results.containerMetrics['Container Start Time']) {
      const containerStats = results.containerMetrics['Container Start Time'];
      markdown += `
### Container Performance Metrics

**Container Start Time:**
- Average: ${containerStats.average?.toFixed(2) || 'N/A'}ms
- Min: ${containerStats.min?.toFixed(2) || 'N/A'}ms
- Max: ${containerStats.max?.toFixed(2) || 'N/A'}ms
- P95: ${containerStats.p95?.toFixed(2) || 'N/A'}ms

`;
    }

    // Recommendations
    if (summary.recommendations && summary.recommendations.length > 0) {
      markdown += `
## 🎯 Performance Recommendations

`;
      summary.recommendations.forEach(rec => {
        markdown += `- ${rec}\n`;
      });
    }

    // Mathematical Formulas
    markdown += `
## 📐 Performance Formulas Used

### Network Latency
\`\`\`
T_total = T_dns + T_proxy + T_container + T_ws
\`\`\`

### Resource Utilization
\`\`\`
U_cpu = Σ (CPU_i / CPU_total)
U_ram = Σ (RAM_i / RAM_total)
\`\`\`

### Reliability Metrics
\`\`\`
Session Success Rate = (Successful Sessions / Total Session Attempts) × 100%
Error Rate = (Failed Requests / Total Requests) × 100%
\`\`\`

## 📈 Performance Optimization Metrics

### Time Saved via Caching
\`\`\`
ΔT = T_uncached - T_cached
\`\`\`

### Throughput Efficiency
\`\`\`
Efficiency = (Actual Throughput / Theoretical Maximum) × 100%
\`\`\`

## 🔧 Test Configuration

- **Iterations per test:** ${reportData.testConfig.iterations}
- **Concurrent users:** ${reportData.testConfig.concurrentUsers}
- **Test duration:** ${reportData.testConfig.testDuration / 1000}s
- **Warmup time:** ${reportData.testConfig.warmupTime / 1000}s

## 📊 Raw Data

For detailed raw performance data, see the accompanying \`performance-report.json\` file.

---

*Report generated by Cloud IDE Performance Test Suite*
`;

    return markdown;
  }

  printSummary() {
    console.log('\n📊 Performance Test Summary');
    console.log('============================');

    // Response Time Summary
    console.log('\n🚀 Response Times:');
    Object.entries(this.results.responseTime).forEach(([endpoint, stats]) => {
      console.log(`  ${endpoint}: ${stats.average?.toFixed(2) || 'N/A'}ms avg (P95: ${stats.p95?.toFixed(2) || 'N/A'}ms)`);
    });

    // Throughput Summary
    console.log('\n📈 Throughput:');
    Object.entries(this.results.throughput).forEach(([endpoint, data]) => {
      console.log(`  ${endpoint}: ${data.requestsPerSecond?.toFixed(2) || 'N/A'} req/sec (${data.errorRate?.toFixed(2) || 'N/A'}% errors)`);
    });

    // Network Latency Summary
    if (this.results.networkLatency['Total Perceived Latency']) {
      const latency = this.results.networkLatency['Total Perceived Latency'];
      console.log('\n🌐 Network Latency:');
      console.log(`  Total Perceived Latency: ${latency.T_total?.toFixed(2) || 'N/A'}ms`);
      console.log(`  DNS: ${latency.T_dns?.toFixed(2) || 'N/A'}ms | HTTP+Proxy: ${latency.T_http_proxy?.toFixed(2) || 'N/A'}ms | WebSocket: ${latency.T_ws?.toFixed(2) || 'N/A'}ms`);
    }

    // Reliability Summary
    if (this.results.reliability['Session Success Rate']) {
      const session = this.results.reliability['Session Success Rate'];
      console.log('\n🛡️  Reliability:');
      console.log(`  Session Success Rate: ${session.successRate?.toFixed(2) || 'N/A'}%`);
    }

    console.log('\n✅ Performance analysis complete!');
    console.log('📄 Detailed reports saved to:');
    console.log('   - performance-report.json');
    console.log('   - performance-analysis-report.md');
  }

  calculateStats(values) {
    if (values.length === 0) return { average: 0, min: 0, max: 0, median: 0, p95: 0, p99: 0 };

    const sorted = values.sort((a, b) => a - b);
    const sum = values.reduce((a, b) => a + b, 0);

    return {
      average: sum / values.length,
      min: sorted[0],
      max: sorted[sorted.length - 1],
      median: sorted[Math.floor(sorted.length / 2)],
      p95: sorted[Math.floor(sorted.length * 0.95)],
      p99: sorted[Math.floor(sorted.length * 0.99)]
    };
  }
}

// Export for use as module
module.exports = PerformanceTestSuite;

// Run if called directly
if (require.main === module) {
  const args = process.argv.slice(2);

  if (args.includes('--help') || args.includes('-h')) {
    console.log(`
🚀 Cloud IDE Performance Test Suite

Usage: node performance-test-suite.js [options]

Options:
  --help, -h          Show this help message
  --quick, -q         Run quick performance check (reduced iterations)
  --response, -r      Test only response times
  --throughput, -t    Test only throughput
  --network, -n       Test only network latency
  --reliability, -rel Test only reliability metrics

Examples:
  node performance-test-suite.js              # Run all performance tests
  node performance-test-suite.js --quick      # Quick performance check
  node performance-test-suite.js --response   # Response time tests only
    `);
    process.exit(0);
  }

  const suite = new PerformanceTestSuite();

  // Adjust config for quick tests
  if (args.includes('--quick') || args.includes('-q')) {
    suite.testConfig.iterations = 20;
    suite.testConfig.concurrentUsers = 5;
    suite.testConfig.testDuration = 30000;
    console.log('🚀 Running Quick Performance Tests...\n');
  }

  // Run specific test suites
  if (args.includes('--response') || args.includes('-r')) {
    suite.testResponseTimes().then(() => suite.generateReport()).catch(console.error);
  } else if (args.includes('--throughput') || args.includes('-t')) {
    suite.testThroughput().then(() => suite.generateReport()).catch(console.error);
  } else if (args.includes('--network') || args.includes('-n')) {
    suite.testNetworkLatency().then(() => suite.generateReport()).catch(console.error);
  } else if (args.includes('--reliability') || args.includes('-rel')) {
    suite.testReliability().then(() => suite.generateReport()).catch(console.error);
  } else {
    suite.runAllTests().catch(console.error);
  }
}
