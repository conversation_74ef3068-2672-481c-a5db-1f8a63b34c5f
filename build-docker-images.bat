@echo off
echo Building Cloud IDE Docker Images...
echo ===================================

cd docker-images

echo.
echo Building Node.js/JavaScript image...
docker build -f Dockerfile.nodejs -t cloud-ide/nodejs:latest .
if %ERRORLEVEL% neq 0 (
    echo Failed to build Node.js image
    exit /b 1
)

echo.
echo Building Python image...
docker build -f Dockerfile.python -t cloud-ide/python:latest .
if %ERRORLEVEL% neq 0 (
    echo Failed to build Python image
    exit /b 1
)

echo.
echo Building Java image...
docker build -f Dockerfile.java -t cloud-ide/java:latest .
if %ERRORLEVEL% neq 0 (
    echo Failed to build Java image
    exit /b 1
)

echo.
echo Building General purpose image...
docker build -f Dockerfile.general -t cloud-ide/general:latest .
if %ERRORLEVEL% neq 0 (
    echo Failed to build General image
    exit /b 1
)

cd ..

echo.
echo ✅ All Docker images built successfully!
echo.
echo Available images:
docker images | findstr cloud-ide

echo.
echo 🎉 Docker images are ready for use!
