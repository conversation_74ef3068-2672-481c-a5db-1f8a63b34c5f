const fs = require('fs');
const path = require('path');

// Only source code extensions (no config files)
const sourceExtensions = ['.js', '.jsx', '.ts', '.tsx'];
const styleExtensions = ['.css', '.scss', '.sass'];
const htmlExtensions = ['.html'];

// Directories to exclude
const excludeDirs = ['node_modules', '.git', 'dist', 'build', 'coverage', '.next', 'docs', 'test', 'tests', '__tests__'];

// Files to exclude (configuration and non-code files)
const excludeFiles = [
  'package.json', 'package-lock.json', 'yarn.lock',
  'tsconfig.json', 'jsconfig.json',
  'vite.config.js', 'webpack.config.js', 'rollup.config.js',
  'tailwind.config.js', 'postcss.config.js',
  'eslint.config.js', '.eslintrc.js', '.eslintrc.json',
  'prettier.config.js', '.prettierrc',
  'jest.config.js', 'vitest.config.js',
  'docker-compose.yml', 'docker-compose.yaml',
  '.env', '.env.local', '.env.development', '.env.production',
  'README.md', 'CHANGELOG.md', 'LICENSE',
  '.gitignore', '.dockerignore',
  'Dockerfile', 'dockerfile'
];

// Component mapping
const componentMap = {
  'container-manager': {
    name: 'Container Manager',
    description: 'Core container orchestration service',
    type: 'backend'
  },
  'frontend': {
    name: 'Frontend Application',
    description: 'React-based web interface',
    type: 'frontend'
  },
  'dns-server': {
    name: 'DNS Server',
    description: 'Custom DNS resolution service',
    type: 'backend'
  },
  'reverse-proxy': {
    name: 'Reverse Proxy',
    description: 'Dynamic routing service',
    type: 'backend'
  },
  'shared': {
    name: 'Shared Services',
    description: 'Common utilities and libraries',
    type: 'shared'
  }
};

function shouldCountFile(filePath) {
  const ext = path.extname(filePath).toLowerCase();
  const fileName = path.basename(filePath).toLowerCase();
  
  // Exclude specific files
  if (excludeFiles.includes(fileName)) return false;
  
  // Only count source code files
  return [...sourceExtensions, ...styleExtensions, ...htmlExtensions].includes(ext);
}

function shouldExcludeDir(dirPath) {
  const dirName = path.basename(dirPath);
  return excludeDirs.includes(dirName);
}

function countLinesInFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const lines = content.split('\n');
    
    let totalLines = lines.length;
    let codeLines = 0;
    let commentLines = 0;
    let blankLines = 0;
    
    for (const line of lines) {
      const trimmed = line.trim();
      
      if (trimmed === '') {
        blankLines++;
      } else if (trimmed.startsWith('//') || trimmed.startsWith('#') || 
                 trimmed.startsWith('/*') || trimmed.startsWith('*') ||
                 trimmed.startsWith('<!--') || trimmed.startsWith('*/')) {
        commentLines++;
      } else {
        codeLines++;
      }
    }
    
    return { totalLines, codeLines, commentLines, blankLines };
  } catch (error) {
    return { totalLines: 0, codeLines: 0, commentLines: 0, blankLines: 0 };
  }
}

function analyzeComponent(componentPath, componentName) {
  const stats = {
    name: componentName,
    path: componentPath,
    files: 0,
    totalLines: 0,
    codeLines: 0,
    commentLines: 0,
    blankLines: 0,
    fileTypes: {},
    subdirectories: {},
    largestFiles: []
  };

  function scanDir(dirPath, relativePath = '') {
    try {
      const items = fs.readdirSync(dirPath);
      
      for (const item of items) {
        const itemPath = path.join(dirPath, item);
        const relativeItemPath = path.join(relativePath, item);
        const stat = fs.statSync(itemPath);
        
        if (stat.isDirectory()) {
          if (!shouldExcludeDir(itemPath)) {
            scanDir(itemPath, relativeItemPath);
          }
        } else if (stat.isFile() && shouldCountFile(itemPath)) {
          const ext = path.extname(item).toLowerCase();
          const lineCount = countLinesInFile(itemPath);
          
          stats.files++;
          stats.totalLines += lineCount.totalLines;
          stats.codeLines += lineCount.codeLines;
          stats.commentLines += lineCount.commentLines;
          stats.blankLines += lineCount.blankLines;
          
          // Track by file type
          if (!stats.fileTypes[ext]) {
            stats.fileTypes[ext] = { files: 0, lines: 0, codeLines: 0 };
          }
          stats.fileTypes[ext].files++;
          stats.fileTypes[ext].lines += lineCount.totalLines;
          stats.fileTypes[ext].codeLines += lineCount.codeLines;
          
          // Track by subdirectory
          const topDir = relativePath.split(path.sep)[0] || 'root';
          if (!stats.subdirectories[topDir]) {
            stats.subdirectories[topDir] = {
              files: 0,
              totalLines: 0,
              codeLines: 0
            };
          }
          stats.subdirectories[topDir].files++;
          stats.subdirectories[topDir].totalLines += lineCount.totalLines;
          stats.subdirectories[topDir].codeLines += lineCount.codeLines;
          
          // Track largest files
          stats.largestFiles.push({
            path: relativeItemPath,
            lines: lineCount.totalLines,
            codeLines: lineCount.codeLines,
            ext: ext
          });
        }
      }
    } catch (error) {
      console.warn(`Error scanning ${dirPath}:`, error.message);
    }
  }

  if (fs.existsSync(componentPath)) {
    scanDir(componentPath);
    
    // Sort largest files
    stats.largestFiles.sort((a, b) => b.codeLines - a.codeLines);
    stats.largestFiles = stats.largestFiles.slice(0, 10);
  }

  return stats;
}

function generateCodeOnlyReport() {
  console.log('🔍 Code-Only LOC Analysis (Excluding Config Files)\n');
  console.log('=' .repeat(80));
  
  const projectRoot = process.cwd();
  const componentStats = {};
  let grandTotal = {
    files: 0,
    totalLines: 0,
    codeLines: 0,
    commentLines: 0,
    blankLines: 0
  };

  // Analyze each component
  for (const [componentDir, componentInfo] of Object.entries(componentMap)) {
    const componentPath = path.join(projectRoot, componentDir);
    const stats = analyzeComponent(componentPath, componentInfo.name);
    componentStats[componentDir] = { ...stats, ...componentInfo };
    
    grandTotal.files += stats.files;
    grandTotal.totalLines += stats.totalLines;
    grandTotal.codeLines += stats.codeLines;
    grandTotal.commentLines += stats.commentLines;
    grandTotal.blankLines += stats.blankLines;
  }

  // Generate report
  for (const [componentDir, stats] of Object.entries(componentStats)) {
    if (stats.files > 0) {
      console.log(`\n📦 ${stats.name.toUpperCase()} (${stats.type})`);
      console.log(`   ${stats.description}`);
      console.log(`   Files: ${stats.files}`);
      console.log(`   Total Lines: ${stats.totalLines.toLocaleString()}`);
      console.log(`   Code Lines: ${stats.codeLines.toLocaleString()}`);
      console.log(`   Comment Lines: ${stats.commentLines.toLocaleString()}`);
      console.log(`   Blank Lines: ${stats.blankLines.toLocaleString()}`);
      console.log(`   Code Density: ${((stats.codeLines / stats.totalLines) * 100).toFixed(1)}%`);
      console.log(`   Avg Lines/File: ${Math.round(stats.totalLines / stats.files)}`);
      
      // Show file types
      if (Object.keys(stats.fileTypes).length > 0) {
        console.log(`   📄 File Types:`);
        for (const [type, typestats] of Object.entries(stats.fileTypes)) {
          console.log(`      ${type}: ${typestats.files} files, ${typestats.codeLines} code lines`);
        }
      }
      
      // Show top 3 largest files by code lines
      if (stats.largestFiles.length > 0) {
        console.log(`   📈 Largest Files (by code lines):`);
        stats.largestFiles.slice(0, 3).forEach((file, index) => {
          console.log(`      ${index + 1}. ${file.path}: ${file.codeLines} code lines`);
        });
      }
      
      console.log('   ' + '-'.repeat(60));
    }
  }

  console.log(`\n🎯 CODE-ONLY TOTALS:`);
  console.log(`   Total Files: ${grandTotal.files.toLocaleString()}`);
  console.log(`   Total Lines: ${grandTotal.totalLines.toLocaleString()}`);
  console.log(`   Code Lines: ${grandTotal.codeLines.toLocaleString()}`);
  console.log(`   Comment Lines: ${grandTotal.commentLines.toLocaleString()}`);
  console.log(`   Blank Lines: ${grandTotal.blankLines.toLocaleString()}`);
  
  const codePercentage = ((grandTotal.codeLines / grandTotal.totalLines) * 100).toFixed(1);
  const commentPercentage = ((grandTotal.commentLines / grandTotal.totalLines) * 100).toFixed(1);
  
  console.log(`\n📈 COMPOSITION:`);
  console.log(`   Code: ${codePercentage}%`);
  console.log(`   Comments: ${commentPercentage}%`);
  console.log(`   Blank Lines: ${((grandTotal.blankLines / grandTotal.totalLines) * 100).toFixed(1)}%`);

  return { componentStats, grandTotal };
}

// Run the analysis
if (require.main === module) {
  generateCodeOnlyReport();
}

module.exports = { generateCodeOnlyReport };
