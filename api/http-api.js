const express = require("express");
const cors = require("cors");
const {
  dynamicSubdomains,
  addDynamicSubdomain,
  removeDynamicSubdomain,
} = require("../lib/dynamic-records");
const { getRecords } = require("../lib/record-manager");
const { eventBus, EventTypes, errorHandler, configManager } = require("../shared");

function startHttpApi(port) {
  const app = express();

  app.use(express.json());
  app.use(cors());

  // Error handling middleware
  app.use(async (err, req, res, next) => {
    console.error('DNS API error:', err);

    await eventBus.publish(EventTypes.SYSTEM_ERROR, {
      component: 'dns-server',
      operation: 'http_api',
      error: err.message,
      path: req.path,
      method: req.method
    });

    res.status(500).json({ error: 'Internal server error' });
  });

  //* Routes

  // Health check endpoint
  app.get("/health", (req, res) => {
    res.json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      component: 'dns-server',
      subdomainCount: dynamicSubdomains.size
    });
  });

  app.get("/api/dns/subdomains", async (req, res) => {
    try {
      const result = await errorHandler.executeWithProtection('dns.getSubdomains', async () => {
        return [...dynamicSubdomains.entries()].map(([domain, data]) => ({
          domain,
          ipAddress: data.ipAddress,
          expires: data.expires ? new Date(data.expires).toISOString() : null,
          isPersistent: data.isPersistent,
          isInactive: data.isInactive || false
        }));
      }, {
        component: 'dns-server',
        operation: 'getSubdomains',
        retry: { maxRetries: 2, baseDelay: 100 }
      });

      res.json({ subdomains: result });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  });

  app.post("/api/dns/subdomains", async (req, res) => {
    const { subdomain, domain, ipAddress, ttl, isPersistent, isInactive } = req.body;

    if (!subdomain || !domain || !ipAddress) {
      return res.status(400).json({ error: "Missing required fields" });
    }

    try {
      const domainName = await errorHandler.executeWithProtection('dns.addSubdomain', async () => {
        const result = await addDynamicSubdomain(
          subdomain,
          domain,
          ipAddress,
          ttl,
          isPersistent,
          isInactive
        );

        // Publish DNS record event
        const eventType = isInactive ? EventTypes.DNS_RECORD_UPDATED : EventTypes.DNS_RECORD_CREATED;
        await eventBus.publish(eventType, {
          subdomain,
          domain,
          ipAddress,
          ttl: ttl || configManager.get('dnsServer.ttl'),
          isPersistent: isPersistent || false,
          isInactive: isInactive || false
        });

        return result;
      }, {
        component: 'dns-server',
        operation: 'addSubdomain',
        retry: { maxRetries: 2, baseDelay: 500 }
      });

      res.json({
        success: true,
        domain: domainName,
        isPersistent: isPersistent || false,
        isInactive: isInactive || false
      });
    } catch (error) {
      console.error('Error adding subdomain:', error);
      res.status(500).json({ error: "Failed to add subdomain" });
    }
  });

  app.delete("/api/dns/subdomains/:subdomain", async (req, res) => {
    const { subdomain } = req.params;
    const { domain, type = 'all' } = req.body;

    if (!subdomain) {
      return res.status(400).json({ error: "Missing subdomain parameter" });
    }

    try {
      const removed = await errorHandler.executeWithProtection('dns.removeSubdomain', async () => {
        const result = await removeDynamicSubdomain(subdomain, domain || configManager.get('dnsServer.domain'), type);

        if (result) {
          // Publish DNS record removed event
          await eventBus.publish(EventTypes.DNS_RECORD_REMOVED, {
            subdomain,
            domain: domain || configManager.get('dnsServer.domain')
          });
        }

        return result;
      }, {
        component: 'dns-server',
        operation: 'removeSubdomain',
        retry: { maxRetries: 2, baseDelay: 500 }
      });

      res.status(removed ? 200 : 404).json({ success: removed });
    } catch (error) {
      console.error('Error removing subdomain:', error);
      res.status(500).json({ error: "Failed to remove subdomain" });
    }
  });

  app.delete("/api/dns/subdomains", async (req, res) => {
    const { subdomain, domain, type = 'all' } = req.body;

    if (!subdomain || !domain) {
      return res.status(400).json({ error: "Missing required fields" });
    }

    try {
      const removed = await errorHandler.executeWithProtection('dns.removeSubdomain', async () => {
        const result = await removeDynamicSubdomain(subdomain, domain, type);

        if (result) {
          // Publish DNS record removed event
          await eventBus.publish(EventTypes.DNS_RECORD_REMOVED, {
            subdomain,
            domain
          });
        }

        return result;
      }, {
        component: 'dns-server',
        operation: 'removeSubdomain',
        retry: { maxRetries: 2, baseDelay: 500 }
      });

      res.status(removed ? 200 : 404).json({ success: removed });
    } catch (error) {
      console.error('Error removing subdomain:', error);
      res.status(500).json({ error: "Failed to remove subdomain" });
    }
  });

  app.get("/api/dns/records", async (req, res) => {
    try {
      const records = await errorHandler.executeWithProtection('dns.getRecords', async () => {
        return getRecords();
      }, {
        component: 'dns-server',
        operation: 'getRecords',
        retry: { maxRetries: 2, baseDelay: 100 }
      });

      res.json(records);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  });

  return app.listen(port, async () => {
    console.log(`HTTP API server running on port ${port}`);

    // Publish startup event
    await eventBus.publish(EventTypes.SYSTEM_RECOVERY, {
      component: 'dns-server',
      action: 'http_api_started',
      port,
      message: 'DNS HTTP API server started successfully'
    });
  });
}

module.exports = { startHttpApi };
