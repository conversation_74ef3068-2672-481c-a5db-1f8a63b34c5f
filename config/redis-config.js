const Redis = require('ioredis');

const redis = new Redis({
  host: process.env.REDIS_HOST || 'localhost',
  port: process.env.REDIS_PORT || 6379,
  password: process.env.REDIS_PASSWORD || '',
  db: process.env.REDIS_DB || 0
});

const redisHelpers = {
  async setRecord(domain, record) {
    await redis.set(`dns:${domain}`, JSON.stringify(record));
    if (record.ttl) {
      await redis.expire(`dns:${domain}`, record.ttl);
    }
  },

  async getRecord(domain) {
    const data = await redis.get(`dns:${domain}`);
    return data ? JSON.parse(data) : null;
  },

  async getAllRecords() {
    const keys = await redis.keys('dns:*');
    const records = {};
    for (const key of keys) {
      const domain = key.replace('dns:', '');
      const data = await redis.get(key);
      if (data) {
        records[domain] = JSON.parse(data);
      }
    }
    return records;
  },

  async deleteRecord(domain) {
    await redis.del(`dns:${domain}`);
  }
};

module.exports = { redisHelpers }; 