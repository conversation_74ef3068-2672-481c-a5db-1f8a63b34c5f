{"domains": {"example.com": {"A": ["192.168.1.1"], "AAAA": ["2001:db8::1"], "NS": ["ns1.example.com", "ns2.example.com"], "MX": [{"preference": 10, "exchange": "mail.example.com"}], "TXT": ["v=spf1 include:_spf.example.com ~all"]}, "*.example.com": {"A": ["192.168.1.2"]}, "ide.local": {"A": ["127.0.0.1"], "NS": ["ns1.ide.local"], "SOA": {"mname": "ns1.ide.local", "rname": "admin.ide.local", "serial": 1, "refresh": 3600, "retry": 1800, "expire": 604800, "minttl": 3600}}, "*.ide.local": {"A": ["127.0.0.1"]}, "test-ide.local": {"A": ["127.0.0.1"], "NS": ["ns1.ide.local"], "SOA": {"mname": "ns1.ide.local", "rname": "admin.ide.local", "serial": 1, "refresh": 3600, "retry": 1800, "expire": 604800, "minttl": 3600}}}}