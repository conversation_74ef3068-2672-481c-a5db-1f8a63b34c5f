{"name": "cloud-ide-project", "version": "1.0.0", "description": "Cloud IDE project with containerized development environments", "scripts": {"start": "start-and-test.bat", "start:linux": "./start-and-test.sh", "test": "node test-comprehensive.js", "test:quick": "node test-comprehensive.js --quick", "test:docker": "node reverse-proxy/test-docker-integration.js", "build:images": "build-docker-images.bat", "build:images:linux": "./build-docker-images.sh"}, "author": "owais shaikh", "license": "MIT", "dependencies": {"axios": "^1.9.0", "dockerode": "^4.0.7", "socket.io-client": "^4.8.1"}}