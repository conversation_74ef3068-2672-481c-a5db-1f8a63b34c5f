#!/usr/bin/env node

/**
 * Advanced Performance Testing Suite
 * Specialized tests for container lifecycle, real-time collaboration, and stress testing
 */

const axios = require('axios');
const { performance } = require('perf_hooks');
const { spawn, exec } = require('child_process');
const fs = require('fs').promises;

class AdvancedPerformanceTests {
  constructor() {
    this.results = {
      containerLifecycle: {},
      realTimeLatency: {},
      stressTest: {},
      memoryLeakTest: {},
      concurrencyTest: {}
    };
    
    this.services = {
      containerManager: 'http://localhost:3000',
      dnsServer: 'http://localhost:8053'
    };
  }

  async runAdvancedTests() {
    console.log('🚀 Advanced Performance Testing Suite');
    console.log('====================================\n');

    const testSuites = [
      { name: 'Container Lifecycle Performance', fn: () => this.testContainerLifecycle() },
      { name: 'Real-time Edit Latency', fn: () => this.testRealTimeEditLatency() },
      { name: 'Stress Testing', fn: () => this.testSystemStress() },
      { name: 'Memory Leak Detection', fn: () => this.testMemoryLeaks() },
      { name: 'High Concurrency Testing', fn: () => this.testHighConcurrency() }
    ];

    for (const suite of testSuites) {
      console.log(`📊 Running: ${suite.name}`);
      console.log('─'.repeat(50));
      
      try {
        await suite.fn();
        console.log(`✅ ${suite.name} - COMPLETED\n`);
      } catch (error) {
        console.log(`❌ ${suite.name} - ERROR: ${error.message}\n`);
      }
    }

    await this.generateAdvancedReport();
  }

  async testContainerLifecycle() {
    console.log('  🐳 Testing container lifecycle performance...');
    
    const lifecycleMetrics = {
      creation: [],
      startup: [],
      shutdown: [],
      removal: []
    };

    // Test container creation and lifecycle
    for (let i = 0; i < 5; i++) {
      const containerName = `perf-lifecycle-${Date.now()}-${i}`;
      
      try {
        // Test container creation time
        const createStart = performance.now();
        const createResponse = await axios.post(`${this.services.containerManager}/api/containers`, {
          name: containerName,
          image: 'nginx:alpine',
          ports: { '80/tcp': {} },
          portBindings: { '80/tcp': [{ HostPort: `${8200 + i}`, HostIp: '0.0.0.0' }] }
        }, { timeout: 30000 });
        
        const createEnd = performance.now();
        lifecycleMetrics.creation.push(createEnd - createStart);
        
        if (createResponse.data.containerId) {
          const containerId = createResponse.data.containerId;
          console.log(`    ✅ Container ${i + 1} created in ${(createEnd - createStart).toFixed(2)}ms`);
          
          // Wait a bit for container to start
          await new Promise(resolve => setTimeout(resolve, 2000));
          
          // Test container shutdown time
          const stopStart = performance.now();
          await axios.post(`${this.services.containerManager}/api/containers/${containerId}/stop`, {}, { timeout: 15000 });
          const stopEnd = performance.now();
          lifecycleMetrics.shutdown.push(stopEnd - stopStart);
          
          // Test container removal time
          const removeStart = performance.now();
          await axios.delete(`${this.services.containerManager}/api/containers/${containerId}`, { timeout: 15000 });
          const removeEnd = performance.now();
          lifecycleMetrics.removal.push(removeEnd - removeStart);
          
          console.log(`    ✅ Container ${i + 1} lifecycle completed`);
        }
      } catch (error) {
        console.log(`    ⚠️  Container ${i + 1} lifecycle failed: ${error.message}`);
      }
    }

    // Calculate statistics
    Object.keys(lifecycleMetrics).forEach(phase => {
      if (lifecycleMetrics[phase].length > 0) {
        const stats = this.calculateStats(lifecycleMetrics[phase]);
        this.results.containerLifecycle[phase] = stats;
        console.log(`    📊 ${phase}: avg=${stats.average.toFixed(2)}ms, min=${stats.min.toFixed(2)}ms, max=${stats.max.toFixed(2)}ms`);
      }
    });
  }

  async testRealTimeEditLatency() {
    console.log('  ⚡ Testing real-time edit latency...');
    
    const io = require('socket.io-client');
    const editLatencies = [];
    
    try {
      const socket = io(`${this.services.containerManager}`, {
        timeout: 10000,
        forceNew: true
      });

      await new Promise((resolve, reject) => {
        socket.on('connect', async () => {
          console.log('    📡 Connected to WebSocket for real-time testing...');
          
          // Simulate real-time edit operations
          for (let i = 0; i < 20; i++) {
            const editStart = performance.now();
            
            // Simulate terminal input (real-time edit)
            socket.emit('terminal:input', {
              sessionId: 'test-session',
              data: `echo "Edit operation ${i}"\n`
            });
            
            // Wait for response
            await new Promise((editResolve) => {
              socket.once('terminal:output', () => {
                const editEnd = performance.now();
                editLatencies.push(editEnd - editStart);
                editResolve();
              });
              
              setTimeout(editResolve, 1000); // Timeout after 1 second
            });
            
            // Small delay between edits
            await new Promise(resolve => setTimeout(resolve, 100));
          }
          
          socket.disconnect();
          resolve();
        });
        
        socket.on('connect_error', reject);
        setTimeout(() => reject(new Error('Connection timeout')), 10000);
      });

      if (editLatencies.length > 0) {
        const stats = this.calculateStats(editLatencies);
        this.results.realTimeLatency = stats;
        console.log(`    ✅ Real-time edit latency: avg=${stats.average.toFixed(2)}ms, P95=${stats.p95.toFixed(2)}ms`);
      }
    } catch (error) {
      console.log(`    ⚠️  Real-time edit latency test failed: ${error.message}`);
    }
  }

  async testSystemStress() {
    console.log('  🔥 Running system stress test...');
    
    const stressMetrics = {
      maxConcurrentRequests: 0,
      errorThreshold: 0,
      responseTimeUnderLoad: [],
      throughputUnderStress: 0
    };

    // Gradually increase load until errors occur
    for (let concurrency = 10; concurrency <= 100; concurrency += 10) {
      console.log(`    📈 Testing with ${concurrency} concurrent requests...`);
      
      const promises = [];
      const startTime = Date.now();
      let successCount = 0;
      let errorCount = 0;
      
      for (let i = 0; i < concurrency; i++) {
        promises.push(
          axios.get(`${this.services.containerManager}/health`, { timeout: 5000 })
            .then(() => successCount++)
            .catch(() => errorCount++)
        );
      }
      
      await Promise.all(promises);
      const endTime = Date.now();
      const duration = (endTime - startTime) / 1000;
      const throughput = successCount / duration;
      const errorRate = (errorCount / concurrency) * 100;
      
      console.log(`    📊 Concurrency ${concurrency}: ${throughput.toFixed(2)} req/sec, ${errorRate.toFixed(2)}% errors`);
      
      if (errorRate < 5) {
        stressMetrics.maxConcurrentRequests = concurrency;
        stressMetrics.throughputUnderStress = throughput;
      } else {
        stressMetrics.errorThreshold = concurrency;
        break;
      }
    }

    this.results.stressTest = stressMetrics;
    console.log(`    ✅ Max concurrent requests without errors: ${stressMetrics.maxConcurrentRequests}`);
  }

  async testMemoryLeaks() {
    console.log('  🧠 Testing for memory leaks...');
    
    const memorySnapshots = [];
    const iterations = 50;
    
    for (let i = 0; i < iterations; i++) {
      // Make multiple requests to potentially trigger memory leaks
      await Promise.all([
        axios.get(`${this.services.containerManager}/health`).catch(() => {}),
        axios.get(`${this.services.containerManager}/api/containers`).catch(() => {}),
        axios.get(`${this.services.dnsServer}/health`).catch(() => {})
      ]);
      
      // Take memory snapshot every 10 iterations
      if (i % 10 === 0) {
        const memUsage = process.memoryUsage();
        memorySnapshots.push({
          iteration: i,
          heapUsed: memUsage.heapUsed,
          heapTotal: memUsage.heapTotal,
          external: memUsage.external,
          rss: memUsage.rss
        });
      }
    }

    // Analyze memory trend
    const memoryTrend = this.analyzeMemoryTrend(memorySnapshots);
    this.results.memoryLeakTest = {
      snapshots: memorySnapshots,
      trend: memoryTrend,
      leakDetected: memoryTrend.slope > 1000000 // 1MB increase per 10 iterations
    };

    console.log(`    📊 Memory trend: ${memoryTrend.leakDetected ? '⚠️ Potential leak detected' : '✅ No significant leaks'}`);
  }

  async testHighConcurrency() {
    console.log('  🚀 Testing high concurrency scenarios...');
    
    const concurrencyLevels = [50, 100, 200];
    const concurrencyResults = {};
    
    for (const level of concurrencyLevels) {
      console.log(`    📊 Testing ${level} concurrent users...`);
      
      const startTime = performance.now();
      const promises = [];
      
      for (let i = 0; i < level; i++) {
        promises.push(
          this.simulateUserSession().catch(error => ({ error: error.message }))
        );
      }
      
      const results = await Promise.all(promises);
      const endTime = performance.now();
      
      const successful = results.filter(r => !r.error).length;
      const failed = results.filter(r => r.error).length;
      const totalTime = endTime - startTime;
      
      concurrencyResults[level] = {
        successful,
        failed,
        successRate: (successful / level) * 100,
        totalTime,
        avgTimePerUser: totalTime / level
      };
      
      console.log(`    ✅ ${level} users: ${successful}/${level} successful (${((successful/level)*100).toFixed(2)}%)`);
    }
    
    this.results.concurrencyTest = concurrencyResults;
  }

  async simulateUserSession() {
    // Simulate a typical user session
    await axios.get(`${this.services.containerManager}/health`);
    await axios.get(`${this.services.containerManager}/api/containers`);
    await axios.get(`${this.services.dnsServer}/api/dns/records`);
    return { success: true };
  }

  analyzeMemoryTrend(snapshots) {
    if (snapshots.length < 2) return { slope: 0, leakDetected: false };
    
    // Simple linear regression to detect memory trend
    const n = snapshots.length;
    let sumX = 0, sumY = 0, sumXY = 0, sumXX = 0;
    
    snapshots.forEach((snapshot, index) => {
      sumX += index;
      sumY += snapshot.heapUsed;
      sumXY += index * snapshot.heapUsed;
      sumXX += index * index;
    });
    
    const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
    
    return {
      slope,
      leakDetected: slope > 1000000 // 1MB per iteration threshold
    };
  }

  calculateStats(values) {
    if (values.length === 0) return { average: 0, min: 0, max: 0, median: 0, p95: 0, p99: 0 };
    
    const sorted = values.sort((a, b) => a - b);
    const sum = values.reduce((a, b) => a + b, 0);
    
    return {
      average: sum / values.length,
      min: sorted[0],
      max: sorted[sorted.length - 1],
      median: sorted[Math.floor(sorted.length / 2)],
      p95: sorted[Math.floor(sorted.length * 0.95)],
      p99: sorted[Math.floor(sorted.length * 0.99)]
    };
  }

  async generateAdvancedReport() {
    console.log('📋 Generating Advanced Performance Report...');
    
    const reportData = {
      timestamp: new Date().toISOString(),
      results: this.results,
      summary: this.generateAdvancedSummary()
    };

    // Save JSON report
    const jsonReport = JSON.stringify(reportData, null, 2);
    await fs.writeFile('advanced-performance-report.json', jsonReport);
    
    console.log('✅ Advanced performance report generated:');
    console.log('   📄 advanced-performance-report.json');
    
    this.printAdvancedSummary();
  }

  generateAdvancedSummary() {
    return {
      containerLifecycle: this.results.containerLifecycle,
      realTimePerformance: this.results.realTimeLatency,
      stressTestResults: this.results.stressTest,
      memoryAnalysis: this.results.memoryLeakTest,
      concurrencyLimits: this.results.concurrencyTest
    };
  }

  printAdvancedSummary() {
    console.log('\n📊 Advanced Performance Summary');
    console.log('===============================');
    
    // Container Lifecycle
    if (Object.keys(this.results.containerLifecycle).length > 0) {
      console.log('\n🐳 Container Lifecycle:');
      Object.entries(this.results.containerLifecycle).forEach(([phase, stats]) => {
        console.log(`  ${phase}: ${stats.average?.toFixed(2) || 'N/A'}ms avg`);
      });
    }
    
    // Real-time Performance
    if (this.results.realTimeLatency.average) {
      console.log('\n⚡ Real-time Edit Latency:');
      console.log(`  Average: ${this.results.realTimeLatency.average.toFixed(2)}ms`);
      console.log(`  P95: ${this.results.realTimeLatency.p95.toFixed(2)}ms`);
    }
    
    // Stress Test Results
    if (this.results.stressTest.maxConcurrentRequests) {
      console.log('\n🔥 Stress Test Results:');
      console.log(`  Max Concurrent Requests: ${this.results.stressTest.maxConcurrentRequests}`);
      console.log(`  Throughput Under Stress: ${this.results.stressTest.throughputUnderStress?.toFixed(2) || 'N/A'} req/sec`);
    }
    
    // Memory Analysis
    if (this.results.memoryLeakTest) {
      console.log('\n🧠 Memory Analysis:');
      console.log(`  Memory Leak Detected: ${this.results.memoryLeakTest.leakDetected ? '⚠️ Yes' : '✅ No'}`);
    }
    
    console.log('\n✅ Advanced performance analysis complete!');
  }
}

// Export for use as module
module.exports = AdvancedPerformanceTests;

// Run if called directly
if (require.main === module) {
  const suite = new AdvancedPerformanceTests();
  suite.runAdvancedTests().catch(console.error);
}
