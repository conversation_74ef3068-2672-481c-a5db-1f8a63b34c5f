#!/usr/bin/env node

/**
 * Comprehensive Cloud IDE Testing Suite
 * Single script to validate all components and functionality
 */

const axios = require('axios');
const { spawn, exec } = require('child_process');

class CloudIDETester {
  constructor() {
    this.testResults = [];
    this.services = {
      containerManager: 'http://localhost:3000',
      dnsServer: 'http://localhost:8053',
      redis: 'redis://localhost:6379'
    };
  }

  async runAllTests() {
    console.log('🚀 Cloud IDE Comprehensive Test Suite');
    console.log('=====================================\n');

    const testSuites = [
      { name: 'Prerequisites Check', fn: () => this.testPrerequisites() },
      { name: 'Service Health', fn: () => this.testServiceHealth() },
      { name: 'API Functionality', fn: () => this.testAPIFunctionality() },
      { name: 'Integration Tests', fn: () => this.testIntegration() },
      { name: 'Docker Integration', fn: () => this.testDockerIntegration() },
      { name: 'Error Handling', fn: () => this.testErrorHandling() }
    ];

    for (const suite of testSuites) {
      console.log(`📋 Running: ${suite.name}`);
      console.log('─'.repeat(50));
      
      try {
        await suite.fn();
        console.log(`✅ ${suite.name} - PASSED\n`);
      } catch (error) {
        console.log(`❌ ${suite.name} - FAILED: ${error.message}\n`);
        this.testResults.push({ suite: suite.name, status: 'FAILED', error: error.message });
      }
    }

    this.printSummary();
  }

  async testPrerequisites() {
    // Check Docker
    await this.execCommand('docker --version');
    console.log('  ✅ Docker is installed');

    await this.execCommand('docker info');
    console.log('  ✅ Docker is running');

    // Check required ports
    const ports = [3000, 8053, 6379];
    for (const port of ports) {
      try {
        await axios.get(`http://localhost:${port}`, { timeout: 1000 });
        console.log(`  ✅ Port ${port} is accessible`);
      } catch (error) {
        if (error.code === 'ECONNREFUSED') {
          console.log(`  ⚠️  Port ${port} not responding (service may be down)`);
        }
      }
    }
  }

  async testServiceHealth() {
    // Container Manager Health
    const cmHealth = await axios.get(`${this.services.containerManager}/health`);
    if (cmHealth.data.status === 'healthy') {
      console.log('  ✅ Container Manager is healthy');
    } else {
      throw new Error('Container Manager is not healthy');
    }

    // DNS Server Health  
    const dnsHealth = await axios.get(`${this.services.dnsServer}/health`);
    if (dnsHealth.data.status === 'healthy') {
      console.log('  ✅ DNS Server is healthy');
    } else {
      throw new Error('DNS Server is not healthy');
    }

    // Redis connectivity (via DNS server)
    if (dnsHealth.data.redis && dnsHealth.data.redis.status === 'connected') {
      console.log('  ✅ Redis is connected');
    } else {
      console.log('  ⚠️  Redis status unknown');
    }
  }

  async testAPIFunctionality() {
    // Test Container Manager APIs
    const containers = await axios.get(`${this.services.containerManager}/api/containers`);
    if (Array.isArray(containers.data)) {
      console.log(`  ✅ Container listing API (${containers.data.length} containers)`);
    }

    // Test DNS Server APIs
    const dnsRecords = await axios.get(`${this.services.dnsServer}/api/dns/subdomains`);
    if (dnsRecords.data.subdomains) {
      console.log(`  ✅ DNS records API (${dnsRecords.data.subdomains.length} records)`);
    }

    const allRecords = await axios.get(`${this.services.dnsServer}/api/dns/records`);
    if (allRecords.data) {
      console.log('  ✅ DNS full records API');
    }
  }

  async testIntegration() {
    // Test DNS record creation
    const createDNS = await axios.post(`${this.services.dnsServer}/api/dns/subdomains`, {
      subdomain: 'test-integration',
      domain: 'ide.local',
      ipAddress: '*************',
      ttl: 300,
      isPersistent: false
    });

    if (createDNS.data.success) {
      console.log('  ✅ DNS record creation');
    }

    // Verify DNS record exists
    const verifyDNS = await axios.get(`${this.services.dnsServer}/api/dns/subdomains`);
    const hasRecord = verifyDNS.data.subdomains.some(r => r.domain.includes('test-integration'));
    
    if (hasRecord) {
      console.log('  ✅ DNS record verification');
    }

    // Test container creation (with mock service)
    try {
      const createContainer = await axios.post(`${this.services.containerManager}/api/containers`, {
        name: 'test-integration-container',
        image: 'nginx:alpine',
        ports: { '80/tcp': {} },
        portBindings: { '80/tcp': [{ HostPort: '8090', HostIp: '0.0.0.0' }] },
        labels: {
          'ide.subdomain': 'test-integration',
          'ide.enabled': 'true'
        }
      });

      if (createContainer.data.containerId) {
        console.log('  ✅ Container creation (mock service)');
        
        // Cleanup
        await axios.delete(`${this.services.containerManager}/api/containers/${createContainer.data.containerId}`);
        console.log('  ✅ Container cleanup');
      }
    } catch (error) {
      console.log('  ⚠️  Container creation limited by mock service');
    }

    // Cleanup DNS record
    try {
      await axios.delete(`${this.services.dnsServer}/api/dns/subdomains`, {
        data: { subdomain: 'test-integration', domain: 'ide.local' }
      });
      console.log('  ✅ DNS record cleanup');
    } catch (error) {
      console.log('  ⚠️  DNS cleanup failed (record may not exist)');
    }
  }

  async testDockerIntegration() {
    // Run the existing Docker integration test
    try {
      await this.execCommand('node reverse-proxy/test-docker-integration.js', { timeout: 120000 });
      console.log('  ✅ Docker integration test suite passed');
    } catch (error) {
      throw new Error(`Docker integration test failed: ${error.message}`);
    }
  }

  async testErrorHandling() {
    // Test invalid DNS record
    try {
      await axios.post(`${this.services.dnsServer}/api/dns/subdomains`, {
        subdomain: '',  // Invalid
        domain: 'ide.local',
        ipAddress: '*************'
      });
    } catch (error) {
      if (error.response && error.response.status === 400) {
        console.log('  ✅ DNS API error handling');
      }
    }

    // Test invalid container creation
    try {
      await axios.post(`${this.services.containerManager}/api/containers`, {
        name: 'invalid-test',
        image: 'nginx:alpine'
        // Missing required fields
      });
    } catch (error) {
      if (error.response && error.response.status === 400) {
        console.log('  ✅ Container API error handling');
      }
    }

    // Test non-existent endpoints
    try {
      await axios.get(`${this.services.containerManager}/api/nonexistent`);
    } catch (error) {
      if (error.response && error.response.status === 404) {
        console.log('  ✅ 404 error handling');
      }
    }
  }

  async execCommand(command, options = {}) {
    return new Promise((resolve, reject) => {
      const timeout = options.timeout || 30000;
      
      exec(command, (error, stdout, stderr) => {
        if (error) {
          reject(new Error(`${error.message}\n${stderr}`));
        } else {
          resolve(stdout);
        }
      });
    });
  }

  printSummary() {
    console.log('📊 Test Summary');
    console.log('===============');
    
    const failed = this.testResults.filter(r => r.status === 'FAILED');
    
    if (failed.length === 0) {
      console.log('🎉 All tests passed! Your Cloud IDE is working correctly.');
    } else {
      console.log(`❌ ${failed.length} test suite(s) failed:`);
      failed.forEach(test => {
        console.log(`  - ${test.suite}: ${test.error}`);
      });
    }
  }
}

// Run tests
if (require.main === module) {
  const tester = new CloudIDETester();
  tester.runAllTests().catch(console.error);
}

module.exports = CloudIDETester;

// Add CLI options
if (require.main === module) {
  const args = process.argv.slice(2);

  if (args.includes('--help') || args.includes('-h')) {
    console.log(`
🚀 Cloud IDE Comprehensive Test Suite

Usage: node test-comprehensive.js [options]

Options:
  --help, -h     Show this help message
  --quick, -q    Run only basic health checks
  --docker, -d   Run only Docker integration tests
  --api, -a      Run only API functionality tests

Examples:
  node test-comprehensive.js           # Run all tests
  node test-comprehensive.js --quick   # Quick health check
  node test-comprehensive.js --docker  # Docker tests only
    `);
    process.exit(0);
  }

  const tester = new CloudIDETester();

  if (args.includes('--quick') || args.includes('-q')) {
    console.log('🚀 Quick Health Check');
    console.log('====================\n');
    Promise.all([
      tester.testPrerequisites(),
      tester.testServiceHealth()
    ]).then(() => {
      console.log('✅ Quick health check passed!');
    }).catch(console.error);
  } else if (args.includes('--docker') || args.includes('-d')) {
    console.log('🐳 Docker Integration Tests');
    console.log('===========================\n');
    tester.testDockerIntegration().catch(console.error);
  } else if (args.includes('--api') || args.includes('-a')) {
    console.log('🔌 API Functionality Tests');
    console.log('==========================\n');
    tester.testAPIFunctionality().catch(console.error);
  } else {
    tester.runAllTests().catch(console.error);
  }
}
