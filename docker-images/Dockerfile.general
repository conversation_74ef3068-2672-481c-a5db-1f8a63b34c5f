FROM alpine:latest

# Install essential packages for development
RUN apk add --no-cache \
    bash \
    git \
    curl \
    vim \
    nano \
    wget \
    unzip \
    tar \
    build-base \
    && rm -rf /var/cache/apk/*

# Create workspace directory
WORKDIR /workspace

# Create a non-root user
RUN addgroup -g 1000 developer && \
    adduser -D -s /bin/bash -u 1000 -G developer developer

# Set up proper permissions
RUN chown -R developer:developer /workspace

# Switch to non-root user
USER developer

# Keep container running
CMD ["tail", "-f", "/dev/null"]
