FROM node:18-alpine

# Install essential packages for development
RUN apk add --no-cache \
    bash \
    git \
    curl \
    nano \
    && rm -rf /var/cache/apk/*

# Install global npm packages
RUN npm install -g \
    nodemon \
    pm2 \
    typescript \
    ts-node

# Create workspace directory
WORKDIR /workspace

# Create developer user with same UID/GID as node user for compatibility
RUN addgroup -g 1001 developer && \
    adduser -D -s /bin/bash -u 1001 -G developer developer

# Set up proper permissions for both users
RUN chown -R developer:developer /workspace && \
    chown -R node:node /home/<USER>

# Switch to developer user
USER developer

# Keep container running
CMD ["tail", "-f", "/dev/null"]
