FROM python:3.11-alpine

# Install essential packages for development
RUN apk add --no-cache \
    bash \
    git \
    curl \
    vim \
    nano \
    build-base \
    linux-headers \
    && rm -rf /var/cache/apk/*

# Install common Python packages
RUN pip install --no-cache-dir \
    requests \
    flask \
    fastapi \
    uvicorn \
    numpy \
    pandas \
    matplotlib \
    jupyter

# Create workspace directory
WORKDIR /workspace

# Create a non-root user
RUN addgroup -g 1000 developer && \
    adduser -D -s /bin/bash -u 1000 -G developer developer

# Set up proper permissions
RUN chown -R developer:developer /workspace

# Switch to non-root user
USER developer

# Keep container running
CMD ["tail", "-f", "/dev/null"]
