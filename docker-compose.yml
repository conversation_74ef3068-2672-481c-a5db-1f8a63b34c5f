version: '3.8'

services:
  nginx:
    build: ./nginx
    container_name: nginx
    ports:
      - "80:80"
    volumes:
      - ./nginx/conf.d:/etc/nginx/conf.d
      - ./nginx/manual-templates:/etc/nginx/manual-templates
    depends_on:
      - config-manager
    networks:
      - ide-network

  config-manager:
    build: ./config-manager
    container_name: config-manager
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - ./nginx/conf.d:/etc/nginx/conf.d
      - ./nginx/manual-templates:/etc/nginx/manual-templates
    environment:
      - DNS_API_URL=http://dns-server:8053
      - DNS_DOMAIN=ide.local
      - DNS_TTL=3600
      - DNS_PERSISTENT=true
      - NODE_ENV=production
    depends_on:
      - dns-server
    networks:
      - ide-network

  dns-server:
    build: ../dns-server
    container_name: dns-server
    ports:
      - "8053:8053"      # HTTP API
      - "5354:5354/udp"  # DNS over UDP
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=
      - REDIS_DB=0
      - NODE_ENV=production
    depends_on:
      - redis
    networks:
      - ide-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--spider", "http://localhost:8053/api/dns/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  redis:
    image: redis:alpine
    container_name: redis
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    command: redis-server --appendonly yes
    networks:
      - ide-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  test-app:
    build: ./test-app
    container_name: test-app
    labels:
      ide.subdomain: testuser
    networks:
      - ide-network

networks:
  ide-network:
    driver: bridge

volumes:
  redis-data:
    driver: local
