@echo off
setlocal enabledelayedexpansion

REM Cloud IDE - Start and Test Script (Windows)
REM Simple script to start services and validate everything works

echo 🚀 Cloud IDE - Start and Test
echo =============================
echo.

REM Step 1: Check prerequisites
echo 📋 Step 1: Checking Prerequisites
echo --------------------------------

REM Check Docker
docker --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker is not installed
    exit /b 1
)

docker info >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker is not running
    exit /b 1
)

echo ✅ Docker is installed and running

REM Check Node.js
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js is not installed
    exit /b 1
)

for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
echo ✅ Node.js is installed (!NODE_VERSION!)
echo.

REM Step 2: Start services
echo 📋 Step 2: Starting Services
echo ----------------------------

REM Use simplified docker-compose if it exists
if exist "docker-compose.simplified.yml" (
    echo 🐳 Starting services with simplified configuration...
    docker-compose -f docker-compose.simplified.yml up -d
) else (
    echo 🐳 Starting core services...
    docker-compose up -d redis dns-server container-manager
)

echo.

REM Step 3: Wait for services
echo 📋 Step 3: Waiting for Services
echo -------------------------------

echo ⏳ Waiting for services to be ready...
timeout /t 10 /nobreak >nul

REM Check Redis (indirect check via DNS server)
echo ⏳ Checking Redis...
timeout /t 5 /nobreak >nul

REM Check DNS Server
echo ⏳ Checking DNS Server...
for /l %%i in (1,1,15) do (
    powershell -Command "try { Invoke-WebRequest -Uri 'http://localhost:8053/health' -UseBasicParsing -TimeoutSec 2 | Out-Null; exit 0 } catch { exit 1 }" >nul 2>&1
    if not errorlevel 1 (
        echo ✅ DNS Server is ready!
        goto dns_ready
    )
    echo    Attempt %%i/15 - waiting...
    timeout /t 2 /nobreak >nul
)
echo ❌ DNS Server failed to start
exit /b 1
:dns_ready

REM Check Container Manager
echo ⏳ Checking Container Manager...
for /l %%i in (1,1,15) do (
    powershell -Command "try { Invoke-WebRequest -Uri 'http://localhost:3000/health' -UseBasicParsing -TimeoutSec 2 | Out-Null; exit 0 } catch { exit 1 }" >nul 2>&1
    if not errorlevel 1 (
        echo ✅ Container Manager is ready!
        goto cm_ready
    )
    echo    Attempt %%i/15 - waiting...
    timeout /t 2 /nobreak >nul
)
echo ❌ Container Manager failed to start
exit /b 1
:cm_ready

echo.

REM Step 4: Run validation tests
echo 📋 Step 4: Running Validation Tests
echo -----------------------------------

if exist "test-comprehensive.js" (
    echo 🧪 Running comprehensive test suite...
    node test-comprehensive.js --quick
) else (
    echo 🧪 Running Docker integration tests...
    cd reverse-proxy
    node test-docker-integration.js
    cd ..
)

echo.

REM Step 5: Show status
echo 📋 Step 5: System Status
echo ------------------------

echo 🐳 Docker Services:
docker-compose ps

echo.
echo 🌐 Service URLs:
echo   • Container Manager: http://localhost:3000/health
echo   • DNS Server API:    http://localhost:8053/health
echo   • DNS Records:       http://localhost:8053/api/dns/subdomains
echo   • Container API:     http://localhost:3000/api/containers

echo.
echo 🎉 Cloud IDE is ready!
echo.
echo 📚 Next Steps:
echo   • Run full tests:    node test-comprehensive.js
echo   • Check logs:        docker-compose logs -f
echo   • Stop services:     docker-compose down
echo.
