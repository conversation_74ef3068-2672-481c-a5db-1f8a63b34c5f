const path = require("path");
const { startDnsUdpServer } = require("./server/dns-server");
const { startHttpApi } = require("./api/http-api");
const { loadRecords } = require("./lib/record-manager");
const { cleanupExpiredSubdomains } = require("./lib/dynamic-records");
const { configManager, integrationService, eventBus, EventTypes } = require("./shared");

async function startServer() {
  try {
    // Initialize shared services first
    console.log('Initializing shared services...');
    await configManager.initialize();
    await integrationService.initialize();

    // Get configuration
    const config = configManager.getAll();
    const DNS_PORT = process.env.DNS_PORT || config.dnsServer.port;
    const API_PORT = process.env.DNS_API_PORT || config.dnsServer.apiPort;
    const RECORDS_PATH = path.join(__dirname, "config", "dns-records.json");

    // Load DNS records
    await loadRecords(RECORDS_PATH);

    // Start DNS servers
    const dnsServer = startDnsUdpServer(DNS_PORT);
    const apiServer = startHttpApi(API_PORT);

    // Run cleanup based on configuration
    const cleanupInterval = config.dnsServer.cleanupInterval;
    setInterval(cleanupExpiredSubdomains, cleanupInterval);

    console.log(`DNS server started successfully:`);
    console.log(`- UDP server on port ${DNS_PORT}`);
    console.log(`- HTTP API on port ${API_PORT}`);
    console.log(`- Cleanup interval: ${cleanupInterval}ms`);

    // Publish startup complete event
    await eventBus.publish(EventTypes.SYSTEM_RECOVERY, {
      component: 'dns-server',
      action: 'startup_complete',
      ports: { udp: DNS_PORT, http: API_PORT },
      message: 'DNS server started successfully'
    });

    // Graceful shutdown handling
    const gracefulShutdown = async (signal) => {
      console.log(`Received ${signal}. Starting graceful shutdown...`);

      // Publish shutdown event
      await eventBus.publish(EventTypes.SYSTEM_ERROR, {
        component: 'dns-server',
        action: 'shutdown',
        signal
      });

      // Close servers
      dnsServer.close();
      apiServer.close();

      console.log("DNS server shut down gracefully 🫡");
      process.exit(0);
    };

    process.on("SIGINT", () => gracefulShutdown('SIGINT'));
    process.on("SIGTERM", () => gracefulShutdown('SIGTERM'));

  } catch (error) {
    console.error("Failed to start DNS server:", error);

    // Publish startup failure event
    await eventBus.publish(EventTypes.SYSTEM_ERROR, {
      component: 'dns-server',
      action: 'startup_failed',
      error: error.message
    });

    process.exit(1);
  }
}

startServer();
