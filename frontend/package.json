{"name": "cloud-ide-frontend", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@headlessui/react": "^2.1.10", "@heroicons/react": "^2.1.5", "@monaco-editor/react": "^4.6.0", "@xterm/addon-fit": "^0.10.0", "@xterm/addon-web-links": "^0.11.0", "@xterm/xterm": "^5.5.0", "axios": "^1.9.0", "chart.js": "^4.4.9", "clsx": "^2.1.1", "gsap": "^3.13.0", "lucide-react": "^0.460.0", "react": "^18.3.1", "react-chartjs-2": "^5.3.0", "react-dom": "^18.3.1", "react-resizable-panels": "^2.1.4", "react-router-dom": "^6.28.0", "socket.io-client": "^4.8.1", "tailwind-merge": "^2.5.4", "zustand": "^5.0.1"}, "devDependencies": {"@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.3", "autoprefixer": "^10.4.20", "eslint": "^9.15.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "postcss": "^8.4.49", "tailwindcss": "^3.4.14", "vite": "^5.4.10"}}