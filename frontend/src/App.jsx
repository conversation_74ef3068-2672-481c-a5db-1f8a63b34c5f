import React, { useEffect } from 'react'
import { Routes, Route } from 'react-router-dom'
import Landing from './pages/Landing'
import Dashboard from './pages/Dashboard'
import IDE from './pages/IDE'
import Settings from './pages/Settings'
import CollaborationTest from './components/Collaboration/CollaborationTest'
import CursorTest from './components/Collaboration/CursorTest'
import { useAppStore } from './stores/appStore'
import { applyTheme } from './config/themes'

function App() {
  const { theme } = useAppStore()

  useEffect(() => {
    // Apply theme on app initialization
    applyTheme(theme)
  }, [theme])

  return (
    <div className="min-h-screen">
      <Routes>
        <Route path="/" element={<Landing />} />
        <Route path="/dashboard" element={<Dashboard />} />
        <Route path="/ide" element={<IDE />} />
        <Route path="/ide/:projectId" element={<IDE />} />
        <Route path="/settings" element={<Settings />} />
        <Route path="/test/collaboration" element={<CollaborationTest />} />
        <Route path="/test/cursors" element={<CursorTest />} />
      </Routes>
    </div>
  )
}

export default App
