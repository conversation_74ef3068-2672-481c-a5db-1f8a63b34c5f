@tailwind base;
@tailwind components;
@tailwind utilities;

/* Default theme variables (GitHub Dark) */
:root {
  --ide-bg: #0d1117;
  --ide-surface: #161b22;
  --ide-surface-hover: #21262d;
  --ide-border: #30363d;
  --ide-border-hover: #484f58;
  --ide-text: #f0f6fc;
  --ide-text-muted: #8b949e;
  --ide-accent: #58a6ff;
  --ide-accent-hover: #1f6feb;
  --ide-success: #3fb950;
  --ide-warning: #d29922;
  --ide-error: #f85149;
  --ide-primary: #238636;
  --ide-secondary: #6e7681;
  --ide-highlight: #ffd33d;

  /* RGB versions for rgba() usage (default GitHub Dark) */
  --ide-bg-rgb: 13, 17, 23;
  --ide-surface-rgb: 22, 27, 34;
  --ide-surface-hover-rgb: 33, 38, 45;
  --ide-border-rgb: 48, 54, 61;
  --ide-accent-rgb: 88, 166, 255;

  /* Enhanced theme-adaptive variables */
  --ide-glass-bg: rgba(255, 255, 255, 0.08);
  --ide-glass-border: rgba(255, 255, 255, 0.15);
  --ide-glass-hover-bg: rgba(255, 255, 255, 0.12);
  --ide-glass-hover-border: rgba(255, 255, 255, 0.25);
  --ide-shadow-light: rgba(0, 0, 0, 0.1);
  --ide-shadow-medium: rgba(0, 0, 0, 0.15);
  --ide-shadow-heavy: rgba(0, 0, 0, 0.25);
}

@layer base {
  * {
    @apply border-ide-border;
  }

  body {
    @apply bg-ide-bg text-ide-text;
    font-feature-settings: 'liga' 1, 'calt' 1;
    transition: background-color 0.3s ease, color 0.3s ease;
  }
  
  /* Custom scrollbar */
  ::-webkit-scrollbar {
    @apply w-2 h-2;
  }
  
  ::-webkit-scrollbar-track {
    @apply bg-ide-surface;
  }
  
  ::-webkit-scrollbar-thumb {
    @apply bg-ide-border rounded-full;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    @apply bg-ide-text-muted;
  }
}

@layer components {
  .ide-panel {
    @apply bg-ide-surface border border-ide-border rounded-lg transition-all duration-200;
    backdrop-filter: blur(10px);
  }

  .ide-panel-hover {
    @apply hover:bg-ide-surface-hover hover:border-ide-border-hover hover:shadow-lg;
  }

  .ide-button {
    @apply px-3 py-1.5 bg-ide-surface hover:bg-ide-surface-hover text-ide-text rounded-md transition-all duration-200 border border-ide-border;
  }

  .ide-button-primary {
    @apply bg-ide-accent hover:bg-ide-accent-hover text-white border-ide-accent shadow-lg hover:shadow-xl;
  }

  .ide-button-ghost {
    @apply bg-transparent hover:bg-ide-surface-hover border-transparent;
  }

  .ide-input {
    @apply bg-ide-surface border border-ide-border rounded-md px-3 py-2 text-ide-text placeholder-ide-text-muted focus:outline-none focus:ring-2 focus:ring-ide-accent focus:border-transparent transition-all duration-200;
  }

  .ide-input:focus {
    @apply shadow-lg;
  }

  .ide-tab {
    @apply px-4 py-2.5 text-sm text-ide-text-muted hover:text-ide-text transition-all duration-200 relative;
    min-width: 120px;
    max-width: 200px;
    background: rgba(var(--ide-surface-rgb, 22, 27, 34), 0.4);
    border: 1px solid rgba(var(--ide-border-rgb, 48, 54, 61), 0.3);
    border-bottom: none;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    margin-right: 2px;
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .ide-tab:hover {
    background: rgba(var(--ide-surface-hover-rgb, 33, 38, 45), 0.6);
    border-color: rgba(var(--ide-border-rgb, 48, 54, 61), 0.5);
    transform: translateY(-1px);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
  }

  .ide-tab:last-child {
    margin-right: 0;
  }

  .ide-tab-active {
    @apply text-ide-text;
    background: rgba(var(--ide-bg-rgb, 13, 17, 23), 0.95);
    border-color: rgba(var(--ide-accent-rgb, 88, 166, 255), 0.4);
    position: relative;
    z-index: 2;
    transform: translateY(-1px);
    box-shadow:
      0 3px 8px rgba(0, 0, 0, 0.2),
      0 1px 3px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.08);
  }

  .ide-tab-active::after {
    content: '';
    @apply absolute top-0 left-0 right-0 h-0.5 bg-ide-accent;
    border-radius: 8px 8px 0 0;
  }

  /* Tab container styling for rounded panels */
  .ide-tabs-container {
    background: rgba(var(--ide-surface-rgb, 22, 27, 34), 0.6);
    border-bottom: 1px solid rgba(var(--ide-border-rgb, 48, 54, 61), 0.4);
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    padding: 4px 8px 0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  /* Modern scrollbar */
  .ide-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: var(--ide-border) var(--ide-surface);
  }

  .ide-scrollbar::-webkit-scrollbar {
    @apply w-2 h-2;
  }

  .ide-scrollbar::-webkit-scrollbar-track {
    @apply bg-ide-surface rounded-full;
  }

  .ide-scrollbar::-webkit-scrollbar-thumb {
    @apply bg-ide-border rounded-full hover:bg-ide-border-hover;
    transition: background-color 0.2s ease;
  }

  /* Loading skeleton */
  .ide-skeleton {
    @apply bg-ide-surface animate-pulse rounded;
    background: linear-gradient(90deg, var(--ide-surface) 25%, var(--ide-surface-hover) 50%, var(--ide-surface) 75%);
    background-size: 200% 100%;
    animation: shimmer 2s infinite;
  }

  /* Glow effect */
  .ide-glow {
    box-shadow: 0 0 10px var(--ide-accent);
    transition: box-shadow 0.3s ease;
  }

  .ide-glow:hover {
    box-shadow: 0 0 20px var(--ide-accent), 0 0 30px var(--ide-accent);
  }

  /* Advanced animations */
  .animate-fade-in-up {
    animation: fadeInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  }

  .animate-slide-in {
    animation: slideIn 0.8s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  }

  .animate-pulse-glow {
    animation: pulseGlow 2s ease-in-out infinite;
  }

  .animate-bounce-in {
    animation: bounceIn 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55) forwards;
  }

  .animate-scale-in {
    animation: scaleIn 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  }

  .animate-slide-up {
    animation: slideUp 0.5s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  }

  /* Modern glass morphism card */
  .modern-glass-card {
    backdrop-filter: blur(24px) saturate(180%);
    -webkit-backdrop-filter: blur(24px) saturate(180%);
    background: linear-gradient(135deg,
      rgba(255, 255, 255, 0.12) 0%,
      rgba(255, 255, 255, 0.08) 30%,
      rgba(255, 255, 255, 0.04) 70%,
      rgba(255, 255, 255, 0.02) 100%);
    border: 1px solid rgba(255, 255, 255, 0.15);
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.12),
      0 2px 8px rgba(0, 0, 0, 0.08),
      inset 0 1px 0 rgba(255, 255, 255, 0.15),
      inset 0 -1px 0 rgba(255, 255, 255, 0.05);
    position: relative;
    overflow: hidden;
  }

  .modern-glass-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg,
      transparent 0%,
      rgba(255, 255, 255, 0.3) 50%,
      transparent 100%);
  }

  .modern-glass-card:hover {
    background: linear-gradient(135deg,
      rgba(255, 255, 255, 0.18) 0%,
      rgba(255, 255, 255, 0.12) 30%,
      rgba(255, 255, 255, 0.08) 70%,
      rgba(255, 255, 255, 0.04) 100%);
    border: 1px solid rgba(255, 255, 255, 0.25);
    box-shadow:
      0 16px 48px rgba(0, 0, 0, 0.15),
      0 4px 16px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.25),
      inset 0 -1px 0 rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
  }

  /* Optimized Interactive Cards */
  .interactive-card {
    position: relative;
    background: linear-gradient(135deg,
      rgba(255, 255, 255, 0.05) 0%,
      rgba(255, 255, 255, 0.02) 50%,
      rgba(255, 255, 255, 0.01) 100%);
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(12px); /* Reduced blur for better performance */
    -webkit-backdrop-filter: blur(12px);
    transition: transform 0.2s ease, border-color 0.2s ease, background-color 0.2s ease;
    overflow: hidden;
    will-change: transform;
  }

  .interactive-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(600px circle at var(--mouse-x, 50%) var(--mouse-y, 50%),
      rgba(255, 255, 255, 0.06) 0%,
      transparent 40%);
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
  }

  .interactive-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(300px circle at var(--mouse-x, 50%) var(--mouse-y, 50%),
      rgba(255, 255, 255, 0.1) 0%,
      transparent 50%);
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
  }

  .interactive-card:hover::before {
    opacity: 1;
  }

  .interactive-card:hover::after {
    opacity: 0.7;
  }

  .interactive-card:hover {
    transform: translateY(-2px);
    border-color: rgba(255, 255, 255, 0.2);
    background: linear-gradient(135deg,
      rgba(255, 255, 255, 0.08) 0%,
      rgba(255, 255, 255, 0.04) 50%,
      rgba(255, 255, 255, 0.02) 100%);
  }

  /* Color-specific interactive cards */
  .interactive-card[data-color="success"]:hover {
    border-color: rgba(63, 185, 80, 0.3);
  }

  .interactive-card[data-color="success"]::before {
    background: radial-gradient(600px circle at var(--mouse-x, 50%) var(--mouse-y, 50%),
      rgba(63, 185, 80, 0.08) 0%,
      transparent 40%);
  }

  .interactive-card[data-color="accent"]:hover {
    border-color: rgba(88, 166, 255, 0.3);
  }

  .interactive-card[data-color="accent"]::before {
    background: radial-gradient(600px circle at var(--mouse-x, 50%) var(--mouse-y, 50%),
      rgba(88, 166, 255, 0.08) 0%,
      transparent 40%);
  }

  .interactive-card[data-color="warning"]:hover {
    border-color: rgba(210, 153, 34, 0.3);
  }

  .interactive-card[data-color="warning"]::before {
    background: radial-gradient(600px circle at var(--mouse-x, 50%) var(--mouse-y, 50%),
      rgba(210, 153, 34, 0.08) 0%,
      transparent 40%);
  }

  .interactive-card[data-color="error"]:hover {
    border-color: rgba(248, 81, 73, 0.3);
  }

  .interactive-card[data-color="error"]::before {
    background: radial-gradient(600px circle at var(--mouse-x, 50%) var(--mouse-y, 50%),
      rgba(248, 81, 73, 0.08) 0%,
      transparent 40%);
  }

  .interactive-card[data-color="purple"]:hover {
    border-color: rgba(168, 85, 247, 0.3);
  }

  .interactive-card[data-color="purple"]::before {
    background: radial-gradient(600px circle at var(--mouse-x, 50%) var(--mouse-y, 50%),
      rgba(168, 85, 247, 0.08) 0%,
      transparent 40%);
  }

  /* Optimized transitions - only animate necessary properties */
  .interactive-card,
  .ide-button,
  .modern-button-primary,
  .modern-button-secondary {
    transition: transform 0.2s ease, opacity 0.2s ease, background-color 0.2s ease, border-color 0.2s ease;
    will-change: transform, opacity;
  }

  /* Reduce global transitions for better performance */
  button, a, input, select, textarea {
    transition: background-color 0.15s ease, border-color 0.15s ease, color 0.15s ease;
  }

  /* Disable transitions during theme change */
  .theme-changing * {
    transition: none !important;
  }

  /* Responsive improvements */
  @media (max-width: 768px) {
    .interactive-card {
      padding: 1rem;
    }
  }

  /* Animated Graph Effects */
  .animate-graph-line {
    animation: graphLine 3s ease-in-out infinite;
  }

  @keyframes graphLine {
    0%, 100% {
      stroke-dasharray: 0, 1000;
    }
    50% {
      stroke-dasharray: 1000, 0;
    }
  }

  .animate-graph-fill {
    animation: graphFill 4s ease-in-out infinite;
  }

  @keyframes graphFill {
    0%, 100% {
      opacity: 0.3;
    }
    50% {
      opacity: 0.6;
    }
  }

  /* Realtime data animation */
  .realtime-graph {
    animation: realtimeData 2s ease-in-out infinite;
  }

  @keyframes realtimeData {
    0% {
      transform: translateX(0);
    }
    100% {
      transform: translateX(-2px);
    }
  }

  /* Performance optimizations for low-end devices */
  .reduced-animations .interactive-card {
    backdrop-filter: none;
    -webkit-backdrop-filter: none;
    transition: background-color 0.1s ease;
  }

  .reduced-animations .interactive-card::before,
  .reduced-animations .interactive-card::after {
    display: none;
  }

  .reduced-animations .interactive-card:hover {
    transform: none;
  }

  .reduced-animations .animate-fade-in-up,
  .reduced-animations .animate-scale-in,
  .reduced-animations .animate-bounce-in {
    animation: none;
    opacity: 1;
    transform: none;
  }

  /* Accessibility improvements */
  @media (prefers-reduced-motion: reduce) {
    .interactive-card {
      transition: background-color 0.1s ease;
      backdrop-filter: none;
      -webkit-backdrop-filter: none;
    }

    .interactive-card:hover {
      transform: none;
    }

    .interactive-card::before,
    .interactive-card::after {
      display: none;
    }

    .animate-graph-line,
    .animate-graph-fill,
    .realtime-graph,
    .animate-fade-in-up,
    .animate-scale-in,
    .animate-bounce-in {
      animation: none;
    }
  }

  /* Modern button styles */
  .modern-button-primary {
    position: relative;
    overflow: hidden;
    background: linear-gradient(135deg, var(--ide-accent), var(--ide-success));
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow:
      0 4px 16px rgba(88, 166, 255, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
  }

  /* Enhanced Landing Page Animations */
  @keyframes float-complex {
    0%, 100% {
      transform: translateY(0px) rotate(0deg) scale(1);
    }
    25% {
      transform: translateY(-10px) rotate(2deg) scale(1.02);
    }
    50% {
      transform: translateY(-5px) rotate(-1deg) scale(0.98);
    }
    75% {
      transform: translateY(-15px) rotate(3deg) scale(1.01);
    }
  }

  @keyframes glow-pulse {
    0%, 100% {
      box-shadow: 0 0 20px rgba(88, 166, 255, 0.3);
    }
    50% {
      box-shadow: 0 0 40px rgba(88, 166, 255, 0.6), 0 0 60px rgba(147, 51, 234, 0.3);
    }
  }

  @keyframes gradient-shift {
    0% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
    100% {
      background-position: 0% 50%;
    }
  }

  @keyframes star-twinkle {
    0%, 100% {
      opacity: 0.3;
      transform: scale(1);
    }
    50% {
      opacity: 1;
      transform: scale(1.2);
    }
  }

  .animate-float-complex {
    animation: float-complex 6s ease-in-out infinite;
  }

  .animate-glow-pulse {
    animation: glow-pulse 3s ease-in-out infinite;
  }

  .animate-gradient-shift {
    background-size: 200% 200%;
    animation: gradient-shift 8s ease infinite;
  }

  .animate-star-twinkle {
    animation: star-twinkle 2s ease-in-out infinite;
  }

  /* Ultra-smooth transitions */
  @keyframes ultra-smooth-float {
    0%, 100% {
      transform: translateY(0px) translateX(0px) rotate(0deg);
    }
    25% {
      transform: translateY(-8px) translateX(4px) rotate(1deg);
    }
    50% {
      transform: translateY(-4px) translateX(-2px) rotate(-0.5deg);
    }
    75% {
      transform: translateY(-12px) translateX(6px) rotate(1.5deg);
    }
  }

  @keyframes gentle-pulse {
    0%, 100% {
      opacity: 0.6;
      transform: scale(1);
    }
    50% {
      opacity: 1;
      transform: scale(1.05);
    }
  }

  @keyframes fluid-bounce {
    0% {
      transform: translateY(0px);
      animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
    }
    50% {
      transform: translateY(-8px);
      animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
    }
    100% {
      transform: translateY(0px);
      animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
    }
  }

  .animate-ultra-smooth-float {
    animation: ultra-smooth-float 8s ease-in-out infinite;
  }

  .animate-gentle-pulse {
    animation: gentle-pulse 4s ease-in-out infinite;
  }

  .animate-fluid-bounce {
    animation: fluid-bounce 2s ease-in-out infinite;
  }

  /* Smooth scroll behavior */
  html {
    scroll-behavior: smooth;
  }

  /* Enhanced transitions for all interactive elements */
  * {
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  }

  .modern-button-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .modern-button-primary:hover {
    background: linear-gradient(135deg, var(--ide-accent-hover), var(--ide-success));
    box-shadow:
      0 8px 24px rgba(88, 166, 255, 0.4),
      0 4px 12px rgba(59, 130, 246, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
  }

  .modern-button-primary:hover::before {
    left: 100%;
  }

  .modern-button-secondary {
    position: relative;
    background: linear-gradient(135deg, rgba(88, 166, 255, 0.15), rgba(88, 166, 255, 0.08));
    border: 1px solid rgba(88, 166, 255, 0.2);
    backdrop-filter: blur(12px);
    box-shadow:
      0 2px 8px rgba(88, 166, 255, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
  }

  .modern-button-secondary:hover {
    background: linear-gradient(135deg, rgba(88, 166, 255, 0.25), rgba(88, 166, 255, 0.15));
    border: 1px solid rgba(88, 166, 255, 0.4);
    box-shadow:
      0 4px 16px rgba(88, 166, 255, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
  }

  .modern-button-danger {
    position: relative;
    background: linear-gradient(135deg, rgba(248, 81, 73, 0.15), rgba(248, 81, 73, 0.08));
    border: 1px solid rgba(248, 81, 73, 0.2);
    backdrop-filter: blur(12px);
    box-shadow:
      0 2px 8px rgba(248, 81, 73, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
  }

  .modern-button-danger:hover {
    background: linear-gradient(135deg, rgba(248, 81, 73, 0.25), rgba(248, 81, 73, 0.15));
    border: 1px solid rgba(248, 81, 73, 0.4);
    box-shadow:
      0 4px 16px rgba(248, 81, 73, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
  }

  .modern-dropdown-trigger {
    backdrop-filter: blur(16px) saturate(180%);
    -webkit-backdrop-filter: blur(16px) saturate(180%);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow:
      0 2px 8px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .modern-dropdown-trigger:hover {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.08));
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow:
      0 4px 16px rgba(0, 0, 0, 0.15),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
  }

  /* Enhanced glass morphism */
  .glass-morphism {
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  /* Modern IDE Header with theme-adaptive blur */
  .ide-header-blur {
    backdrop-filter: blur(25px) saturate(200%);
    -webkit-backdrop-filter: blur(25px) saturate(200%);
    background: linear-gradient(135deg,
      rgba(var(--ide-surface-rgb, 22, 27, 34), 0.95) 0%,
      rgba(var(--ide-surface-rgb, 22, 27, 34), 0.90) 50%,
      rgba(var(--ide-surface-rgb, 22, 27, 34), 0.85) 100%
    );
    border-bottom: 1px solid rgba(var(--ide-border-rgb, 48, 54, 61), 0.3);
    box-shadow:
      0 2px 8px rgba(0, 0, 0, 0.1),
      0 1px 3px rgba(0, 0, 0, 0.15),
      inset 0 1px 0 rgba(255, 255, 255, 0.08);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
  }

  .ide-header-blur::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg,
      transparent 0%,
      rgba(var(--ide-accent-rgb, 88, 166, 255), 0.3) 50%,
      transparent 100%
    );
  }

  /* Modern button styling for header */
  .ide-header-button {
    @apply relative overflow-hidden rounded-lg transition-all duration-200 ease-out;
    background: rgba(var(--ide-surface-hover-rgb, 33, 38, 45), 0.4);
    border: 1px solid rgba(var(--ide-border-rgb, 48, 54, 61), 0.3);
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .ide-header-button:hover {
    background: rgba(var(--ide-surface-hover-rgb, 33, 38, 45), 0.7);
    border-color: rgba(var(--ide-accent-rgb, 88, 166, 255), 0.5);
    box-shadow:
      0 3px 8px rgba(var(--ide-accent-rgb, 88, 166, 255), 0.12),
      0 1px 3px rgba(0, 0, 0, 0.15),
      inset 0 1px 0 rgba(255, 255, 255, 0.12);
    transform: translateY(-0.5px);
  }

  .ide-header-button:active {
    transform: translateY(0);
    box-shadow:
      0 2px 4px rgba(var(--ide-accent-rgb, 88, 166, 255), 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.05);
  }

  /* Active state for toggle buttons */
  .ide-header-button.active {
    background: rgba(var(--ide-accent-rgb, 88, 166, 255), 0.2);
    border-color: rgba(var(--ide-accent-rgb, 88, 166, 255), 0.7);
    color: var(--ide-accent);
    box-shadow:
      0 2px 8px rgba(var(--ide-accent-rgb, 88, 166, 255), 0.25),
      0 1px 3px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.15);
  }

  /* Header panel styling */
  .ide-header-center-panel {
    background: rgba(var(--ide-surface-rgb, 22, 27, 34), 0.6);
    border: 1px solid rgba(var(--ide-border-rgb, 48, 54, 61), 0.4);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    box-shadow:
      0 2px 8px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.08);
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    white-space: nowrap;
  }

  .ide-header-status-panel {
    background: rgba(var(--ide-surface-rgb, 22, 27, 34), 0.5);
    border: 1px solid rgba(var(--ide-border-rgb, 48, 54, 61), 0.3);
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    box-shadow:
      0 1px 4px rgba(0, 0, 0, 0.08),
      inset 0 1px 0 rgba(255, 255, 255, 0.06);
  }

  /* Separator styling */
  .ide-header-separator {
    background: linear-gradient(to bottom,
      transparent,
      rgba(var(--ide-border-rgb, 48, 54, 61), 0.6),
      transparent
    );
    width: 1px;
    height: 20px;
    opacity: 0.7;
  }

  /* Responsive adjustments */
  @media (max-width: 768px) {
    .ide-header-blur {
      padding-left: 1rem;
      padding-right: 1rem;
    }

    .ide-header-button span {
      display: none;
    }

    .ide-header-separator {
      display: none;
    }

    .modern-sidebar-nav {
      width: 3rem;
    }

    .modern-sidebar-tab {
      margin: 2px 4px;
      padding: 0.5rem;
    }

    .sidebar-tooltip {
      display: none;
    }
  }

  /* Sidebar content styling */
  .sidebar-content-section {
    @apply p-4;
  }

  .sidebar-content-section h3 {
    @apply text-sm font-semibold text-ide-text mb-3 flex items-center space-x-2;
  }

  .sidebar-content-section .content-item {
    @apply flex items-center justify-between p-2 rounded-md hover:bg-ide-surface-hover transition-colors duration-200 cursor-pointer;
  }

  .sidebar-content-section .content-item:hover {
    background: rgba(var(--ide-surface-hover-rgb, 33, 38, 45), 0.6);
  }

  /* Enhanced focus states for accessibility */
  .ide-header-button:focus-visible {
    outline: 2px solid var(--ide-accent);
    outline-offset: 2px;
  }

  /* Smooth theme transition for header */
  .ide-header-blur,
  .ide-header-button,
  .ide-header-separator {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Modern Sidebar Styling */
  .modern-sidebar {
    background: linear-gradient(135deg,
      var(--ide-bg) 0%,
      rgba(var(--ide-surface-rgb, 22, 27, 34), 0.95) 100%
    );
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    border-right: 1px solid rgba(var(--ide-border-rgb, 48, 54, 61), 0.6);
    box-shadow:
      inset -1px 0 0 rgba(255, 255, 255, 0.05),
      2px 0 8px rgba(0, 0, 0, 0.1);
  }

  .modern-sidebar-nav {
    background: rgba(var(--ide-bg-rgb, 13, 17, 23), 0.8);
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
    border-right: 1px solid rgba(var(--ide-border-rgb, 48, 54, 61), 0.3);
    box-shadow:
      inset -1px 0 0 rgba(255, 255, 255, 0.03),
      1px 0 4px rgba(0, 0, 0, 0.05);
  }

  /* Modern Sidebar Tab Button */
  .modern-sidebar-tab {
    @apply relative p-3 flex items-center justify-center transition-all duration-300 ease-out;
    border-radius: 12px;
    margin: 4px 6px;
    background: transparent;
    border: 1px solid transparent;
    position: relative;
    overflow: hidden;
  }

  .modern-sidebar-tab::before {
    content: '';
    position: absolute;
    inset: 0;
    background: rgba(var(--ide-surface-hover-rgb, 33, 38, 45), 0.3);
    border-radius: inherit;
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .modern-sidebar-tab:hover::before {
    opacity: 1;
  }

  .modern-sidebar-tab:hover {
    border-color: rgba(var(--ide-accent-rgb, 88, 166, 255), 0.3);
    box-shadow:
      0 4px 12px rgba(var(--ide-accent-rgb, 88, 166, 255), 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
    transform: translateY(-1px);
  }

  /* Active sidebar tab */
  .modern-sidebar-tab.active {
    background: rgba(var(--ide-accent-rgb, 88, 166, 255), 0.15);
    border-color: rgba(var(--ide-accent-rgb, 88, 166, 255), 0.6);
    color: var(--ide-accent);
    box-shadow:
      0 0 20px rgba(var(--ide-accent-rgb, 88, 166, 255), 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
  }

  .modern-sidebar-tab.active::before {
    opacity: 0;
  }

  /* Active indicator */
  .modern-sidebar-tab.active::after {
    content: '';
    position: absolute;
    left: -6px;
    top: 50%;
    transform: translateY(-50%);
    width: 3px;
    height: 20px;
    background: var(--ide-accent);
    border-radius: 0 2px 2px 0;
    box-shadow: 0 0 8px rgba(var(--ide-accent-rgb, 88, 166, 255), 0.6);
  }

  /* Tooltip styling */
  .sidebar-tooltip {
    @apply absolute left-full ml-3 px-3 py-2 bg-ide-surface border border-ide-border rounded-lg text-sm text-ide-text whitespace-nowrap z-50;
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    box-shadow:
      0 4px 12px rgba(0, 0, 0, 0.15),
      0 2px 4px rgba(0, 0, 0, 0.1);
    opacity: 0;
    transform: translateX(-8px);
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    pointer-events: none;
    top: 50%;
    transform: translateY(-50%) translateX(-8px);
  }

  .modern-sidebar-tab:hover .sidebar-tooltip {
    opacity: 1;
    transform: translateY(-50%) translateX(0);
  }

  /* Content area rounded corners */
  .ide-content-panel {
    @apply rounded-lg overflow-hidden;
    background: var(--ide-surface);
    border-left: none;
    box-shadow:
      0 1px 3px rgba(0, 0, 0, 0.05),
      inset 0 1px 0 rgba(255, 255, 255, 0.03);
  }

  /* Editor panel styling */
  .ide-editor-panel {
    @apply rounded-lg overflow-hidden;
    background: var(--ide-surface);
    box-shadow:
      0 2px 8px rgba(0, 0, 0, 0.05),
      inset 0 1px 0 rgba(255, 255, 255, 0.02);
  }

  /* Terminal panel styling */
  .ide-terminal-panel {
    @apply rounded-lg overflow-hidden;
    background: var(--ide-surface);
    box-shadow:
      0 -2px 8px rgba(0, 0, 0, 0.05),
      inset 0 1px 0 rgba(255, 255, 255, 0.02);
  }

  /* Modern Panel Resize Handles */
  .panel-resize-handle {
    @apply transition-all duration-200 ease-out relative;
    background: transparent;
    width: 4px;
    margin: 0 2px;
  }

  .panel-resize-handle::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 1px;
    height: 60%;
    background: rgba(var(--ide-border-rgb, 48, 54, 61), 0.4);
    transition: all 0.2s ease;
  }

  .panel-resize-handle:hover::before {
    width: 2px;
    background: rgba(var(--ide-accent-rgb, 88, 166, 255), 0.8);
    box-shadow: 0 0 4px rgba(var(--ide-accent-rgb, 88, 166, 255), 0.4);
  }

  .panel-resize-handle-vertical {
    @apply transition-all duration-200 ease-out relative;
    background: transparent;
    height: 4px;
    margin: 2px 0;
  }

  .panel-resize-handle-vertical::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60%;
    height: 1px;
    background: rgba(var(--ide-border-rgb, 48, 54, 61), 0.4);
    transition: all 0.2s ease;
  }

  .panel-resize-handle-vertical:hover::before {
    height: 2px;
    background: rgba(var(--ide-accent-rgb, 88, 166, 255), 0.8);
    box-shadow: 0 0 4px rgba(var(--ide-accent-rgb, 88, 166, 255), 0.4);
  }

  /* Ultra-minimal resize handle for split editor */
  .modern-resize-handle {
    @apply transition-all duration-200 ease-out relative;
    background: transparent;
    width: 1px;
    margin: 0;
    cursor: col-resize;
    opacity: 0;
  }

  .modern-resize-handle::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 1px;
    height: 100%;
    background: rgba(var(--ide-border-rgb, 48, 54, 61), 0.2);
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .modern-resize-handle:hover {
    opacity: 1;
    width: 3px;
  }

  .modern-resize-handle:hover::before {
    width: 1px;
    background: rgba(var(--ide-accent-rgb, 88, 166, 255), 0.8);
    box-shadow:
      0 0 4px rgba(var(--ide-accent-rgb, 88, 166, 255), 0.4),
      0 0 8px rgba(var(--ide-accent-rgb, 88, 166, 255), 0.2);
  }

  .modern-resize-handle:active {
    opacity: 1;
  }

  .modern-resize-handle:active::before {
    background: rgba(var(--ide-accent-rgb, 88, 166, 255), 1);
    box-shadow:
      0 0 6px rgba(var(--ide-accent-rgb, 88, 166, 255), 0.6),
      0 0 12px rgba(var(--ide-accent-rgb, 88, 166, 255), 0.3);
  }

  /* Show resize handle on parent hover */
  .panel-group:hover .modern-resize-handle {
    opacity: 0.6;
  }

  /* Ultra-minimal vertical resize handle for split editor */
  .modern-resize-handle-vertical {
    @apply transition-all duration-200 ease-out relative;
    background: transparent;
    height: 1px;
    margin: 0;
    cursor: row-resize;
    opacity: 0;
  }

  .modern-resize-handle-vertical::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    width: 100%;
    height: 1px;
    background: rgba(var(--ide-border-rgb, 48, 54, 61), 0.2);
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .modern-resize-handle-vertical:hover {
    opacity: 1;
    height: 3px;
  }

  .modern-resize-handle-vertical:hover::before {
    height: 1px;
    background: rgba(var(--ide-accent-rgb, 88, 166, 255), 0.8);
    box-shadow:
      0 0 4px rgba(var(--ide-accent-rgb, 88, 166, 255), 0.4),
      0 0 8px rgba(var(--ide-accent-rgb, 88, 166, 255), 0.2);
  }

  .modern-resize-handle-vertical:active {
    opacity: 1;
  }

  .modern-resize-handle-vertical:active::before {
    background: rgba(var(--ide-accent-rgb, 88, 166, 255), 1);
    box-shadow:
      0 0 6px rgba(var(--ide-accent-rgb, 88, 166, 255), 0.6),
      0 0 12px rgba(var(--ide-accent-rgb, 88, 166, 255), 0.3);
  }

  /* Show vertical resize handle on parent hover */
  .panel-group:hover .modern-resize-handle-vertical {
    opacity: 0.6;
  }

  /* Split Editor Group Styling */
  .editor-group {
    @apply transition-all duration-200 ease-out;
    border-radius: 8px;
    overflow: hidden;
  }

  .editor-group.active {
    box-shadow:
      0 0 0 2px rgba(var(--ide-accent-rgb, 88, 166, 255), 0.3),
      0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .editor-group:not(.active) {
    opacity: 0.95;
  }

  .editor-group:not(.active):hover {
    opacity: 1;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  }

  /* Context Menu Styling */
  .context-menu {
    @apply bg-ide-surface border border-ide-border rounded-lg shadow-lg;
    backdrop-filter: blur(8px);
    background: rgba(var(--ide-surface-rgb, 30, 30, 30), 0.95);
    animation: contextMenuFadeIn 0.15s ease-out;
  }

  @keyframes contextMenuFadeIn {
    from {
      opacity: 0;
      transform: scale(0.95) translateY(-4px);
    }
    to {
      opacity: 1;
      transform: scale(1) translateY(0);
    }
  }

  .context-menu-item {
    @apply px-3 py-2 text-left hover:bg-ide-border transition-colors flex items-center space-x-2;
    font-size: 13px;
  }

  .context-menu-item:hover {
    background: rgba(var(--ide-accent-rgb, 88, 166, 255), 0.1);
  }

  .context-menu-separator {
    @apply my-1 border-ide-border;
    border-top: 1px solid rgba(var(--ide-border-rgb, 48, 54, 61), 0.5);
  }

  /* Smooth transitions for all IDE components */
  .ide-smooth-transition {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* File tree and content improvements */
  .file-tree-item {
    @apply p-2 rounded-md hover:bg-ide-surface-hover transition-all duration-200 cursor-pointer flex items-center space-x-2;
  }

  .file-tree-item:hover {
    background: rgba(var(--ide-surface-hover-rgb, 33, 38, 45), 0.6);
    transform: translateX(2px);
  }

  .file-tree-item.active {
    background: rgba(var(--ide-accent-rgb, 88, 166, 255), 0.15);
    border-left: 3px solid var(--ide-accent);
    color: var(--ide-accent);
  }

  /* Moving border effect */
  @keyframes movingBorder {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
  }

  .moving-border {
    background: linear-gradient(-45deg,
      var(--ide-accent),
      var(--ide-success),
      var(--ide-accent),
      var(--ide-success));
    background-size: 400% 400%;
    animation: movingBorder 3s ease infinite;
  }

  /* Improved fade in animation */
  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Slide in animation */
  @keyframes slideIn {
    from {
      opacity: 0;
      transform: translateX(-20px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  /* Sidebar slide in animation */
  .animate-slide-in {
    animation: slideIn 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  }

  /* Pulse glow animation */
  @keyframes pulseGlow {
    0%, 100% {
      box-shadow: 0 0 5px var(--ide-accent);
    }
    50% {
      box-shadow: 0 0 20px var(--ide-accent), 0 0 30px var(--ide-accent);
    }
  }

  .animate-gradient {
    background-size: 200% 200%;
    animation: gradientShift 3s ease infinite;
  }

  /* Enhanced Glass morphism effect */
  .glass-morphism-enhanced {
    backdrop-filter: blur(24px) saturate(180%);
    -webkit-backdrop-filter: blur(24px) saturate(180%);
    background: linear-gradient(135deg,
      rgba(255, 255, 255, 0.08) 0%,
      rgba(255, 255, 255, 0.04) 50%,
      rgba(255, 255, 255, 0.02) 100%);
    border: 1px solid rgba(255, 255, 255, 0.12);
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.12),
      0 2px 8px rgba(0, 0, 0, 0.08),
      inset 0 1px 0 rgba(255, 255, 255, 0.15);
  }

  /* Modern input styling */
  .modern-input {
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.08);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .modern-input:focus {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(88, 166, 255, 0.4);
    box-shadow:
      0 0 0 3px rgba(88, 166, 255, 0.1),
      0 4px 16px rgba(88, 166, 255, 0.15);
  }

  .modern-input:hover:not(:focus) {
    background: rgba(255, 255, 255, 0.04);
    border: 1px solid rgba(255, 255, 255, 0.12);
  }

  /* Enhanced card hover effects */
  .enhanced-card-hover {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .enhanced-card-hover:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow:
      0 20px 40px rgba(0, 0, 0, 0.15),
      0 8px 16px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
  }

  /* Gradient text effect */
  .gradient-text {
    background: linear-gradient(135deg, var(--ide-text), var(--ide-accent));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* Animated border gradient */
  .animated-border {
    position: relative;
    overflow: hidden;
  }

  .animated-border::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
      transparent,
      rgba(88, 166, 255, 0.4),
      transparent);
    transition: left 0.8s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .animated-border:hover::before {
    left: 100%;
  }

  /* Pulse animation for loading states */
  .pulse-loading {
    animation: pulseLoading 2s ease-in-out infinite;
  }

  @keyframes pulseLoading {
    0%, 100% {
      opacity: 0.6;
      transform: scale(1);
    }
    50% {
      opacity: 1;
      transform: scale(1.05);
    }
  }

  /* Smooth transitions for theme changes */
  .theme-transition {
    transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
  }

  /* Enhanced scrollbar for modals */
  .modal-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: rgba(88, 166, 255, 0.3) transparent;
  }

  .modal-scrollbar::-webkit-scrollbar {
    width: 6px;
  }

  .modal-scrollbar::-webkit-scrollbar-track {
    background: transparent;
  }

  .modal-scrollbar::-webkit-scrollbar-thumb {
    background: rgba(88, 166, 255, 0.3);
    border-radius: 3px;
  }

  .modal-scrollbar::-webkit-scrollbar-thumb:hover {
    background: rgba(88, 166, 255, 0.5);
  }

  /* Improved scale-in animation */
  @keyframes scaleIn {
    from {
      opacity: 0;
      transform: scale(0.9) translateY(20px);
    }
    to {
      opacity: 1;
      transform: scale(1) translateY(0);
    }
  }

  /* Smooth bounce-in animation */

  /* Command Palette Styling */
  .command-palette-container {
    @apply w-full max-w-2xl mx-4;
    background: rgba(var(--ide-surface-rgb, 30, 30, 30), 0.95);
    backdrop-filter: blur(20px) saturate(180%);
    -webkit-backdrop-filter: blur(20px) saturate(180%);
    border: 1px solid rgba(var(--ide-border-rgb, 48, 54, 61), 0.8);
    border-radius: 16px;
    box-shadow:
      0 20px 40px rgba(0, 0, 0, 0.3),
      0 8px 16px rgba(0, 0, 0, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
    animation: commandPaletteSlideIn 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
  }

  @keyframes commandPaletteSlideIn {
    from {
      opacity: 0;
      transform: translateY(-20px) scale(0.95);
    }
    to {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }

  .command-palette-header {
    @apply flex items-center space-x-3 p-4 border-b;
    border-color: rgba(var(--ide-border-rgb, 48, 54, 61), 0.5);
    background: rgba(var(--ide-surface-rgb, 30, 30, 30), 0.3);
  }

  .command-palette-input {
    @apply flex-1 bg-transparent text-ide-text placeholder-ide-text-muted outline-none;
    font-size: 16px;
    font-weight: 500;
  }

  .command-palette-input::placeholder {
    color: rgba(var(--ide-text-muted-rgb, 139, 148, 158), 0.7);
  }

  .command-palette-list {
    @apply max-h-96 overflow-y-auto;
    scrollbar-width: thin;
    scrollbar-color: rgba(var(--ide-accent-rgb, 88, 166, 255), 0.3) transparent;
  }

  .command-palette-list::-webkit-scrollbar {
    width: 6px;
  }

  .command-palette-list::-webkit-scrollbar-track {
    background: transparent;
  }

  .command-palette-list::-webkit-scrollbar-thumb {
    background: rgba(var(--ide-accent-rgb, 88, 166, 255), 0.3);
    border-radius: 3px;
  }

  .command-palette-list::-webkit-scrollbar-thumb:hover {
    background: rgba(var(--ide-accent-rgb, 88, 166, 255), 0.5);
  }

  .command-palette-item {
    @apply flex items-center justify-between p-3 cursor-pointer transition-all duration-150;
    border-bottom: 1px solid rgba(var(--ide-border-rgb, 48, 54, 61), 0.2);
  }

  .command-palette-item:hover {
    background: rgba(var(--ide-surface-hover-rgb, 33, 38, 45), 0.6);
  }

  .command-palette-item.selected {
    background: rgba(var(--ide-accent-rgb, 88, 166, 255), 0.15);
    border-left: 3px solid var(--ide-accent);
  }

  .command-palette-item:last-child {
    border-bottom: none;
  }

  .command-title {
    @apply text-ide-text font-medium truncate;
    font-size: 14px;
  }

  .command-description {
    @apply text-ide-text-muted text-xs truncate mt-0.5;
  }

  .command-category {
    @apply flex items-center space-x-1 px-2 py-1 rounded-full text-xs;
    background: rgba(var(--ide-accent-rgb, 88, 166, 255), 0.1);
    color: var(--ide-accent);
    font-weight: 500;
  }

  .command-shortcut {
    @apply px-2 py-1 rounded text-xs font-mono;
    background: rgba(var(--ide-border-rgb, 48, 54, 61), 0.3);
    color: var(--ide-text-muted);
    border: 1px solid rgba(var(--ide-border-rgb, 48, 54, 61), 0.5);
  }

  .command-palette-empty {
    @apply flex flex-col items-center justify-center py-12 text-center;
  }

  .command-palette-footer {
    @apply p-3 border-t;
    border-color: rgba(var(--ide-border-rgb, 48, 54, 61), 0.5);
    background: rgba(var(--ide-surface-rgb, 30, 30, 30), 0.3);
  }

  /* Command palette item animations */
  .command-palette-item {
    animation: commandItemSlideIn 0.1s ease-out;
    animation-fill-mode: both;
  }

  .command-palette-item:nth-child(1) { animation-delay: 0ms; }
  .command-palette-item:nth-child(2) { animation-delay: 20ms; }
  .command-palette-item:nth-child(3) { animation-delay: 40ms; }
  .command-palette-item:nth-child(4) { animation-delay: 60ms; }
  .command-palette-item:nth-child(5) { animation-delay: 80ms; }
  .command-palette-item:nth-child(6) { animation-delay: 100ms; }
  .command-palette-item:nth-child(7) { animation-delay: 120ms; }
  .command-palette-item:nth-child(8) { animation-delay: 140ms; }

  @keyframes commandItemSlideIn {
    from {
      opacity: 0;
      transform: translateX(-10px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  /* Enhanced command palette backdrop */
  .command-palette-backdrop {
    backdrop-filter: blur(8px) saturate(120%);
    -webkit-backdrop-filter: blur(8px) saturate(120%);
    background: rgba(0, 0, 0, 0.4);
    animation: backdropFadeIn 0.15s ease-out;
  }

  @keyframes backdropFadeIn {
    from {
      opacity: 0;
      backdrop-filter: blur(0px);
    }
    to {
      opacity: 1;
      backdrop-filter: blur(8px) saturate(120%);
    }
  }

  /* Command palette search input focus effect */
  .command-palette-input:focus {
    outline: none;
  }

  .command-palette-header:focus-within {
    box-shadow: inset 0 0 0 1px rgba(var(--ide-accent-rgb, 88, 166, 255), 0.5);
  }

  /* Smooth scrolling for command list */
  .command-palette-list {
    scroll-behavior: smooth;
  }

  /* Command category badge hover effect */
  .command-category:hover {
    background: rgba(var(--ide-accent-rgb, 88, 166, 255), 0.2);
    transform: scale(1.05);
  }

  /* Command shortcut badge hover effect */
  .command-shortcut:hover {
    background: rgba(var(--ide-border-rgb, 48, 54, 61), 0.5);
    border-color: rgba(var(--ide-accent-rgb, 88, 166, 255), 0.3);
  }

  /* Zen Mode Styling */
  .zen-mode-container {
    position: relative;
    width: 100%;
    height: 100%;
  }

  .zen-mode-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(var(--ide-bg-rgb, 13, 17, 23), 0.95);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    z-index: 9999;
    pointer-events: none;
  }

  .zen-mode-exit-hint {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 10000;
    animation: zenHintSlideIn 1.2s cubic-bezier(0.4, 0, 0.2, 1);
    perspective: 1000px;
  }

  .zen-mode-hint-content {
    background: rgba(var(--ide-surface-rgb, 30, 30, 30), 0.98);
    backdrop-filter: blur(25px) saturate(200%);
    -webkit-backdrop-filter: blur(25px) saturate(200%);
    border: 1px solid rgba(var(--ide-accent-rgb, 88, 166, 255), 0.3);
    border-radius: 20px;
    padding: 28px 32px;
    box-shadow:
      0 25px 50px rgba(0, 0, 0, 0.5),
      0 12px 24px rgba(0, 0, 0, 0.4),
      0 4px 8px rgba(0, 0, 0, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.15),
      inset 0 -1px 0 rgba(0, 0, 0, 0.1);
    min-width: 360px;
    text-align: center;
    transform-style: preserve-3d;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .zen-mode-hint-content:hover {
    transform: translateZ(10px);
    box-shadow:
      0 30px 60px rgba(0, 0, 0, 0.6),
      0 15px 30px rgba(0, 0, 0, 0.5),
      0 6px 12px rgba(0, 0, 0, 0.4),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
  }

  @keyframes zenHintSlideIn {
    0% {
      opacity: 0;
      transform: translate(-50%, -50%) scale(0.8) translateY(30px) rotateX(15deg);
      filter: blur(10px);
    }
    50% {
      opacity: 0.8;
      transform: translate(-50%, -50%) scale(1.05) translateY(-5px) rotateX(0deg);
      filter: blur(2px);
    }
    100% {
      opacity: 1;
      transform: translate(-50%, -50%) scale(1) translateY(0) rotateX(0deg);
      filter: blur(0px);
    }
  }

  .zen-kbd {
    @apply inline-flex items-center justify-center px-2 py-1 text-xs font-mono font-medium rounded;
    background: rgba(var(--ide-border-rgb, 48, 54, 61), 0.3);
    color: var(--ide-text);
    border: 1px solid rgba(var(--ide-border-rgb, 48, 54, 61), 0.5);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    min-width: 24px;
  }

  /* Zen Mode Layout Adjustments */
  .zen-mode-active .ide-topbar {
    position: fixed !important;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1001;
    transform: translateY(-100%);
    opacity: 0;
    background: rgba(var(--ide-surface-rgb, 22, 27, 34), 0.98) !important;
    backdrop-filter: blur(25px) saturate(200%) !important;
    -webkit-backdrop-filter: blur(25px) saturate(200%) !important;
    border-bottom: 1px solid rgba(var(--ide-border-rgb, 48, 54, 61), 0.4) !important;
    box-shadow:
      0 4px 16px rgba(0, 0, 0, 0.2),
      0 2px 8px rgba(0, 0, 0, 0.15) !important;
    pointer-events: auto !important;
  }

  /* Ensure zen mode topbar styling overrides */
  .zen-mode-active .ide-topbar .ide-header-center-panel {
    background: rgba(var(--ide-surface-rgb, 22, 27, 34), 0.8) !important;
    border-color: rgba(var(--ide-border-rgb, 48, 54, 61), 0.5) !important;
    position: absolute !important;
    left: 50% !important;
    top: 50% !important;
    transform: translate(-50%, -50%) !important;
  }

  .zen-mode-active .ide-topbar .ide-header-status-panel {
    background: rgba(var(--ide-surface-rgb, 22, 27, 34), 0.8) !important;
    border-color: rgba(var(--ide-border-rgb, 48, 54, 61), 0.5) !important;
  }

  .zen-mode-active .sidebar-container {
    transform: translateX(-100%);
    opacity: 0;
    pointer-events: none;
  }

  .zen-mode-active .ide-terminal-panel {
    transform: translateY(100%);
    opacity: 0;
    pointer-events: none;
  }

  .zen-mode-active .split-editor-container {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 100;
    margin: 0;
    padding: 0;
  }

  /* Ensure normal layout when not in zen mode */
  .ide-topbar {
    position: relative;
    z-index: 10;
    background: var(--ide-surface);
    border-bottom: 1px solid var(--ide-border);
  }

  .sidebar-container {
    position: relative;
    z-index: 5;
  }

  .ide-terminal-panel {
    position: relative;
    z-index: 5;
  }

  /* Enhanced smooth transitions for zen mode elements */
  .sidebar-container,
  .ide-terminal-panel,
  .split-editor-container {
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: transform, opacity;
  }

  .ide-topbar {
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: transform, opacity, background, border-color, box-shadow;
  }

  /* Ensure header panels transition smoothly */
  .ide-header-center-panel,
  .ide-header-status-panel {
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Zen mode editor adjustments */
  .zen-mode-active .monaco-editor {
    border-radius: 0;
  }

  .zen-mode-active .editor-tabs {
    border-radius: 0;
  }

  /* Hide scrollbars in zen mode for cleaner look */
  .zen-mode-active ::-webkit-scrollbar {
    width: 4px;
    height: 4px;
  }

  .zen-mode-active ::-webkit-scrollbar-track {
    background: transparent;
  }

  .zen-mode-active ::-webkit-scrollbar-thumb {
    background: rgba(var(--ide-accent-rgb, 88, 166, 255), 0.2);
    border-radius: 2px;
  }

  .zen-mode-active ::-webkit-scrollbar-thumb:hover {
    background: rgba(var(--ide-accent-rgb, 88, 166, 255), 0.4);
  }
  /* Removed unused CSS animations - GSAP handles all animations now */

  /* Simple, Fast Cursor Effects */
  .premium-cursor {
    position: fixed;
    top: 0;
    left: 0;
    width: 40px;
    height: 40px;
    border: 2px solid rgba(59, 130, 246, 0.6);
    border-radius: 50%;
    pointer-events: none;
    z-index: 9999;
    mix-blend-mode: difference;
    will-change: transform;
    transition: scale 0.3s ease-out;
  }

  .premium-cursor-dot {
    position: fixed;
    top: 0;
    left: 0;
    width: 8px;
    height: 8px;
    background: linear-gradient(45deg, #3b82f6, #8b5cf6);
    border-radius: 50%;
    pointer-events: none;
    z-index: 10000;
    box-shadow: 0 0 15px rgba(59, 130, 246, 0.5);
    will-change: transform;
    transition: scale 0.3s ease-out;
  }

  .premium-cursor.cursor-hover {
    border-color: rgba(147, 51, 234, 0.8);
    box-shadow: 0 0 25px rgba(147, 51, 234, 0.3);
  }

  /* Hide default cursor only on landing page - Desktop only */
  @media (hover: hover) and (pointer: fine) {
    .landing-page * {
      cursor: none !important;
    }

    /* Keep text cursor only for inputs on landing page */
    .landing-page input,
    .landing-page textarea,
    .landing-page [contenteditable="true"] {
      cursor: text !important;
    }
  }

  /* Hide custom cursor on mobile */
  @media (hover: none) or (pointer: coarse) {
    .premium-cursor,
    .premium-cursor-dot {
      display: none !important;
    }

    /* Restore normal cursors on mobile */
    * {
      cursor: auto !important;
    }
  }

  /* Smooth scroll behavior */
  html {
    scroll-behavior: smooth;
  }

  /* Enhanced smooth scrolling */
  * {
    scroll-behavior: smooth;
  }

  /* Component transition enhancements */
  .scroll-animate, .group, .interactive-card {
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Ultra-smooth scrolling for webkit browsers */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1);
  }

  ::-webkit-scrollbar-thumb {
    background: linear-gradient(45deg, #3b82f6, #8b5cf6);
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(45deg, #2563eb, #7c3aed);
  }

  /* Enhanced button hover effects */
  button:hover, .group:hover {
    transform: translateY(-1px);
  }

  /* Smooth page transitions */
  * {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* GSAP handles all scroll animations now - removed conflicting CSS animations */

  /* Card hover lift effect */
  .card-hover-lift {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .card-hover-lift:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  }

  /* Smooth theme transitions */
  .theme-transition * {
    transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease, box-shadow 0.3s ease !important;
  }

  /* Enhanced button animations */
  .button-press {
    transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .button-press:active {
    transform: scale(0.95);
  }

  /* Improved loading spinner */
  @keyframes smoothSpin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }

  .smooth-spin {
    animation: smoothSpin 1s linear infinite;
  }

  /* Modal backdrop blur effect */
  .modal-backdrop {
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    background: rgba(0, 0, 0, 0.5);
    transition: all 0.3s ease;
  }

  /* Enhanced focus states */
  .enhanced-focus:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(88, 166, 255, 0.2);
    border-color: var(--ide-accent);
  }

  /* Subtle glow effect for selected items */
  .selected-glow {
    box-shadow: 0 0 20px rgba(88, 166, 255, 0.3);
  }

  /* Smooth progress bar animations */
  .progress-bar-fill {
    transition: width 1s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Pulsing dot animation for active indicators */
  .pulse-dot {
    animation: pulseDot 2s infinite;
  }

  @keyframes pulseDot {
    0%, 100% {
      opacity: 1;
      transform: scale(1);
    }
    50% {
      opacity: 0.7;
      transform: scale(1.1);
    }
  }

  /* Language tag hover effects */
  .language-tag {
    transition: all 0.2s ease;
  }

  .language-tag:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }

  /* Shimmer animation for progress bars */
  @keyframes shimmer {
    0% {
      transform: translateX(-100%);
    }
    100% {
      transform: translateX(100%);
    }
  }

  /* Minimal Modern Loading Animation */
  @keyframes loading-dot {
    0%, 80%, 100% {
      transform: scale(0.8) translateY(0);
      opacity: 0.5;
    }
    40% {
      transform: scale(1.2) translateY(-8px);
      opacity: 1;
    }
  }

  .animate-loading-dot {
    animation: loading-dot 1.4s ease-in-out infinite;
  }



  .animate-shimmer {
    animation: shimmer 2s infinite;
  }

  /* Dropdown menu positioning and z-index */
  .dropdown-menu {
    position: absolute;
    z-index: 9999 !important;
    right: 0;
    top: 100%;
    margin-top: 0.5rem;
  }

  /* Enhanced scale in animation for dropdowns */
  @keyframes scale-in {
    0% {
      opacity: 0;
      transform: scale(0.95) translateY(-10px);
    }
    100% {
      opacity: 1;
      transform: scale(1) translateY(0);
    }
  }

  .animate-scale-in {
    animation: scale-in 0.2s cubic-bezier(0.16, 1, 0.3, 1);
  }

  /* Ensure project cards don't clip dropdowns */
  .interactive-card {
    position: relative;
  }

  /* Landing Page Specific Animations */
  @keyframes gradientShift {
    0%, 100% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
  }

  .animate-gradient {
    background-size: 200% 200%;
    animation: gradientShift 8s ease infinite;
  }

  /* Removed unused float, glow, and particle animations for better performance */

  /* Smooth scroll behavior */
  html {
    scroll-behavior: smooth;
  }

  /* Landing page backdrop blur */
  .backdrop-blur-md {
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
  }

  /* Enhanced glass morphism for landing */
  .glass-landing {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  /* Magnetic button effect */
  .magnetic-button {
    transition: transform 0.3s cubic-bezier(0.23, 1, 0.32, 1);
  }

  /* Hero section specific styles */
  .hero-gradient {
    background: linear-gradient(135deg,
      var(--ide-bg) 0%,
      var(--ide-surface) 25%,
      var(--ide-bg) 50%,
      var(--ide-surface) 75%,
      var(--ide-bg) 100%);
  }

  /* Feature card hover effects */
  .feature-card {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .feature-card:hover {
    transform: translateY(-8px) scale(1.02);
  }

  /* Stats counter animation */
  .stats-counter {
    font-variant-numeric: tabular-nums;
  }

  /* Navigation blur effect */
  .nav-blur {
    backdrop-filter: blur(16px) saturate(180%);
    -webkit-backdrop-filter: blur(16px) saturate(180%);
    background: rgba(13, 17, 23, 0.8);
  }

  /* Removed advanced float animation - using GSAP instead */
    animation: advancedFloat 8s ease-in-out infinite;
  }

  /* Morphing gradient background */
  @keyframes morphGradient {
    0%, 100% {
      background-position: 0% 50%;
      transform: scale(1) rotate(0deg);
    }
    25% {
      background-position: 100% 50%;
      transform: scale(1.1) rotate(90deg);
    }
    50% {
      background-position: 100% 100%;
      transform: scale(0.9) rotate(180deg);
    }
    75% {
      background-position: 0% 100%;
      transform: scale(1.05) rotate(270deg);
    }
  }

  .animate-morph-gradient {
    background-size: 400% 400%;
    animation: morphGradient 20s ease-in-out infinite;
  }

  /* Advanced glow effect */
  @keyframes advancedGlow {
    0%, 100% {
      box-shadow:
        0 0 20px rgba(59, 130, 246, 0.3),
        0 0 40px rgba(59, 130, 246, 0.1),
        inset 0 0 20px rgba(59, 130, 246, 0.1);
    }
    25% {
      box-shadow:
        0 0 30px rgba(147, 51, 234, 0.4),
        0 0 60px rgba(147, 51, 234, 0.2),
        inset 0 0 30px rgba(147, 51, 234, 0.1);
    }
    50% {
      box-shadow:
        0 0 25px rgba(236, 72, 153, 0.35),
        0 0 50px rgba(236, 72, 153, 0.15),
        inset 0 0 25px rgba(236, 72, 153, 0.1);
    }
    75% {
      box-shadow:
        0 0 35px rgba(6, 182, 212, 0.4),
        0 0 70px rgba(6, 182, 212, 0.2),
        inset 0 0 35px rgba(6, 182, 212, 0.1);
    }
  }

  .animate-advanced-glow {
    animation: advancedGlow 6s ease-in-out infinite;
  }

  /* Floating component animation */
  @keyframes floatingComponent {
    0%, 100% {
      transform: translateY(0px) translateX(0px) rotate(0deg);
      opacity: 0.8;
    }
    25% {
      transform: translateY(-15px) translateX(5px) rotate(1deg);
      opacity: 1;
    }
    50% {
      transform: translateY(-8px) translateX(-3px) rotate(-0.5deg);
      opacity: 0.9;
    }
    75% {
      transform: translateY(-20px) translateX(8px) rotate(1.5deg);
      opacity: 1;
    }
  }

  .animate-floating-component {
    animation: floatingComponent 12s ease-in-out infinite;
  }

  /* Text reveal animation */
  @keyframes textReveal {
    0% {
      opacity: 0;
      transform: translateY(100px) rotateX(90deg);
    }
    50% {
      opacity: 0.5;
      transform: translateY(50px) rotateX(45deg);
    }
    100% {
      opacity: 1;
      transform: translateY(0px) rotateX(0deg);
    }
  }

  .animate-text-reveal {
    animation: textReveal 1.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
    transform-origin: center bottom;
    perspective: 1000px;
  }

  /* Advanced button hover effects */
  .advanced-button {
    position: relative;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }

  .advanced-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
  }

  .advanced-button:hover::before {
    left: 100%;
  }

  .advanced-button:hover {
    transform: translateY(-2px) scale(1.02);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  }

  /* Particle trail effect */
  @keyframes particleTrail {
    0% {
      opacity: 1;
      transform: scale(1) translateY(0);
    }
    100% {
      opacity: 0;
      transform: scale(0.3) translateY(-100px);
    }
  }

  .particle-trail {
    animation: particleTrail 2s linear infinite;
  }

  /* Advanced backdrop blur */
  .advanced-backdrop-blur {
    backdrop-filter: blur(20px) saturate(180%) brightness(120%);
    -webkit-backdrop-filter: blur(20px) saturate(180%) brightness(120%);
    background: rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  /* Glitch effect for special elements */
  @keyframes glitch {
    0%, 100% {
      transform: translate(0);
      filter: hue-rotate(0deg);
    }
    20% {
      transform: translate(-2px, 2px);
      filter: hue-rotate(90deg);
    }
    40% {
      transform: translate(-2px, -2px);
      filter: hue-rotate(180deg);
    }
    60% {
      transform: translate(2px, 2px);
      filter: hue-rotate(270deg);
    }
    80% {
      transform: translate(2px, -2px);
      filter: hue-rotate(360deg);
    }
  }

  .animate-glitch {
    animation: glitch 0.3s ease-in-out infinite alternate;
  }

  /* 3D card effect */
  .card-3d {
    transform-style: preserve-3d;
    transition: transform 0.3s ease;
  }

  .card-3d:hover {
    transform: rotateY(10deg) rotateX(10deg) translateZ(20px);
  }

  /* Smooth scroll with easing */
  html {
    scroll-behavior: smooth;
    scroll-padding-top: 80px;
  }

  /* Advanced loading animation */
  @keyframes advancedLoading {
    0%, 100% {
      transform: scale(1) rotate(0deg);
      opacity: 1;
    }
    25% {
      transform: scale(1.2) rotate(90deg);
      opacity: 0.8;
    }
    50% {
      transform: scale(0.8) rotate(180deg);
      opacity: 0.6;
    }
    75% {
      transform: scale(1.1) rotate(270deg);
      opacity: 0.8;
    }
  }

  .animate-advanced-loading {
    animation: advancedLoading 2s ease-in-out infinite;
  }

  .interactive-card .relative {
    position: relative;
    z-index: 10;
  }

  /* Force dropdown to be above everything */
  .project-dropdown {
    position: absolute !important;
    z-index: 99999 !important;
    right: 0 !important;
    top: calc(100% + 0.5rem) !important;
    background: var(--ide-surface) !important;
    backdrop-filter: blur(20px) !important;
    border: 1px solid var(--ide-border) !important;
    border-radius: 0.5rem !important;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5) !important;
    min-width: 160px !important;
    max-height: 300px !important;
    overflow-y: auto !important;
  }

  /* Ensure parent containers don't clip the dropdown */
  .interactive-card.overflow-visible {
    overflow: visible !important;
  }

  .interactive-card.overflow-visible .relative {
    overflow: visible !important;
  }

  /* Add a backdrop to close dropdown when clicking outside */
  .dropdown-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 99998;
    background: transparent;
  }

  /* Ensure grid containers don't clip dropdowns */
  .grid:has(.overflow-visible) {
    overflow: visible !important;
  }

  /* Alternative approach - force all project containers to be visible when needed */
  .projects-container {
    overflow: visible !important;
  }

  .projects-container .grid {
    overflow: visible !important;
  }
  .glass-morphism {
    background: rgba(255, 255, 255, 0.08);
    backdrop-filter: blur(16px) saturate(180%);
    -webkit-backdrop-filter: blur(16px) saturate(180%);
    border: 1px solid rgba(255, 255, 255, 0.15);
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.2),
      inset 0 -1px 0 rgba(255, 255, 255, 0.1);
  }

  .glass-morphism:hover {
    background: rgba(255, 255, 255, 0.12);
    border: 1px solid rgba(255, 255, 255, 0.25);
    box-shadow:
      0 12px 40px rgba(0, 0, 0, 0.15),
      inset 0 1px 0 rgba(255, 255, 255, 0.3),
      inset 0 -1px 0 rgba(255, 255, 255, 0.15);
  }

  /* Floating animation */
  .animate-float {
    animation: float 3s ease-in-out infinite;
  }

  /* Shimmer effect */
  .animate-shimmer {
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    background-size: 200% 100%;
    animation: shimmer 2s infinite;
  }

  /* Gradient X animation for moving borders */
  .animate-gradient-x {
    background-size: 400% 400%;
    animation: gradient-x 3s ease infinite;
  }

/* Monaco Editor theme adjustments */
.monaco-editor {
  @apply rounded-lg;
}

/* Terminal styling */
.xterm {
  @apply rounded-lg;
}

.xterm .xterm-viewport {
  @apply bg-ide-bg;
}

/* Resizable pane styling */
.Resizer {
  @apply bg-ide-border hover:bg-ide-text-muted transition-colors duration-200;
  opacity: 0.2;
  z-index: 1;
  box-sizing: border-box;
  background-clip: padding-box;
}

.Resizer:hover {
  opacity: 0.6;
}

.Resizer.horizontal {
  height: 11px;
  margin: -5px 0;
  border-top: 5px solid transparent;
  border-bottom: 5px solid transparent;
  cursor: row-resize;
  width: 100%;
}

.Resizer.vertical {
  width: 11px;
  margin: 0 -5px;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  cursor: col-resize;
}

.Resizer.disabled {
  cursor: not-allowed;
}

.Resizer.disabled:hover {
  border-color: transparent;
}

/* Advanced Keyframe Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulseGlow {
  0%, 100% {
    box-shadow: 0 0 5px var(--ide-accent);
  }
  50% {
    box-shadow: 0 0 20px var(--ide-accent), 0 0 30px var(--ide-accent);
  }
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes gradient-x {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3) translateY(20px);
  }
  50% {
    opacity: 1;
    transform: scale(1.05) translateY(-5px);
  }
  70% {
    transform: scale(0.95) translateY(2px);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes scaleIn {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slideUp {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Modern card hover effects */
.modern-card {
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modern-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s;
}

.modern-card:hover::before {
  left: 100%;
}

/* Neon glow effect */
.neon-glow {
  position: relative;
}

.neon-glow::after {
  content: '';
  position: absolute;
  inset: 0;
  padding: 2px;
  background: linear-gradient(45deg, var(--ide-accent), var(--ide-success), var(--ide-accent));
  border-radius: inherit;
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: exclude;
  opacity: 0;
  transition: opacity 0.3s;
}

.neon-glow:hover::after {
  opacity: 1;
}
