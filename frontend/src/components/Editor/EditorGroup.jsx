import React, { useState, useRef } from 'react'
import { Panel, PanelGroup, PanelResizeHandle } from 'react-resizable-panels'
import { MoreHorizontal, X, SplitSquareHorizontal, SplitSquareVertical, Copy } from 'lucide-react'
import { useAppStore } from '../../stores/appStore'
import MonacoEditor from './MonacoEditor'
import EditorTabs from './EditorTabs'
import { WelcomeScreen } from '../IDE'
import { clsx } from 'clsx'

const EditorGroup = ({
  group,
  isActive,
  onFileSelect,
  onSave,
  editorContent,
  onEditorChange,
  getLanguageFromFileName,
  currentProject
}) => {
  const [showContextMenu, setShowContextMenu] = useState(false)
  const [contextMenuPosition, setContextMenuPosition] = useState({ x: 0, y: 0 })
  const contextMenuRef = useRef(null)
  
  const { 
    splitGroup, 
    closeGroup, 
    setActiveGroup,
    setActiveFileInGroup,
    editorGroups
  } = useAppStore()

  const handleContextMenu = (e) => {
    e.preventDefault()
    setContextMenuPosition({ x: e.clientX, y: e.clientY })
    setShowContextMenu(true)
  }

  const handleSplitHorizontal = () => {
    splitGroup(group.id, 'horizontal', group.activeFile)
    setShowContextMenu(false)
  }

  const handleSplitVertical = () => {
    splitGroup(group.id, 'vertical', group.activeFile)
    setShowContextMenu(false)
  }

  const handleCloseGroup = () => {
    if (editorGroups.length > 1) {
      closeGroup(group.id)
    }
    setShowContextMenu(false)
  }

  const handleGroupClick = () => {
    if (!isActive) {
      setActiveGroup(group.id)
    }
  }

  const handleTabClick = (file) => {
    setActiveFileInGroup(file, group.id)
    setActiveGroup(group.id)
    onFileSelect(file)
  }

  // Close context menu when clicking outside
  React.useEffect(() => {
    const handleClickOutside = (event) => {
      if (contextMenuRef.current && !contextMenuRef.current.contains(event.target)) {
        setShowContextMenu(false)
      }
    }

    if (showContextMenu) {
      document.addEventListener('mousedown', handleClickOutside)
      return () => document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [showContextMenu])

  return (
    <div
      className={clsx(
        'flex flex-col h-full ide-editor-panel relative editor-group',
        isActive ? 'active' : ''
      )}
      onClick={handleGroupClick}
      onContextMenu={handleContextMenu}
    >
      {/* Editor Tabs */}
      <div className="flex items-center justify-between">
        <EditorTabs 
          files={group.files}
          activeFile={group.activeFile}
          onTabClick={handleTabClick}
          groupId={group.id}
        />
        
        {/* Group Actions */}
        <div className="flex items-center space-x-1 px-2">
          {editorGroups.length > 1 && (
            <button
              onClick={handleCloseGroup}
              className="p-1 rounded hover:bg-ide-border transition-colors"
              title="Close group"
            >
              <X size={14} className="text-ide-text-muted" />
            </button>
          )}
          <button
            onClick={handleContextMenu}
            className="p-1 rounded hover:bg-ide-border transition-colors"
            title="Split options"
          >
            <MoreHorizontal size={14} className="text-ide-text-muted" />
          </button>
        </div>
      </div>

      {/* Editor Content */}
      <div className="flex-1 overflow-hidden rounded-b-lg">
        {group.activeFile ? (
          <MonacoEditor
            value={editorContent}
            onChange={onEditorChange}
            onSave={onSave}
            language={getLanguageFromFileName(group.activeFile.name)}
            className="h-full rounded-b-lg"
            filePath={group.activeFile.path}
            projectId={currentProject?.id}
          />
        ) : (
          <WelcomeScreen />
        )}
      </div>

      {/* Context Menu */}
      {showContextMenu && (
        <div
          ref={contextMenuRef}
          className="fixed z-50 context-menu py-1 min-w-[180px]"
          style={{
            left: contextMenuPosition.x,
            top: contextMenuPosition.y,
          }}
        >
          <button
            onClick={handleSplitHorizontal}
            className="context-menu-item w-full"
          >
            <SplitSquareHorizontal size={16} />
            <span>Split Right</span>
          </button>
          <button
            onClick={handleSplitVertical}
            className="context-menu-item w-full"
          >
            <SplitSquareVertical size={16} />
            <span>Split Down</span>
          </button>
          {group.activeFile && (
            <>
              <div className="context-menu-separator" />
              <button
                onClick={() => {
                  // Duplicate file in new group
                  splitGroup(group.id, 'horizontal', group.activeFile)
                  setShowContextMenu(false)
                }}
                className="context-menu-item w-full"
              >
                <Copy size={16} />
                <span>Duplicate in Split</span>
              </button>
            </>
          )}
          {editorGroups.length > 1 && (
            <>
              <div className="context-menu-separator" />
              <button
                onClick={handleCloseGroup}
                className="context-menu-item w-full text-red-400"
              >
                <span>Close Group</span>
              </button>
            </>
          )}
        </div>
      )}
    </div>
  )
}

export default EditorGroup
