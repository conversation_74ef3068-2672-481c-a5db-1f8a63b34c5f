import React, { useState } from 'react'
import { X, FileText, FileCode, FileImage, Settings, Database } from 'lucide-react'
import { useAppStore } from '../../stores/appStore'
import { clsx } from 'clsx'

const EditorTabs = ({
  files = null,
  activeFile = null,
  onTabClick = null,
  groupId = null
}) => {
  const {
    openFiles,
    currentFile,
    setCurrentFile,
    removeOpenFile,
    removeFileFromGroup,
    moveFileToGroup,
    editorGroups
  } = useAppStore()

  const [draggedFile, setDraggedFile] = useState(null)
  const [dragOverGroup, setDragOverGroup] = useState(null)

  // Use props if provided (for group mode), otherwise use global state
  const tabFiles = files || openFiles
  const activeTabFile = activeFile || currentFile

  const handleTabClick = (file) => {
    if (onTabClick) {
      onTabClick(file)
    } else {
      setCurrentFile(file)
    }
  }

  const handleTabClose = (e, filePath) => {
    e.stopPropagation()
    if (groupId) {
      removeFileFromGroup(filePath, groupId)
    } else {
      removeOpenFile(filePath)
    }
  }

  const handleDragStart = (e, file) => {
    setDraggedFile(file)
    e.dataTransfer.effectAllowed = 'move'
    e.dataTransfer.setData('text/plain', file.path)
  }

  const handleDragOver = (e) => {
    e.preventDefault()
    e.dataTransfer.dropEffect = 'move'
  }

  const handleDrop = (e, targetGroupId) => {
    e.preventDefault()
    if (draggedFile && groupId && targetGroupId !== groupId) {
      moveFileToGroup(draggedFile, groupId, targetGroupId)
    }
    setDraggedFile(null)
    setDragOverGroup(null)
  }

  const handleDragEnd = () => {
    setDraggedFile(null)
    setDragOverGroup(null)
  }

  const getFileIcon = (fileName) => {
    const ext = fileName.split('.').pop()?.toLowerCase()
    const iconProps = { size: 14, className: 'flex-shrink-0' }

    switch (ext) {
      case 'js':
      case 'jsx':
        return <FileCode {...iconProps} className="text-yellow-500" />
      case 'ts':
      case 'tsx':
        return <FileCode {...iconProps} className="text-blue-500" />
      case 'vue':
        return <FileCode {...iconProps} className="text-green-500" />
      case 'py':
        return <FileCode {...iconProps} className="text-blue-400" />
      case 'json':
        return <Database {...iconProps} className="text-ide-warning" />
      case 'xml':
        return <Database {...iconProps} className="text-orange-500" />
      case 'css':
      case 'scss':
      case 'sass':
      case 'less':
        return <FileText {...iconProps} className="text-blue-400" />
      case 'html':
      case 'htm':
        return <FileCode {...iconProps} className="text-orange-500" />
      case 'md':
      case 'mdx':
        return <FileText {...iconProps} className="text-ide-text-muted" />
      case 'png':
      case 'jpg':
      case 'jpeg':
      case 'gif':
      case 'svg':
        return <FileImage {...iconProps} className="text-purple-500" />
      case 'config':
      case 'conf':
      case 'env':
        return <Settings {...iconProps} className="text-ide-text-muted" />
      default:
        return <FileText {...iconProps} className="text-ide-text-muted" />
    }
  }

  if (tabFiles.length === 0) {
    return null
  }

  return (
    <div
      className="ide-tabs-container flex items-end overflow-x-auto ide-scrollbar"
      onDragOver={handleDragOver}
      onDrop={(e) => handleDrop(e, groupId)}
    >
      {tabFiles.map((file, index) => (
        <div
          key={file.path}
          className={clsx(
            'ide-tab flex items-center justify-between group cursor-pointer relative animate-slide-in-right',
            activeTabFile?.path === file.path && 'ide-tab-active',
            draggedFile?.path === file.path && 'opacity-50'
          )}
          style={{ animationDelay: `${index * 50}ms` }}
          onClick={() => handleTabClick(file)}
          draggable={!!groupId}
          onDragStart={(e) => handleDragStart(e, file)}
          onDragEnd={handleDragEnd}
        >
          <div className="flex items-center space-x-2 min-w-0 flex-1">
            {getFileIcon(file.name)}
            <span className="truncate text-sm font-medium">
              {file.name}
            </span>
          </div>
          <button
            className="opacity-0 group-hover:opacity-100 hover:bg-ide-border rounded-full p-1 transition-all duration-200 ml-2 flex-shrink-0"
            onClick={(e) => handleTabClose(e, file.path)}
            title="Close file"
          >
            <X size={10} />
          </button>
        </div>
      ))}
    </div>
  )
}

export default EditorTabs
