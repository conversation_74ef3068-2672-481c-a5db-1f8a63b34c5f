import React, { useState, useEffect } from 'react'
import { Panel, PanelGroup, PanelResizeHandle } from 'react-resizable-panels'
import { useAppStore } from '../../stores/appStore'
import EditorGroup from './EditorGroup'
import { fileService } from '../../services/api'

const SplitEditor = ({
  onFileSelect,
  getLanguageFromFileName,
  currentProject
}) => {
  const {
    editorGroups,
    activeGroupId,
    currentFile,
    addNotification,
    splitGroup
  } = useAppStore()

  const [editorContents, setEditorContents] = useState({})

  // Keyboard shortcuts for split functionality
  useEffect(() => {
    const handleKeyDown = (e) => {
      // Ctrl+\ or Cmd+\ to split right
      if ((e.ctrlKey || e.metaKey) && e.key === '\\') {
        e.preventDefault()
        if (currentFile) {
          splitGroup(activeGroupId, 'horizontal', currentFile)
        }
      }
      // Ctrl+Shift+\ or Cmd+Shift+\ to split down
      if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === '\\') {
        e.preventDefault()
        if (currentFile) {
          splitGroup(activeGroupId, 'vertical', currentFile)
        }
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [currentFile, activeGroupId, splitGroup])

  // Load file content when currentFile changes
  useEffect(() => {
    const loadFileContent = async () => {
      if (!currentFile || currentFile.type !== 'file') return
      
      // Check if content is already loaded
      if (editorContents[currentFile.path]) return
      
      try {
        const projectId = currentProject?.id || 'default'
        const response = await fileService.readFile(currentFile.path, projectId)
        setEditorContents(prev => ({
          ...prev,
          [currentFile.path]: response.content || ''
        }))
      } catch (error) {
        console.error('Failed to load file:', error)
        // Mock content for development
        setEditorContents(prev => ({
          ...prev,
          [currentFile.path]: `// ${currentFile.name}\n// File content will be loaded here\n\nconsole.log('Hello from ${currentFile.name}');`
        }))
      }
    }

    loadFileContent()
  }, [currentFile, currentProject, editorContents])

  const handleEditorChange = (newContent) => {
    if (!currentFile) return
    
    setEditorContents(prev => ({
      ...prev,
      [currentFile.path]: newContent
    }))
  }

  const handleSaveFile = async () => {
    if (!currentFile || currentFile.type !== 'file') return

    try {
      const projectId = currentProject?.id || 'default'
      const content = editorContents[currentFile.path] || ''
      await fileService.writeFile(currentFile.path, content, projectId)

      addNotification({
        type: 'success',
        message: `Saved ${currentFile.name}`,
      })
    } catch (error) {
      console.error('Failed to save file:', error)
      addNotification({
        type: 'error',
        message: `Failed to save ${currentFile.name}`,
      })
    }
  }

  const renderEditorGroups = () => {
    if (editorGroups.length === 1) {
      const group = editorGroups[0]
      return (
        <EditorGroup
          group={group}
          isActive={true}
          onFileSelect={onFileSelect}
          onSave={handleSaveFile}
          editorContent={currentFile ? editorContents[currentFile.path] || '' : ''}
          onEditorChange={handleEditorChange}
          getLanguageFromFileName={getLanguageFromFileName}
          currentProject={currentProject}
        />
      )
    }

    // For multiple groups, we need to arrange them based on split direction
    // For now, let's implement a simple horizontal split
    return (
      <PanelGroup direction="horizontal" className="panel-group">
        {editorGroups.map((group, index) => (
          <React.Fragment key={group.id}>
            <Panel defaultSize={100 / editorGroups.length} minSize={20}>
              <EditorGroup
                group={group}
                isActive={group.id === activeGroupId}
                onFileSelect={onFileSelect}
                onSave={handleSaveFile}
                editorContent={
                  group.activeFile
                    ? editorContents[group.activeFile.path] || ''
                    : ''
                }
                onEditorChange={handleEditorChange}
                getLanguageFromFileName={getLanguageFromFileName}
                currentProject={currentProject}
              />
            </Panel>
            {index < editorGroups.length - 1 && (
              <PanelResizeHandle className="modern-resize-handle" />
            )}
          </React.Fragment>
        ))}
      </PanelGroup>
    )
  }

  return (
    <div className="flex flex-col h-full split-editor-container">
      {renderEditorGroups()}
    </div>
  )
}

export default SplitEditor
