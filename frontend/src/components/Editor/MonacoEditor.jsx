import React, { useRef, useEffect, useState } from 'react'
import Editor from '@monaco-editor/react'
import { useAppStore } from '../../stores/appStore'
import { getTheme } from '../../config/themes'
import CommandPalette from './CommandPalette'
import RemoteCursors from '../Collaboration/RemoteCursors'
import collaborationService from '../../services/collaboration'
import { debugCollaboration } from '../../utils/collaborationDebug'

const MonacoEditor = ({
  value = '',
  onChange,
  onSave,
  language = 'javascript',
  readOnly = false,
  className = '',
  filePath = null,
  projectId = null
}) => {
  const editorRef = useRef(null)
  const monacoRef = useRef(null)
  const [showCommandPalette, setShowCommandPalette] = useState(false)
  const [remoteCursors, setRemoteCursors] = useState([])
  const [isCollaborating, setIsCollaborating] = useState(false)
  const { theme, settings } = useAppStore()
  const currentTheme = getTheme(theme)

  // Refs for collaboration
  const lastValueRef = useRef(value)
  const isRemoteChangeRef = useRef(false)
  const cursorUpdateTimeoutRef = useRef(null)

  // Auto-join collaboration when file and project are available
  useEffect(() => {
    const autoJoinCollaboration = async () => {
      if (filePath && projectId && typeof collaborationService.connect === 'function') {
        try {
          // Connect to collaboration service
          await collaborationService.connect();

          // Auto-join room for this file (let server generate anonymous name)
          if (!collaborationService.isInRoom()) {
            await collaborationService.joinRoom(projectId, filePath);
            console.log(`🤝 Auto-joined collaboration room: ${projectId}:${filePath}`);
          }
        } catch (error) {
          console.log('ℹ️ Collaboration auto-join failed (this is normal if service is not running):', error.message);
        }
      }
    };

    // Delay auto-join to ensure editor is mounted
    const timer = setTimeout(autoJoinCollaboration, 1000);

    return () => clearTimeout(timer);
  }, [filePath, projectId]);

  // Cleanup collaboration on unmount
  useEffect(() => {
    return () => {
      if (editorRef.current && editorRef.current.cleanupCollaboration) {
        editorRef.current.cleanupCollaboration();
      }
      if (cursorUpdateTimeoutRef.current) {
        clearTimeout(cursorUpdateTimeoutRef.current);
      }
    };
  }, [])

  const handleEditorDidMount = (editor, monaco) => {
    editorRef.current = editor
    monacoRef.current = monaco

    // Set up collaboration if in a collaboration room
    const cleanupCollaboration = setupCollaboration(editor, monaco)

    // Store cleanup function for later use
    editorRef.current.cleanupCollaboration = cleanupCollaboration

    // Configure custom Monaco themes for each theme
    const defineCustomThemes = () => {
      // GitHub Dark theme
      monaco.editor.defineTheme('github-dark', {
        base: 'vs-dark',
        inherit: true,
        rules: [
          { token: 'comment', foreground: '8b949e', fontStyle: 'italic' },
          { token: 'keyword', foreground: 'ff7b72' },
          { token: 'string', foreground: 'a5d6ff' },
          { token: 'number', foreground: '79c0ff' },
          { token: 'type', foreground: 'ffa657' },
          { token: 'function', foreground: 'd2a8ff' },
          { token: 'variable', foreground: 'f0f6fc' },
        ],
        colors: {
          'editor.background': '#0d1117',
          'editor.foreground': '#f0f6fc',
          'editor.lineHighlightBackground': '#161b22',
          'editor.selectionBackground': '#264f78',
          'editor.inactiveSelectionBackground': '#3a3d41',
          'editorCursor.foreground': '#58a6ff',
          'editorWhitespace.foreground': '#484f58',
          'editorIndentGuide.background': '#21262d',
          'editorIndentGuide.activeBackground': '#30363d',
          'editorLineNumber.foreground': '#8b949e',
          'editorLineNumber.activeForeground': '#f0f6fc',
          'scrollbarSlider.background': '#30363d',
          'scrollbarSlider.hoverBackground': '#484f58',
          'scrollbarSlider.activeBackground': '#6e7681',
        }
      })

      // Catppuccin Mocha theme
      monaco.editor.defineTheme('catppuccin-mocha', {
        base: 'vs-dark',
        inherit: true,
        rules: [
          { token: 'comment', foreground: 'a6adc8', fontStyle: 'italic' },
          { token: 'keyword', foreground: 'cba6f7' },
          { token: 'string', foreground: 'a6e3a1' },
          { token: 'number', foreground: 'fab387' },
          { token: 'type', foreground: 'f9e2af' },
          { token: 'function', foreground: '89b4fa' },
          { token: 'variable', foreground: 'cdd6f4' },
        ],
        colors: {
          'editor.background': '#1e1e2e',
          'editor.foreground': '#cdd6f4',
          'editor.lineHighlightBackground': '#313244',
          'editor.selectionBackground': '#45475a',
          'editor.inactiveSelectionBackground': '#585b70',
          'editorCursor.foreground': '#89b4fa',
          'editorWhitespace.foreground': '#6c7086',
          'editorIndentGuide.background': '#45475a',
          'editorIndentGuide.activeBackground': '#585b70',
          'editorLineNumber.foreground': '#a6adc8',
          'editorLineNumber.activeForeground': '#cdd6f4',
          'scrollbarSlider.background': '#45475a',
          'scrollbarSlider.hoverBackground': '#585b70',
          'scrollbarSlider.activeBackground': '#6c7086',
        }
      })

      // Catppuccin Latte theme (Light)
      monaco.editor.defineTheme('catppuccin-latte', {
        base: 'vs',
        inherit: true,
        rules: [
          { token: 'comment', foreground: '6c6f85', fontStyle: 'italic' },
          { token: 'keyword', foreground: '8839ef' },
          { token: 'string', foreground: '40a02b' },
          { token: 'number', foreground: 'fe640b' },
          { token: 'type', foreground: 'df8e1d' },
          { token: 'function', foreground: '1e66f5' },
          { token: 'variable', foreground: '4c4f69' },
        ],
        colors: {
          'editor.background': '#eff1f5',
          'editor.foreground': '#4c4f69',
          'editor.lineHighlightBackground': '#e6e9ef',
          'editor.selectionBackground': '#dce0e8',
          'editor.inactiveSelectionBackground': '#ccd0da',
          'editorCursor.foreground': '#1e66f5',
          'editorWhitespace.foreground': '#bcc0cc',
          'editorIndentGuide.background': '#dce0e8',
          'editorIndentGuide.activeBackground': '#ccd0da',
          'editorLineNumber.foreground': '#6c6f85',
          'editorLineNumber.activeForeground': '#4c4f69',
          'scrollbarSlider.background': '#ccd0da',
          'scrollbarSlider.hoverBackground': '#bcc0cc',
          'scrollbarSlider.activeBackground': '#acb0be',
        }
      })

      // Gruvbox Dark theme
      monaco.editor.defineTheme('gruvbox-dark', {
        base: 'vs-dark',
        inherit: true,
        rules: [
          { token: 'comment', foreground: 'a89984', fontStyle: 'italic' },
          { token: 'keyword', foreground: 'fb4934' },
          { token: 'string', foreground: 'b8bb26' },
          { token: 'number', foreground: 'd3869b' },
          { token: 'type', foreground: 'fabd2f' },
          { token: 'function', foreground: '83a598' },
          { token: 'variable', foreground: 'ebdbb2' },
        ],
        colors: {
          'editor.background': '#282828',
          'editor.foreground': '#ebdbb2',
          'editor.lineHighlightBackground': '#3c3836',
          'editor.selectionBackground': '#504945',
          'editor.inactiveSelectionBackground': '#665c54',
          'editorCursor.foreground': '#83a598',
          'editorWhitespace.foreground': '#7c6f64',
          'editorIndentGuide.background': '#504945',
          'editorIndentGuide.activeBackground': '#665c54',
          'editorLineNumber.foreground': '#a89984',
          'editorLineNumber.activeForeground': '#ebdbb2',
          'scrollbarSlider.background': '#504945',
          'scrollbarSlider.hoverBackground': '#665c54',
          'scrollbarSlider.activeBackground': '#7c6f64',
        }
      })

      // Dracula theme
      monaco.editor.defineTheme('dracula', {
        base: 'vs-dark',
        inherit: true,
        rules: [
          { token: 'comment', foreground: '6272a4', fontStyle: 'italic' },
          { token: 'keyword', foreground: 'ff79c6' },
          { token: 'string', foreground: 'f1fa8c' },
          { token: 'number', foreground: 'bd93f9' },
          { token: 'type', foreground: '8be9fd' },
          { token: 'function', foreground: '50fa7b' },
          { token: 'variable', foreground: 'f8f8f2' },
        ],
        colors: {
          'editor.background': '#282a36',
          'editor.foreground': '#f8f8f2',
          'editor.lineHighlightBackground': '#44475a',
          'editor.selectionBackground': '#44475a',
          'editor.inactiveSelectionBackground': '#6272a4',
          'editorCursor.foreground': '#bd93f9',
          'editorWhitespace.foreground': '#6272a4',
          'editorIndentGuide.background': '#44475a',
          'editorIndentGuide.activeBackground': '#6272a4',
          'editorLineNumber.foreground': '#6272a4',
          'editorLineNumber.activeForeground': '#f8f8f2',
          'scrollbarSlider.background': '#44475a',
          'scrollbarSlider.hoverBackground': '#6272a4',
          'scrollbarSlider.activeBackground': '#8be9fd',
        }
      })

      // One Dark Pro theme
      monaco.editor.defineTheme('one-dark-pro', {
        base: 'vs-dark',
        inherit: true,
        rules: [
          { token: 'comment', foreground: '5c6370', fontStyle: 'italic' },
          { token: 'keyword', foreground: 'c678dd' },
          { token: 'string', foreground: '98c379' },
          { token: 'number', foreground: 'd19a66' },
          { token: 'type', foreground: 'e5c07b' },
          { token: 'function', foreground: '61afef' },
          { token: 'variable', foreground: 'abb2bf' },
        ],
        colors: {
          'editor.background': '#282c34',
          'editor.foreground': '#abb2bf',
          'editor.lineHighlightBackground': '#2c313c',
          'editor.selectionBackground': '#3e4451',
          'editor.inactiveSelectionBackground': '#5c6370',
          'editorCursor.foreground': '#61afef',
          'editorWhitespace.foreground': '#5c6370',
          'editorIndentGuide.background': '#3e4451',
          'editorIndentGuide.activeBackground': '#5c6370',
          'editorLineNumber.foreground': '#5c6370',
          'editorLineNumber.activeForeground': '#abb2bf',
          'scrollbarSlider.background': '#3e4451',
          'scrollbarSlider.hoverBackground': '#5c6370',
          'scrollbarSlider.activeBackground': '#61afef',
        }
      })
    }

    defineCustomThemes()

    // Set theme based on current theme - now properly maps all themes
    let monacoTheme = 'vs-dark' // default fallback
    switch (theme) {
      case 'github-dark':
        monacoTheme = 'github-dark'
        break
      case 'catppuccin-mocha':
        monacoTheme = 'catppuccin-mocha'
        break
      case 'catppuccin-latte':
        monacoTheme = 'catppuccin-latte'
        break
      case 'gruvbox-dark':
        monacoTheme = 'gruvbox-dark'
        break
      case 'dracula':
        monacoTheme = 'dracula'
        break
      case 'one-dark-pro':
        monacoTheme = 'one-dark-pro'
        break
      default:
        monacoTheme = currentTheme.monaco === 'vs' ? 'vs' : 'github-dark'
    }

    monaco.editor.setTheme(monacoTheme)
    
    // Configure editor options
    editor.updateOptions({
      fontSize: 14,
      fontFamily: 'JetBrains Mono, Fira Code, Monaco, Consolas, monospace',
      fontLigatures: true,
      lineNumbers: 'on',
      minimap: { enabled: true },
      scrollBeyondLastLine: false,
      wordWrap: 'on',
      automaticLayout: true,
      tabSize: 2,
      insertSpaces: true,
      detectIndentation: true,
      folding: true,
      foldingStrategy: 'indentation',
      showFoldingControls: 'mouseover',
      matchBrackets: 'always',
      autoClosingBrackets: 'always',
      autoClosingQuotes: 'always',
      formatOnPaste: true,
      formatOnType: true,
      suggestOnTriggerCharacters: true,
      acceptSuggestionOnEnter: 'on',
      quickSuggestions: true,
      parameterHints: { enabled: true },
      hover: { enabled: true },
    })

    // Add keyboard shortcuts
    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyS, () => {
      // Save file shortcut
      if (onSave) {
        onSave()
      }
    })

    // Override default command palette with our custom one
    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyMod.Shift | monaco.KeyCode.KeyP, () => {
      setShowCommandPalette(true)
    })

    // Also handle F1 key for command palette
    editor.addCommand(monaco.KeyCode.F1, () => {
      setShowCommandPalette(true)
    })

    // Disable Monaco's default command palette
    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyMod.Shift | monaco.KeyCode.KeyP, () => {
      // Prevent default and show our custom palette
      setShowCommandPalette(true)
    })
  }

  const handleEditorChange = (newValue) => {
    // Update last value reference
    lastValueRef.current = newValue

    // Send collaboration update if in collaboration mode and not a remote change
    if (isCollaborating && !isRemoteChangeRef.current &&
        typeof collaborationService.isInRoom === 'function' &&
        collaborationService.isInRoom()) {
      try {
        if (typeof collaborationService.sendDocumentChange === 'function') {
          console.log('📤 Sending document change to collaborators');
          collaborationService.sendDocumentChange(newValue);
        }
      } catch (error) {
        console.error('Failed to send document change:', error);
      }
    }

    // Reset remote change flag
    isRemoteChangeRef.current = false;

    if (onChange) {
      onChange(newValue)
    }
  }

  // Set up collaboration features
  const setupCollaboration = (editor, monaco) => {
    try {
      // Debug collaboration setup
      if (process.env.NODE_ENV === 'development') {
        debugCollaboration.testMonacoIntegration(editor, monaco);
        debugCollaboration.safeStateCheck();
      }

      // Check if already in collaboration (with safety check)
      const inRoom = typeof collaborationService.isInRoom === 'function'
        ? collaborationService.isInRoom()
        : false;
      setIsCollaborating(inRoom);

      // Set up collaboration event listeners
      const handleDocumentChanged = (data) => {
        console.log('📝 Received document change from another user:', data.userId);
        if (data.content !== lastValueRef.current) {
          isRemoteChangeRef.current = true;
          editor.setValue(data.content);
          lastValueRef.current = data.content;

          // Trigger onChange to update parent component
          if (onChange) {
            onChange(data.content);
          }
        }
      };

    const handleCursorChanged = (data) => {
      console.log('🖱️ Received cursor change from another user:', data.userId);
      if (Array.isArray(data.cursors)) {
        setRemoteCursors(data.cursors);
      }
    };

    const handleRoomJoined = (data) => {
      console.log('🎉 Joined collaboration room:', data?.roomId);
      console.log('👥 Collaborators:', data?.collaborators?.length || 0);
      setIsCollaborating(true);
    };

    const handleRoomLeft = () => {
      console.log('👋 Left collaboration room');
      setIsCollaborating(false);
      setRemoteCursors([]);
    };

    // Add event listeners (with safety checks)
    if (typeof collaborationService.on === 'function') {
      collaborationService.on('document-changed', handleDocumentChanged);
      collaborationService.on('cursor-changed', handleCursorChanged);
      collaborationService.on('room-joined', handleRoomJoined);
      collaborationService.on('room-left', handleRoomLeft);
    }

    // Set up cursor position tracking
    const handleCursorPositionChange = () => {
      if (!isCollaborating ||
          typeof collaborationService.isInRoom !== 'function' ||
          !collaborationService.isInRoom()) return;

      if (cursorUpdateTimeoutRef.current) {
        clearTimeout(cursorUpdateTimeoutRef.current);
      }

      cursorUpdateTimeoutRef.current = setTimeout(() => {
        try {
          const position = editor.getPosition();
          const selection = editor.getSelection();

          if (position) {
            const cursor = {
              line: position.lineNumber - 1, // Convert to 0-based
              column: position.column - 1
            };

            let selectionData = null;
            if (selection && !selection.isEmpty()) {
              selectionData = {
                startLine: selection.startLineNumber - 1,
                startColumn: selection.startColumn - 1,
                endLine: selection.endLineNumber - 1,
                endColumn: selection.endColumn - 1
              };
            }

            if (typeof collaborationService.sendCursorUpdate === 'function') {
              console.log('🖱️ Sending cursor position:', cursor);
              collaborationService.sendCursorUpdate(cursor, selectionData);
            }
          }
        } catch (error) {
          console.error('Failed to send cursor update:', error);
        }
      }, 100); // Throttle cursor updates
    };

    // Listen for cursor position changes
    editor.onDidChangeCursorPosition(handleCursorPositionChange);
    editor.onDidChangeCursorSelection(handleCursorPositionChange);

      // Cleanup function
      return () => {
        if (typeof collaborationService.off === 'function') {
          collaborationService.off('document-changed', handleDocumentChanged);
          collaborationService.off('cursor-changed', handleCursorChanged);
          collaborationService.off('room-joined', handleRoomJoined);
          collaborationService.off('room-left', handleRoomLeft);
        }

        if (cursorUpdateTimeoutRef.current) {
          clearTimeout(cursorUpdateTimeoutRef.current);
        }
      };
    } catch (error) {
      console.error('Failed to setup collaboration:', error);
      // Return empty cleanup function on error
      return () => {};
    }
  }

  // Global keyboard shortcut handler for command palette and zoom
  useEffect(() => {
    const handleGlobalKeyDown = (e) => {
      // Ctrl+Shift+P or Cmd+Shift+P to open command palette
      if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'P') {
        e.preventDefault()
        setShowCommandPalette(true)
      }
      // F1 to open command palette
      if (e.key === 'F1') {
        e.preventDefault()
        setShowCommandPalette(true)
      }
    }

    const handleWheel = (e) => {
      // Ctrl+Scroll zoom functionality
      if (settings.editor.mouseWheelZoom && (e.ctrlKey || e.metaKey) && editorRef.current) {
        e.preventDefault()
        const editor = editorRef.current
        const currentFontSize = editor.getOption(monacoRef.current.editor.EditorOption.fontSize)
        const delta = e.deltaY > 0 ? -1 : 1
        const newFontSize = Math.max(8, Math.min(72, currentFontSize + delta))

        editor.updateOptions({ fontSize: newFontSize })

        // Update settings store
        const { updateSetting } = useAppStore.getState()
        updateSetting('editor', 'fontSize', newFontSize)
      }
    }

    document.addEventListener('keydown', handleGlobalKeyDown)
    document.addEventListener('wheel', handleWheel, { passive: false })

    return () => {
      document.removeEventListener('keydown', handleGlobalKeyDown)
      document.removeEventListener('wheel', handleWheel)
    }
  }, [settings.editor.mouseWheelZoom])

  return (
    <div className={`h-full w-full relative ${className}`}>
      <Editor
        height="100%"
        language={language}
        value={value}
        onChange={handleEditorChange}
        onMount={handleEditorDidMount}
        theme={(() => {
          switch (theme) {
            case 'github-dark': return 'github-dark'
            case 'catppuccin-mocha': return 'catppuccin-mocha'
            case 'catppuccin-latte': return 'catppuccin-latte'
            case 'gruvbox-dark': return 'gruvbox-dark'
            case 'dracula': return 'dracula'
            case 'one-dark-pro': return 'one-dark-pro'
            default: return currentTheme.monaco === 'vs' ? 'vs' : 'github-dark'
          }
        })()}
        options={{
          readOnly,
          automaticLayout: true,
          // Apply settings from store
          fontSize: settings.editor.fontSize,
          fontFamily: settings.editor.fontFamily,
          tabSize: settings.editor.tabSize,
          insertSpaces: settings.editor.insertSpaces,
          wordWrap: settings.editor.wordWrap,
          lineNumbers: settings.editor.lineNumbers,
          minimap: { enabled: settings.editor.minimap },
          scrollBeyondLastLine: settings.editor.scrollBeyondLastLine,
          cursorBlinking: settings.editor.cursorBlinking,
          cursorStyle: settings.editor.cursorStyle,
          renderWhitespace: settings.editor.renderWhitespace,
          bracketPairColorization: { enabled: settings.editor.bracketPairColorization },
          autoClosingBrackets: settings.editor.autoClosingBrackets,
          autoClosingQuotes: settings.editor.autoClosingQuotes,
          smoothScrolling: settings.editor.smoothScrolling,
          mouseWheelZoom: settings.editor.mouseWheelZoom,
          quickSuggestions: settings.editor.quickSuggestions,
          parameterHints: { enabled: settings.editor.parameterHints },
          codeLens: settings.editor.codeLens,
          folding: settings.editor.folding,
          foldingStrategy: settings.editor.foldingStrategy,
          showFoldingControls: settings.editor.showFoldingControls,
          matchBrackets: settings.editor.matchBrackets,
          renderLineHighlight: settings.editor.renderLineHighlight,
          highlightActiveIndentGuide: settings.editor.highlightActiveIndentGuide,
          rulers: settings.editor.rulers,
          // Disable Monaco's built-in command palette
          quickSuggestionsDelay: 100,
        }}
        loading={
          <div className="flex items-center justify-center h-full">
            <div className="text-ide-text-muted">Loading editor...</div>
          </div>
        }
      />

      {/* Custom Command Palette */}
      <CommandPalette
        isOpen={showCommandPalette}
        onClose={() => setShowCommandPalette(false)}
        editorRef={editorRef}
      />

      {/* Remote Cursors for Collaboration */}
      {isCollaborating && (
        <RemoteCursors
          editor={editorRef.current}
          monaco={monacoRef.current}
          cursors={remoteCursors}
        />
      )}
    </div>
  )
}

export default MonacoEditor
