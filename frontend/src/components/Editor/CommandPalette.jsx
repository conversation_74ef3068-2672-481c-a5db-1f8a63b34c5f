import React, { useState, useEffect, useRef } from 'react'
import { Search, Command, FileText, Settings, Palette, Zap, Code, Terminal, FolderOpen, Save, Copy, Scissors, RotateCcw, RotateCw, ChevronRight, Maximize2, Minimize2 } from 'lucide-react'
import { useAppStore } from '../../stores/appStore'
import { clsx } from 'clsx'

const CommandPalette = ({ isOpen, onClose, editorRef }) => {
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedIndex, setSelectedIndex] = useState(0)
  const [filteredCommands, setFilteredCommands] = useState([])
  const searchInputRef = useRef(null)
  const { theme, setTheme, splitGroup, activeGroupId, currentFile, toggleZenMode, settings } = useAppStore()

  // Command definitions
  const commands = [
    {
      id: 'file.save',
      title: 'File: Save',
      description: 'Save the current file',
      icon: Save,
      shortcut: 'Ctrl+S',
      category: 'File',
      action: () => {
        if (editorRef?.current) {
          // Trigger save action
          editorRef.current.trigger('keyboard', 'editor.action.save', {})
        }
      }
    },
    {
      id: 'file.saveAll',
      title: 'File: Save All',
      description: 'Save all open files',
      icon: Save,
      shortcut: 'Ctrl+K S',
      category: 'File',
      action: () => {
        // Implement save all logic
        console.log('Save all files')
      }
    },
    {
      id: 'editor.action.formatDocument',
      title: 'Format Document',
      description: 'Format the entire document',
      icon: Code,
      shortcut: 'Shift+Alt+F',
      category: 'Editor',
      action: () => {
        if (editorRef?.current) {
          editorRef.current.trigger('keyboard', 'editor.action.formatDocument', {})
        }
      }
    },
    {
      id: 'editor.action.commentLine',
      title: 'Toggle Line Comment',
      description: 'Toggle line comment',
      icon: Code,
      shortcut: 'Ctrl+/',
      category: 'Editor',
      action: () => {
        if (editorRef?.current) {
          editorRef.current.trigger('keyboard', 'editor.action.commentLine', {})
        }
      }
    },
    {
      id: 'editor.action.blockComment',
      title: 'Toggle Block Comment',
      description: 'Toggle block comment',
      icon: Code,
      shortcut: 'Shift+Alt+A',
      category: 'Editor',
      action: () => {
        if (editorRef?.current) {
          editorRef.current.trigger('keyboard', 'editor.action.blockComment', {})
        }
      }
    },
    {
      id: 'editor.action.copyLinesDownAction',
      title: 'Copy Line Down',
      description: 'Copy line down',
      icon: Copy,
      shortcut: 'Shift+Alt+Down',
      category: 'Editor',
      action: () => {
        if (editorRef?.current) {
          editorRef.current.trigger('keyboard', 'editor.action.copyLinesDownAction', {})
        }
      }
    },
    {
      id: 'editor.action.moveLinesDownAction',
      title: 'Move Line Down',
      description: 'Move line down',
      icon: ChevronRight,
      shortcut: 'Alt+Down',
      category: 'Editor',
      action: () => {
        if (editorRef?.current) {
          editorRef.current.trigger('keyboard', 'editor.action.moveLinesDownAction', {})
        }
      }
    },
    {
      id: 'editor.action.moveLinesUpAction',
      title: 'Move Line Up',
      description: 'Move line up',
      icon: ChevronRight,
      shortcut: 'Alt+Up',
      category: 'Editor',
      action: () => {
        if (editorRef?.current) {
          editorRef.current.trigger('keyboard', 'editor.action.moveLinesUpAction', {})
        }
      }
    },
    {
      id: 'editor.action.deleteLines',
      title: 'Delete Line',
      description: 'Delete entire line',
      icon: Scissors,
      shortcut: 'Ctrl+Shift+K',
      category: 'Editor',
      action: () => {
        if (editorRef?.current) {
          editorRef.current.trigger('keyboard', 'editor.action.deleteLines', {})
        }
      }
    },
    {
      id: 'undo',
      title: 'Undo',
      description: 'Undo last action',
      icon: RotateCcw,
      shortcut: 'Ctrl+Z',
      category: 'Editor',
      action: () => {
        if (editorRef?.current) {
          editorRef.current.trigger('keyboard', 'undo', {})
        }
      }
    },
    {
      id: 'redo',
      title: 'Redo',
      description: 'Redo last action',
      icon: RotateCw,
      shortcut: 'Ctrl+Y',
      category: 'Editor',
      action: () => {
        if (editorRef?.current) {
          editorRef.current.trigger('keyboard', 'redo', {})
        }
      }
    },
    {
      id: 'split.right',
      title: 'Split Editor Right',
      description: 'Split the editor to the right',
      icon: Code,
      shortcut: 'Ctrl+\\',
      category: 'View',
      action: () => {
        if (currentFile) {
          splitGroup(activeGroupId, 'horizontal', currentFile)
        }
      }
    },
    {
      id: 'split.down',
      title: 'Split Editor Down',
      description: 'Split the editor downward',
      icon: Code,
      shortcut: 'Ctrl+Shift+\\',
      category: 'View',
      action: () => {
        if (currentFile) {
          splitGroup(activeGroupId, 'vertical', currentFile)
        }
      }
    },
    {
      id: 'theme.github-dark',
      title: 'Theme: GitHub Dark',
      description: 'Switch to GitHub Dark theme',
      icon: Palette,
      category: 'Theme',
      action: () => setTheme('github-dark')
    },
    {
      id: 'theme.catppuccin-mocha',
      title: 'Theme: Catppuccin Mocha',
      description: 'Switch to Catppuccin Mocha theme',
      icon: Palette,
      category: 'Theme',
      action: () => setTheme('catppuccin-mocha')
    },
    {
      id: 'theme.catppuccin-latte',
      title: 'Theme: Catppuccin Latte',
      description: 'Switch to Catppuccin Latte theme',
      icon: Palette,
      category: 'Theme',
      action: () => setTheme('catppuccin-latte')
    },
    {
      id: 'theme.gruvbox-dark',
      title: 'Theme: Gruvbox Dark',
      description: 'Switch to Gruvbox Dark theme',
      icon: Palette,
      category: 'Theme',
      action: () => setTheme('gruvbox-dark')
    },
    {
      id: 'theme.dracula',
      title: 'Theme: Dracula',
      description: 'Switch to Dracula theme',
      icon: Palette,
      category: 'Theme',
      action: () => setTheme('dracula')
    },
    {
      id: 'theme.one-dark-pro',
      title: 'Theme: One Dark Pro',
      description: 'Switch to One Dark Pro theme',
      icon: Palette,
      category: 'Theme',
      action: () => setTheme('one-dark-pro')
    },
    {
      id: 'zen.toggle',
      title: settings.ui.zenMode ? 'Exit Zen Mode' : 'Enter Zen Mode',
      description: settings.ui.zenMode ? 'Exit distraction-free coding mode' : 'Enter distraction-free coding mode',
      icon: settings.ui.zenMode ? Minimize2 : Maximize2,
      shortcut: 'Ctrl+K Z',
      category: 'View',
      action: () => toggleZenMode()
    }
  ]

  // Filter commands based on search query
  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredCommands(commands)
    } else {
      const filtered = commands.filter(command =>
        command.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        command.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        command.category.toLowerCase().includes(searchQuery.toLowerCase())
      )
      setFilteredCommands(filtered)
    }
    setSelectedIndex(0)
  }, [searchQuery])

  // Focus search input when opened
  useEffect(() => {
    if (isOpen && searchInputRef.current) {
      searchInputRef.current.focus()
    }
  }, [isOpen])

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e) => {
      if (!isOpen) return

      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault()
          setSelectedIndex(prev => 
            prev < filteredCommands.length - 1 ? prev + 1 : 0
          )
          break
        case 'ArrowUp':
          e.preventDefault()
          setSelectedIndex(prev => 
            prev > 0 ? prev - 1 : filteredCommands.length - 1
          )
          break
        case 'Enter':
          e.preventDefault()
          if (filteredCommands[selectedIndex]) {
            executeCommand(filteredCommands[selectedIndex])
          }
          break
        case 'Escape':
          e.preventDefault()
          onClose()
          break
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [isOpen, filteredCommands, selectedIndex, onClose])

  const executeCommand = (command) => {
    command.action()
    onClose()
    setSearchQuery('')
  }

  const getCategoryIcon = (category) => {
    switch (category) {
      case 'File': return FileText
      case 'Editor': return Code
      case 'View': return FolderOpen
      case 'Theme': return Palette
      case 'Terminal': return Terminal
      case 'Settings': return Settings
      default: return Command
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 flex items-start justify-center pt-[15vh] command-palette-backdrop">
      <div className="command-palette-container">
        {/* Search Input */}
        <div className="command-palette-header">
          <Search size={18} className="text-ide-text-muted flex-shrink-0" />
          <input
            ref={searchInputRef}
            type="text"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder="Type a command or search..."
            className="command-palette-input"
          />
        </div>

        {/* Commands List */}
        <div className="command-palette-list">
          {filteredCommands.length > 0 ? (
            filteredCommands.map((command, index) => {
              const IconComponent = command.icon
              const CategoryIcon = getCategoryIcon(command.category)
              
              return (
                <div
                  key={command.id}
                  className={clsx(
                    'command-palette-item',
                    index === selectedIndex && 'selected'
                  )}
                  onClick={() => executeCommand(command)}
                >
                  <div className="flex items-center space-x-3 flex-1 min-w-0">
                    <IconComponent size={16} className="text-ide-accent flex-shrink-0" />
                    <div className="flex-1 min-w-0">
                      <div className="command-title">{command.title}</div>
                      <div className="command-description">{command.description}</div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2 flex-shrink-0">
                    <div className="command-category">
                      <CategoryIcon size={12} />
                      <span>{command.category}</span>
                    </div>
                    {command.shortcut && (
                      <div className="command-shortcut">
                        {command.shortcut}
                      </div>
                    )}
                  </div>
                </div>
              )
            })
          ) : (
            <div className="command-palette-empty">
              <Command size={24} className="text-ide-text-muted mb-2" />
              <div className="text-ide-text-muted">No commands found</div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="command-palette-footer">
          <div className="flex items-center space-x-4 text-xs text-ide-text-muted">
            <span>↑↓ Navigate</span>
            <span>↵ Execute</span>
            <span>Esc Close</span>
          </div>
        </div>
      </div>
    </div>
  )
}

export default CommandPalette
