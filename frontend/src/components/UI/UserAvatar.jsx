import React from 'react'

const UserAvatar = ({ name = "Owaiss", size = "md" }) => {
  // Size configurations
  const sizeClasses = {
    sm: "w-8 h-8 text-xs",
    md: "w-9 h-9 text-sm",
    lg: "w-12 h-12 text-base"
  }

  // Create a modern gradient avatar similar to the provided image
  // Using a blue-cyan gradient that matches our UI aesthetic
  const avatarGradient = "bg-gradient-to-br from-cyan-400 via-blue-500 to-purple-600"
  
  // Get initials from name
  const getInitials = (name) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  return (
    <div className={`${sizeClasses[size]} ${avatarGradient} rounded-full flex items-center justify-center font-semibold text-white shadow-lg relative overflow-hidden flex-shrink-0`}>
      {/* Gradient overlay for depth */}
      <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent rounded-full" />

      {/* Simple face elements to match the provided design */}
      <div className="relative z-10 flex flex-col items-center justify-center">
        {/* Eyes */}
        <div className="flex space-x-1 mb-0.5">
          <div className="w-0.5 h-0.5 bg-white/90 rounded-full" />
          <div className="w-0.5 h-0.5 bg-white/90 rounded-full" />
        </div>

        {/* Smile */}
        <div className="w-2 h-1 border-b border-white/90 rounded-full" style={{ borderBottomWidth: '1px' }} />
      </div>
    </div>
  )
}

export default UserAvatar
