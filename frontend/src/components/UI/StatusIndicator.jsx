import React from 'react'
import { clsx } from 'clsx'

const StatusIndicator = ({ status, label, size = 'sm', showPulse = false }) => {
  const statusColors = {
    // System health statuses
    healthy: 'bg-ide-success',
    unhealthy: 'bg-ide-error',
    unknown: 'bg-ide-warning',
    loading: 'bg-ide-text-muted animate-pulse',

    // Project statuses
    running: 'bg-ide-success',
    stopped: 'bg-ide-warning',
    not_deployed: 'bg-ide-text-muted',
    error: 'bg-ide-error',
    creating: 'bg-ide-accent animate-pulse',
    container_failed: 'bg-ide-error',
  }

  const sizes = {
    xs: 'w-2 h-2',
    sm: 'w-3 h-3',
    md: 'w-4 h-4',
    lg: 'w-5 h-5',
  }

  const getStatusColor = () => {
    const baseColor = statusColors[status] || statusColors.unknown
    if (showPulse && (status === 'running' || status === 'healthy')) {
      return `${baseColor} animate-pulse`
    }
    return baseColor
  }

  const shouldShowPulse = status === 'healthy' || (showPulse && status === 'running')

  return (
    <div className="flex items-center space-x-2">
      <div className="relative">
        <div
          className={clsx(
            'rounded-full',
            sizes[size],
            getStatusColor()
          )}
        />
        {shouldShowPulse && (
          <div
            className={clsx(
              'absolute inset-0 rounded-full animate-ping opacity-75',
              statusColors[status] || statusColors.unknown
            )}
          />
        )}
      </div>
      {label && (
        <span className={clsx(
          'text-sm font-medium',
          status === 'healthy' ? 'text-ide-success' :
          status === 'unhealthy' ? 'text-ide-error' :
          status === 'unknown' ? 'text-ide-warning' :
          'text-ide-text-muted'
        )}>
          {label}
        </span>
      )}
    </div>
  )
}

export default StatusIndicator
