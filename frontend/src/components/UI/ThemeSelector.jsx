import React, { useState } from 'react'
import { Palette, Check, ChevronDown } from 'lucide-react'
import { useAppStore } from '../../stores/appStore'
import { getThemesByType } from '../../config/themes'
import { clsx } from 'clsx'

const ThemeSelector = ({ className = '' }) => {
  const { theme, setTheme } = useAppStore()
  const [isOpen, setIsOpen] = useState(false)
  const themesByType = getThemesByType()

  const currentTheme = [...themesByType.dark, ...themesByType.light].find(t => t.key === theme)

  const handleThemeSelect = (themeKey) => {
    setTheme(themeKey)
    setIsOpen(false)
  }

  return (
    <div className={`relative ${className}`}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-2 px-3 py-2 text-ide-text-muted hover:text-ide-text transition-all duration-200 rounded-lg hover:bg-ide-surface/40"
      >
        <div className="flex items-center space-x-2">
          <Palette size={16} />
          <span className="text-sm font-medium">{currentTheme?.name || 'Theme'}</span>
        </div>
        <ChevronDown
          size={14}
          className={`transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`}
        />
      </button>

      {isOpen && (
        <>
          {/* Backdrop */}
          <div 
            className="fixed inset-0 z-10" 
            onClick={() => setIsOpen(false)}
          />
          
          {/* Dropdown */}
          <div className="absolute top-full left-0 mt-2 w-64 bg-ide-surface border border-ide-border rounded-lg shadow-xl z-20 animate-fade-in-up">
            <div className="p-2">
              {/* Dark Themes */}
              <div className="mb-3">
                <h4 className="text-xs font-medium text-ide-text-muted uppercase tracking-wide mb-2 px-2">
                  Dark Themes
                </h4>
                <div className="space-y-1">
                  {themesByType.dark.map((themeOption) => (
                    <button
                      key={themeOption.key}
                      onClick={() => handleThemeSelect(themeOption.key)}
                      className={clsx(
                        'w-full flex items-center justify-between px-3 py-2 rounded-md text-sm transition-colors duration-200',
                        theme === themeOption.key
                          ? 'bg-ide-accent text-white'
                          : 'hover:bg-ide-surface-hover text-ide-text'
                      )}
                    >
                      <div className="flex items-center space-x-3">
                        <div 
                          className="w-4 h-4 rounded-full border border-ide-border"
                          style={{ backgroundColor: themeOption.colors.accent }}
                        />
                        <span>{themeOption.name}</span>
                      </div>
                      {theme === themeOption.key && (
                        <Check size={16} />
                      )}
                    </button>
                  ))}
                </div>
              </div>

              {/* Light Themes */}
              {themesByType.light.length > 0 && (
                <div>
                  <h4 className="text-xs font-medium text-ide-text-muted uppercase tracking-wide mb-2 px-2">
                    Light Themes
                  </h4>
                  <div className="space-y-1">
                    {themesByType.light.map((themeOption) => (
                      <button
                        key={themeOption.key}
                        onClick={() => handleThemeSelect(themeOption.key)}
                        className={clsx(
                          'w-full flex items-center justify-between px-3 py-2 rounded-md text-sm transition-colors duration-200',
                          theme === themeOption.key
                            ? 'bg-ide-accent text-white'
                            : 'hover:bg-ide-surface-hover text-ide-text'
                        )}
                      >
                        <div className="flex items-center space-x-3">
                          <div 
                            className="w-4 h-4 rounded-full border border-ide-border"
                            style={{ backgroundColor: themeOption.colors.accent }}
                          />
                          <span>{themeOption.name}</span>
                        </div>
                        {theme === themeOption.key && (
                          <Check size={16} />
                        )}
                      </button>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </>
      )}
    </div>
  )
}

export default ThemeSelector
