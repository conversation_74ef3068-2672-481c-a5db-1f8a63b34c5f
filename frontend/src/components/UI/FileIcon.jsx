import React from 'react'
import {
  File,
  Folder,
  FolderOpen,
  FileText,
  FileCode,
  FileImage,
  Database,
  Settings,
  Terminal,
  Archive,
  Video,
  Music,
  Zap,
  Globe,
  Palette,
  Lock,
  Package,
  GitBranch,
  Coffee,
  Code,
  Hash,
  Braces,
  FileJson,
  FileX,
  Image,
  Play
} from 'lucide-react'

const FileIcon = ({ fileName, isDirectory = false, isExpanded = false, size = 16, className = '' }) => {
  if (isDirectory) {
    return isExpanded ? 
      <FolderOpen size={size} className={`text-blue-400 ${className}`} /> : 
      <Folder size={size} className={`text-blue-400 ${className}`} />
  }

  const getFileIcon = () => {
    const ext = fileName.split('.').pop()?.toLowerCase()
    const name = fileName.toLowerCase()

    // Special files by name
    if (name === 'package.json' || name === 'package-lock.json') {
      return <Package size={size} className={`text-green-500 ${className}`} />
    }
    if (name === 'dockerfile' || name.startsWith('dockerfile.')) {
      return <Package size={size} className={`text-blue-500 ${className}`} />
    }
    if (name === '.gitignore' || name === '.gitattributes') {
      return <GitBranch size={size} className={`text-orange-500 ${className}`} />
    }
    if (name === 'readme.md' || name === 'readme.txt') {
      return <FileText size={size} className={`text-blue-400 ${className}`} />
    }
    if (name === '.env' || name.startsWith('.env.')) {
      return <Settings size={size} className={`text-yellow-500 ${className}`} />
    }
    if (name === 'requirements.txt') {
      return <FileText size={size} className={`text-blue-400 ${className}`} />
    }

    // Icons by extension
    switch (ext) {
      // JavaScript/TypeScript
      case 'js':
      case 'mjs':
        return (
          <div className={`relative ${className}`}>
            <Code size={size} className="text-yellow-400" />
            <span className="absolute -bottom-1 -right-1 text-[8px] font-bold text-yellow-400 bg-ide-surface rounded px-0.5">JS</span>
          </div>
        )
      case 'jsx':
        return (
          <div className={`relative ${className}`}>
            <Code size={size} className="text-cyan-400" />
            <span className="absolute -bottom-1 -right-1 text-[8px] font-bold text-cyan-400 bg-ide-surface rounded px-0.5">JSX</span>
          </div>
        )
      case 'ts':
        return (
          <div className={`relative ${className}`}>
            <Code size={size} className="text-blue-500" />
            <span className="absolute -bottom-1 -right-1 text-[8px] font-bold text-blue-500 bg-ide-surface rounded px-0.5">TS</span>
          </div>
        )
      case 'tsx':
        return (
          <div className={`relative ${className}`}>
            <Code size={size} className="text-blue-400" />
            <span className="absolute -bottom-1 -right-1 text-[8px] font-bold text-blue-400 bg-ide-surface rounded px-0.5">TSX</span>
          </div>
        )

      // Web
      case 'html':
      case 'htm':
        return (
          <div className={`relative ${className}`}>
            <Globe size={size} className="text-orange-500" />
            <span className="absolute -bottom-1 -right-1 text-[8px] font-bold text-orange-500 bg-ide-surface rounded px-0.5">HTML</span>
          </div>
        )
      case 'css':
        return (
          <div className={`relative ${className}`}>
            <Palette size={size} className="text-blue-400" />
            <span className="absolute -bottom-1 -right-1 text-[8px] font-bold text-blue-400 bg-ide-surface rounded px-0.5">CSS</span>
          </div>
        )
      case 'scss':
      case 'sass':
        return (
          <div className={`relative ${className}`}>
            <Palette size={size} className="text-pink-400" />
            <span className="absolute -bottom-1 -right-1 text-[8px] font-bold text-pink-400 bg-ide-surface rounded px-0.5">SCSS</span>
          </div>
        )

      // Backend Languages
      case 'py':
        return (
          <div className={`relative ${className}`}>
            <Code size={size} className="text-blue-400" />
            <span className="absolute -bottom-1 -right-1 text-[8px] font-bold text-blue-400 bg-ide-surface rounded px-0.5">PY</span>
          </div>
        )
      case 'java':
        return (
          <div className={`relative ${className}`}>
            <Coffee size={size} className="text-red-500" />
            <span className="absolute -bottom-1 -right-1 text-[8px] font-bold text-red-500 bg-ide-surface rounded px-0.5">JAVA</span>
          </div>
        )
      case 'go':
        return (
          <div className={`relative ${className}`}>
            <Code size={size} className="text-cyan-500" />
            <span className="absolute -bottom-1 -right-1 text-[8px] font-bold text-cyan-500 bg-ide-surface rounded px-0.5">GO</span>
          </div>
        )
      case 'rs':
        return (
          <div className={`relative ${className}`}>
            <Code size={size} className="text-orange-600" />
            <span className="absolute -bottom-1 -right-1 text-[8px] font-bold text-orange-600 bg-ide-surface rounded px-0.5">RS</span>
          </div>
        )
      case 'php':
        return (
          <div className={`relative ${className}`}>
            <Code size={size} className="text-purple-400" />
            <span className="absolute -bottom-1 -right-1 text-[8px] font-bold text-purple-400 bg-ide-surface rounded px-0.5">PHP</span>
          </div>
        )

      // Data formats
      case 'json':
        return (
          <div className={`relative ${className}`}>
            <Braces size={size} className="text-yellow-500" />
            <span className="absolute -bottom-1 -right-1 text-[8px] font-bold text-yellow-500 bg-ide-surface rounded px-0.5">JSON</span>
          </div>
        )
      case 'xml':
        return (
          <div className={`relative ${className}`}>
            <Code size={size} className="text-orange-500" />
            <span className="absolute -bottom-1 -right-1 text-[8px] font-bold text-orange-500 bg-ide-surface rounded px-0.5">XML</span>
          </div>
        )
      case 'yaml':
      case 'yml':
        return (
          <div className={`relative ${className}`}>
            <FileText size={size} className="text-red-400" />
            <span className="absolute -bottom-1 -right-1 text-[8px] font-bold text-red-400 bg-ide-surface rounded px-0.5">YAML</span>
          </div>
        )

      // Documentation
      case 'md':
      case 'mdx':
        return (
          <div className={`relative ${className}`}>
            <FileText size={size} className="text-blue-400" />
            <span className="absolute -bottom-1 -right-1 text-[8px] font-bold text-blue-400 bg-ide-surface rounded px-0.5">MD</span>
          </div>
        )
      case 'txt':
        return <FileText size={size} className={`text-gray-400 ${className}`} />
      case 'pdf':
        return <FileText size={size} className={`text-red-500 ${className}`} />

      // Images
      case 'png':
      case 'jpg':
      case 'jpeg':
      case 'gif':
      case 'bmp':
      case 'webp':
        return <FileImage size={size} className={`text-green-500 ${className}`} />
      case 'svg':
        return <FileImage size={size} className={`text-yellow-500 ${className}`} />
      case 'ico':
        return <FileImage size={size} className={`text-blue-500 ${className}`} />

      // Media
      case 'mp4':
      case 'avi':
      case 'mov':
      case 'wmv':
      case 'flv':
      case 'webm':
        return <Video size={size} className={`text-purple-500 ${className}`} />
      case 'mp3':
      case 'wav':
      case 'flac':
      case 'aac':
      case 'ogg':
        return <Music size={size} className={`text-pink-500 ${className}`} />

      // Archives
      case 'zip':
      case 'rar':
      case 'tar':
      case 'gz':
      case '7z':
        return <Archive size={size} className={`text-yellow-600 ${className}`} />

      // Shell/Scripts
      case 'sh':
      case 'bash':
      case 'zsh':
      case 'fish':
      case 'ps1':
      case 'bat':
        return <Terminal size={size} className={`text-green-400 ${className}`} />

      // Config files
      case 'conf':
      case 'config':
      case 'ini':
      case 'cfg':
        return <Settings size={size} className={`text-gray-500 ${className}`} />

      // Database
      case 'sql':
      case 'db':
      case 'sqlite':
        return <Database size={size} className={`text-blue-500 ${className}`} />

      // Security
      case 'key':
      case 'pem':
      case 'crt':
      case 'cert':
        return <Lock size={size} className={`text-red-400 ${className}`} />

      // Build/Package files
      case 'lock':
        return <Lock size={size} className={`text-yellow-500 ${className}`} />

      // Energy/Performance
      case 'wasm':
        return <Zap size={size} className={`text-purple-600 ${className}`} />

      default:
        return <File size={size} className={`text-gray-400 ${className}`} />
    }
  }

  return getFileIcon()
}

export default FileIcon
