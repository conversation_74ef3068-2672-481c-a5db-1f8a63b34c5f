import React from 'react'
import { clsx } from 'clsx'

const Input = ({ 
  label,
  error,
  className = '',
  ...props 
}) => {
  return (
    <div className="space-y-1">
      {label && (
        <label className="block text-sm font-medium text-ide-text">
          {label}
        </label>
      )}
      <input
        className={clsx(
          'ide-input w-full',
          error && 'border-ide-error focus:ring-ide-error',
          className
        )}
        {...props}
      />
      {error && (
        <p className="text-sm text-ide-error">{error}</p>
      )}
    </div>
  )
}

export default Input
