import React, { useState, useEffect } from 'react'
import { X, Folder, Code, Globe, Smartphone, Monitor, Plus, ChevronRight } from 'lucide-react'
import { Button } from '../UI'
import { projectService } from '../../services/api'

const ProjectTypeCard = ({ type, icon: Icon, title, description, selected, onClick }) => (
  <div
    className={`group relative overflow-hidden rounded-2xl p-6 cursor-pointer transition-all duration-300 border-2 ${
      selected
        ? 'border-ide-accent bg-ide-accent/5 shadow-lg shadow-ide-accent/20'
        : 'border-ide-border bg-ide-surface hover:border-ide-accent/50 hover:bg-ide-surface-hover hover:shadow-lg'
    }`}
    onClick={onClick}
  >
    <div className="relative z-10">
      <div className="flex items-center justify-between mb-4">
        <div className={`w-14 h-14 rounded-2xl flex items-center justify-center transition-all duration-300 ${
          selected
            ? 'bg-ide-accent text-white shadow-lg shadow-ide-accent/30'
            : 'bg-ide-bg text-ide-text-muted group-hover:bg-ide-accent group-hover:text-white group-hover:shadow-lg group-hover:shadow-ide-accent/30'
        }`}>
          <Icon size={24} />
        </div>
        {selected && (
          <div className="w-8 h-8 bg-ide-accent rounded-full flex items-center justify-center animate-scale-in shadow-lg">
            <ChevronRight className="text-white" size={16} />
          </div>
        )}
      </div>
      <div className="space-y-3">
        <h3 className={`text-lg font-bold transition-colors duration-300 ${
          selected ? 'text-ide-accent' : 'text-ide-text group-hover:text-ide-accent'
        }`}>
          {title}
        </h3>
        <p className="text-ide-text-muted leading-relaxed">{description}</p>
      </div>
    </div>
    {/* Subtle background pattern */}
    <div className="absolute inset-0 opacity-5">
      <div className="absolute top-4 right-4 w-20 h-20 rounded-full bg-gradient-to-br from-ide-accent to-transparent"></div>
    </div>
  </div>
)

const TemplateCard = ({ template, selected, onClick }) => (
  <div
    className={`group relative overflow-hidden rounded-2xl p-6 cursor-pointer transition-all duration-300 border-2 ${
      selected
        ? 'border-ide-accent bg-ide-accent/5 shadow-lg shadow-ide-accent/20'
        : 'border-ide-border bg-ide-surface hover:border-ide-accent/50 hover:bg-ide-surface-hover hover:shadow-lg'
    }`}
    onClick={onClick}
  >
    <div className="relative z-10">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-4">
          <div className={`w-12 h-12 rounded-xl flex items-center justify-center text-2xl transition-all duration-300 ${
            selected
              ? 'bg-ide-accent text-white shadow-lg shadow-ide-accent/30'
              : 'bg-ide-bg text-ide-text-muted group-hover:bg-ide-accent group-hover:text-white group-hover:shadow-lg group-hover:shadow-ide-accent/30'
          }`}>
            {template.icon}
          </div>
          <h4 className={`text-lg font-bold transition-colors duration-300 ${
            selected ? 'text-ide-accent' : 'text-ide-text group-hover:text-ide-accent'
          }`}>
            {template.name}
          </h4>
        </div>
        {selected && (
          <div className="w-7 h-7 bg-ide-accent rounded-full flex items-center justify-center animate-scale-in shadow-lg">
            <ChevronRight className="text-white" size={14} />
          </div>
        )}
      </div>
      <p className="text-ide-text-muted mb-4 leading-relaxed">{template.description}</p>
      {template.features && (
        <div className="flex flex-wrap gap-2">
          {template.features.map((feature, index) => (
            <span
              key={index}
              className={`px-3 py-1.5 text-xs rounded-lg font-medium transition-all duration-200 ${
                selected
                  ? 'bg-ide-accent/10 text-ide-accent border border-ide-accent/20'
                  : 'bg-ide-bg text-ide-text-muted border border-ide-border group-hover:border-ide-accent/30 group-hover:text-ide-accent'
              }`}
            >
              {feature}
            </span>
          ))}
        </div>
      )}
    </div>
    {/* Subtle background pattern */}
    <div className="absolute inset-0 opacity-5">
      <div className="absolute bottom-4 left-4 w-16 h-16 rounded-full bg-gradient-to-tr from-ide-accent to-transparent"></div>
    </div>
  </div>
)

const NewProjectModal = ({ isOpen, onClose, onSuccess }) => {
  const [step, setStep] = useState(1)
  const [loading, setLoading] = useState(false)
  const [templates, setTemplates] = useState([])
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    type: 'general',
    template: 'blank',
    language: 'javascript',
    framework: 'none',
    environment: 'development'
  })
  const [errors, setErrors] = useState({})

  const projectTypes = [
    {
      id: 'general',
      icon: Folder,
      title: 'General Project',
      description: 'A flexible project for any type of development'
    },
    {
      id: 'web',
      icon: Globe,
      title: 'Web Application',
      description: 'Frontend web application with modern frameworks'
    },
    {
      id: 'api',
      icon: Code,
      title: 'API Server',
      description: 'Backend API service with REST endpoints'
    },
    {
      id: 'mobile',
      icon: Smartphone,
      title: 'Mobile App',
      description: 'Mobile application development'
    },
    {
      id: 'desktop',
      icon: Monitor,
      title: 'Desktop App',
      description: 'Desktop application development'
    }
  ]

  useEffect(() => {
    if (isOpen) {
      loadTemplates()
      resetForm()
    }
  }, [isOpen])

  const loadTemplates = async () => {
    try {
      const templatesData = await projectService.getTemplates()
      setTemplates(templatesData)
    } catch (error) {
      console.error('Failed to load templates:', error)
      // Fallback templates
      setTemplates([
        {
          id: 'blank',
          name: 'Blank Project',
          description: 'Start with an empty project',
          language: 'general',
          icon: '📄',
          features: ['Basic README', 'Empty workspace']
        },
        {
          id: 'javascript',
          name: 'JavaScript/Node.js',
          description: 'Node.js project with package.json and sample code',
          language: 'javascript',
          icon: '🟨',
          features: ['package.json', 'index.js', 'npm scripts']
        },
        {
          id: 'react',
          name: 'React App',
          description: 'React application with modern setup',
          language: 'react',
          icon: '⚛️',
          features: ['React 18', 'JSX components', 'CSS styling']
        },
        {
          id: 'python',
          name: 'Python Project',
          description: 'Python project with requirements.txt and sample code',
          language: 'python',
          icon: '🐍',
          features: ['main.py', 'requirements.txt', 'Python 3.11+']
        },
        {
          id: 'java',
          name: 'Java Project',
          description: 'Java project with Maven and JUnit testing',
          language: 'java',
          icon: '☕',
          features: ['Maven build', 'JUnit 5', 'Java 17']
        }
      ])
    }
  }

  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      type: 'general',
      template: 'blank',
      language: 'javascript',
      framework: 'none',
      environment: 'development'
    })
    setErrors({})
    setStep(1)
  }

  const validateStep = (stepNumber) => {
    const newErrors = {}

    if (stepNumber === 1) {
      if (!formData.name.trim()) {
        newErrors.name = 'Project name is required'
      } else if (formData.name.length < 2) {
        newErrors.name = 'Project name must be at least 2 characters'
      } else if (formData.name.length > 50) {
        newErrors.name = 'Project name must be less than 50 characters'
      } else if (!/^[a-zA-Z0-9\s\-_]+$/.test(formData.name)) {
        newErrors.name = 'Project name can only contain letters, numbers, spaces, hyphens, and underscores'
      }

      if (formData.description && formData.description.length > 200) {
        newErrors.description = 'Description must be less than 200 characters'
      }
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleNext = () => {
    if (validateStep(step)) {
      setStep(step + 1)
    }
  }

  const handleBack = () => {
    setStep(step - 1)
    setErrors({})
  }

  const handleSubmit = async () => {
    if (!validateStep(step)) return

    setLoading(true)
    try {
      const project = await projectService.createProject(formData)
      onSuccess(project)
      onClose()
    } catch (error) {
      console.error('Failed to create project:', error)
      setErrors({ submit: 'Failed to create project. Please try again.' })
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: null }))
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 modal-backdrop flex items-center justify-center z-50 p-4">
      <div
        className="bg-ide-surface border border-ide-border rounded-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden shadow-2xl animate-scale-in theme-transition"
        style={{
          boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.05)'
        }}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-8 border-b border-ide-border">
          <div className="space-y-3">
            <h2 className="text-3xl font-bold text-ide-text">Create New Project</h2>
            <div className="flex items-center space-x-4">
              <p className="text-ide-text-muted">
                Step {step} of 3: {step === 1 ? 'Project Details' : step === 2 ? 'Project Type' : 'Template Selection'}
              </p>
              {/* Modern Progress Indicator */}
              <div className="flex items-center space-x-2">
                {[1, 2, 3].map((stepNumber) => (
                  <div key={stepNumber} className="flex items-center">
                    <div
                      className={`w-3 h-3 rounded-full transition-all duration-500 ${
                        stepNumber < step
                          ? 'bg-ide-success shadow-lg shadow-ide-success/30'
                          : stepNumber === step
                          ? 'bg-ide-accent shadow-lg shadow-ide-accent/30 animate-pulse'
                          : 'bg-ide-border'
                      }`}
                    />
                    {stepNumber < 3 && (
                      <div className={`w-8 h-0.5 mx-2 transition-all duration-500 ${
                        stepNumber < step ? 'bg-ide-success' : 'bg-ide-border'
                      }`} />
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-3 hover:bg-ide-surface-hover rounded-xl transition-all duration-200 group"
          >
            <X size={24} className="text-ide-text-muted group-hover:text-ide-text transition-colors" />
          </button>
        </div>

        {/* Content */}
        <div className="p-8 overflow-y-auto max-h-[calc(90vh-240px)] ide-scrollbar">
          {step === 1 && (
            <div className="space-y-8 animate-fade-in-up">
              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-semibold text-ide-text mb-3">
                    Project Name *
                  </label>
                  <div className="relative">
                    <input
                      type="text"
                      value={formData.name}
                      onChange={(e) => handleInputChange('name', e.target.value)}
                      placeholder="Enter your project name"
                      className={`w-full px-4 py-4 bg-ide-bg border rounded-xl text-ide-text placeholder-ide-text-muted enhanced-focus transition-all duration-200 text-lg ${
                        errors.name ? 'border-red-500 focus:ring-red-500/30' : 'border-ide-border hover:border-ide-accent/50'
                      }`}
                    />
                    {errors.name && (
                      <div className="mt-2 flex items-center space-x-2">
                        <div className="w-1.5 h-1.5 bg-red-500 rounded-full"></div>
                        <p className="text-red-500 text-sm font-medium">{errors.name}</p>
                      </div>
                    )}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-semibold text-ide-text mb-3">
                    Description
                  </label>
                  <div className="relative">
                    <textarea
                      value={formData.description}
                      onChange={(e) => handleInputChange('description', e.target.value)}
                      placeholder="Describe your project (optional)"
                      rows={4}
                      className={`w-full px-4 py-4 bg-ide-bg border rounded-xl text-ide-text placeholder-ide-text-muted enhanced-focus resize-none transition-all duration-200 ${
                        errors.description ? 'border-red-500 focus:ring-red-500/30' : 'border-ide-border hover:border-ide-accent/50'
                      }`}
                    />
                    {errors.description && (
                      <div className="mt-2 flex items-center space-x-2">
                        <div className="w-1.5 h-1.5 bg-red-500 rounded-full"></div>
                        <p className="text-red-500 text-sm font-medium">{errors.description}</p>
                      </div>
                    )}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-semibold text-ide-text mb-3">
                    Environment
                  </label>
                  <select
                    value={formData.environment}
                    onChange={(e) => handleInputChange('environment', e.target.value)}
                    className="w-full px-4 py-4 bg-ide-bg border border-ide-border hover:border-ide-accent/50 rounded-xl text-ide-text enhanced-focus transition-all duration-200 text-lg"
                  >
                    <option value="development">Development</option>
                    <option value="staging">Staging</option>
                    <option value="production">Production</option>
                  </select>
                </div>
              </div>
            </div>
          )}

          {step === 2 && (
            <div className="space-y-8 animate-fade-in-up">
              <div>
                <h3 className="text-2xl font-bold text-ide-text mb-3">Choose Project Type</h3>
                <p className="text-ide-text-muted mb-8 text-lg">Select the type of project you want to create</p>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {projectTypes.map((type, index) => (
                    <div
                      key={type.id}
                      style={{ animationDelay: `${index * 100}ms` }}
                      className="animate-fade-in-up"
                    >
                      <ProjectTypeCard
                        type={type.id}
                        icon={type.icon}
                        title={type.title}
                        description={type.description}
                        selected={formData.type === type.id}
                        onClick={() => handleInputChange('type', type.id)}
                      />
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {step === 3 && (
            <div className="space-y-8 animate-fade-in-up">
              <div>
                <h3 className="text-2xl font-bold text-ide-text mb-3">Select Template</h3>
                <p className="text-ide-text-muted mb-8 text-lg">Choose a template to get started quickly</p>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {templates.map((template, index) => (
                    <div
                      key={template.id}
                      style={{ animationDelay: `${index * 100}ms` }}
                      className="animate-fade-in-up"
                    >
                      <TemplateCard
                        template={template}
                        selected={formData.template === template.id}
                        onClick={() => {
                          handleInputChange('template', template.id)
                          handleInputChange('language', template.language || 'javascript')
                        }}
                      />
                    </div>
                  ))}
                </div>
              </div>

              {errors.submit && (
                <div className="p-4 bg-red-500/10 border border-red-500 rounded-xl animate-fade-in-up">
                  <div className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                    <p className="text-red-500 font-medium">{errors.submit}</p>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-8 border-t border-ide-border bg-ide-surface/50">
          <div className="flex items-center space-x-4">
            <div className="text-ide-text-muted">
              {step === 1 && 'Enter project details'}
              {step === 2 && 'Choose project type'}
              {step === 3 && 'Select template'}
            </div>
          </div>

          <div className="flex items-center space-x-4">
            {step > 1 && (
              <button
                onClick={handleBack}
                disabled={loading}
                className="px-8 py-3 rounded-xl font-medium text-ide-text hover:bg-ide-surface-hover border border-ide-border hover:border-ide-accent/50 transition-all duration-200 button-press disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Back
              </button>
            )}
            {step < 3 ? (
              <button
                onClick={handleNext}
                className="px-8 py-3 rounded-xl font-medium flex items-center space-x-2 bg-ide-accent hover:bg-ide-accent-hover text-white transition-all duration-200 shadow-lg hover:shadow-xl button-press"
              >
                <span>Next</span>
                <ChevronRight size={18} />
              </button>
            ) : (
              <button
                onClick={handleSubmit}
                disabled={loading}
                className="px-8 py-3 rounded-xl font-medium flex items-center space-x-2 bg-ide-accent hover:bg-ide-accent-hover text-white transition-all duration-200 shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed button-press"
              >
                {loading ? (
                  <>
                    <div className="smooth-spin w-5 h-5 border-2 border-white border-t-transparent rounded-full" />
                    <span>Creating...</span>
                  </>
                ) : (
                  <>
                    <Plus size={18} />
                    <span>Create Project</span>
                  </>
                )}
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

export default NewProjectModal
