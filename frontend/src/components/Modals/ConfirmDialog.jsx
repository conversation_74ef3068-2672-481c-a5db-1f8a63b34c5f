import React from 'react'
import { AlertTriangle, X } from 'lucide-react'
import { Button } from '../UI'

const ConfirmDialog = ({
  isOpen,
  onClose,
  onConfirm,
  title = "Confirm Action",
  message = "Are you sure you want to proceed?",
  confirmText = "Confirm",
  cancelText = "Cancel",
  variant = "danger",
  loading = false,
  icon: Icon = AlertTriangle
}) => {
  if (!isOpen) return null

  const handleConfirm = () => {
    onConfirm()
  }

  const handleCancel = () => {
    if (!loading) {
      onClose()
    }
  }

  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget && !loading) {
      onClose()
    }
  }

  return (
    <div 
      className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
      onClick={handleBackdropClick}
    >
      <div className="bg-ide-surface border border-ide-border rounded-lg shadow-xl max-w-md w-full animate-fade-in">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-ide-border">
          <div className="flex items-center space-x-3">
            <div className={`p-2 rounded-lg ${
              variant === 'danger' ? 'bg-ide-error/20' : 'bg-ide-warning/20'
            }`}>
              <Icon className={`${
                variant === 'danger' ? 'text-ide-error' : 'text-ide-warning'
              }`} size={20} />
            </div>
            <h2 className="text-lg font-semibold text-ide-text">{title}</h2>
          </div>
          {!loading && (
            <button
              onClick={handleCancel}
              className="p-1 hover:bg-ide-border rounded transition-colors"
            >
              <X size={20} className="text-ide-text-muted" />
            </button>
          )}
        </div>

        {/* Content */}
        <div className="p-6">
          <p className="text-ide-text-muted leading-relaxed">{message}</p>
        </div>

        {/* Actions */}
        <div className="flex items-center justify-end space-x-3 p-6 border-t border-ide-border">
          <Button
            variant="ghost"
            onClick={handleCancel}
            disabled={loading}
          >
            {cancelText}
          </Button>
          <Button
            variant={variant === 'danger' ? 'danger' : 'primary'}
            onClick={handleConfirm}
            loading={loading}
          >
            {confirmText}
          </Button>
        </div>
      </div>
    </div>
  )
}

export default ConfirmDialog
