import React from 'react'
import { Plus, X, Terminal } from 'lucide-react'
import { useAppStore } from '../../stores/appStore'
import { clsx } from 'clsx'

const TerminalTabs = ({ onNewTerminal }) => {
  const { 
    terminalSessions, 
    activeTerminalId, 
    setActiveTerminal, 
    removeTerminalSession 
  } = useAppStore()

  const handleTabClick = (sessionId) => {
    setActiveTerminal(sessionId)
  }

  const handleTabClose = (e, sessionId) => {
    e.stopPropagation()
    removeTerminalSession(sessionId)
  }

  const handleNewTerminal = () => {
    if (onNewTerminal) {
      onNewTerminal()
    }
  }

  return (
    <div className="ide-tabs-container flex items-end">
      <div className="flex items-end overflow-x-auto ide-scrollbar flex-1">
        {terminalSessions.map((session, index) => (
          <div
            key={session.id}
            className={clsx(
              'ide-tab flex items-center justify-between group cursor-pointer relative animate-slide-in-right',
              activeTerminalId === session.id && 'ide-tab-active'
            )}
            style={{ animationDelay: `${index * 50}ms` }}
            onClick={() => handleTabClick(session.id)}
          >
            <div className="flex items-center space-x-2 min-w-0 flex-1">
              <Terminal size={14} className="text-ide-accent" />
              <span className="truncate text-sm font-medium">
                {session.name || `Terminal ${session.id}`}
              </span>
            </div>
            <button
              className="opacity-0 group-hover:opacity-100 hover:bg-ide-border rounded-full p-1 transition-all duration-200 ml-2 flex-shrink-0"
              onClick={(e) => handleTabClose(e, session.id)}
              title="Close terminal"
            >
              <X size={10} />
            </button>
          </div>
        ))}
      </div>

      <button
        className="p-2 hover:bg-ide-surface-hover text-ide-text-muted hover:text-ide-text transition-colors rounded-tl-lg mx-1"
        onClick={handleNewTerminal}
        title="New Terminal"
      >
        <Plus size={16} />
      </button>
    </div>
  )
}

export default TerminalTabs
