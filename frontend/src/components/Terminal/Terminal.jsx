import React, { useEffect, useRef, useState } from 'react'
import { Terminal as XTerm } from '@xterm/xterm'
import { FitAddon } from '@xterm/addon-fit'
import { WebLinksAddon } from '@xterm/addon-web-links'
import '@xterm/xterm/css/xterm.css'
import { io } from 'socket.io-client'
import { terminalService } from '../../services/terminal'
import { useAppStore } from '../../stores/appStore'
import { getTheme } from '../../config/themes'

const Terminal = React.forwardRef(({
  projectId,
  sessionId,
  onSessionCreated,
  onSessionEnded,
  className = '',
  style = {}
}, ref) => {
  const terminalRef = useRef(null)
  const xtermRef = useRef(null)
  const fitAddonRef = useRef(null)
  const socketRef = useRef(null)
  const [isConnected, setIsConnected] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState(null)
  const { theme } = useAppStore()
  const currentTheme = getTheme(theme)

  useEffect(() => {
    if (!terminalRef.current) return

    // Initialize xterm.js with theme-aware colors
    const terminal = new XTerm({
      theme: {
        background: currentTheme.terminal.background,
        foreground: currentTheme.terminal.foreground,
        cursor: currentTheme.terminal.cursor,
        selection: currentTheme.terminal.selection,
        black: currentTheme.type === 'dark' ? '#000000' : '#2e3440',
        red: currentTheme.type === 'dark' ? '#cd3131' : '#bf616a',
        green: currentTheme.type === 'dark' ? '#0dbc79' : '#a3be8c',
        yellow: currentTheme.type === 'dark' ? '#e5e510' : '#ebcb8b',
        blue: currentTheme.type === 'dark' ? '#2472c8' : '#81a1c1',
        magenta: currentTheme.type === 'dark' ? '#bc3fbc' : '#b48ead',
        cyan: currentTheme.type === 'dark' ? '#11a8cd' : '#88c0d0',
        white: currentTheme.type === 'dark' ? '#e5e5e5' : '#e5e9f0',
        brightBlack: currentTheme.type === 'dark' ? '#666666' : '#4c566a',
        brightRed: currentTheme.type === 'dark' ? '#f14c4c' : '#bf616a',
        brightGreen: currentTheme.type === 'dark' ? '#23d18b' : '#a3be8c',
        brightYellow: currentTheme.type === 'dark' ? '#f5f543' : '#ebcb8b',
        brightBlue: currentTheme.type === 'dark' ? '#3b8eea' : '#81a1c1',
        brightMagenta: currentTheme.type === 'dark' ? '#d670d6' : '#b48ead',
        brightCyan: currentTheme.type === 'dark' ? '#29b8db' : '#8fbcbb',
        brightWhite: currentTheme.type === 'dark' ? '#ffffff' : '#eceff4'
      },
      fontFamily: '"Cascadia Code", "Fira Code", "JetBrains Mono", "SF Mono", Monaco, Consolas, "Ubuntu Mono", monospace',
      fontSize: 14,
      lineHeight: 1.2,
      cursorBlink: true,
      cursorStyle: 'block',
      scrollback: 1000,
      tabStopWidth: 4,
      convertEol: true, // Convert \n to \r\n for proper line endings
      disableStdin: false, // Ensure input is enabled
      screenKeys: false, // Disable screen keys to prevent conflicts
      useFlowControl: false // Disable flow control for better performance
    })

    const fitAddon = new FitAddon()
    const webLinksAddon = new WebLinksAddon()

    terminal.loadAddon(fitAddon)
    terminal.loadAddon(webLinksAddon)

    terminal.open(terminalRef.current)
    fitAddon.fit()

    xtermRef.current = terminal
    fitAddonRef.current = fitAddon

    // Note: Terminal input handling will be set up after connection

    // Handle terminal resize
    terminal.onResize(({ cols, rows }) => {
      if (socketRef.current && socketRef.current.connected) {
        socketRef.current.emit('terminal:resize', {
          sessionId: sessionId,
          cols,
          rows
        })
      }
    })

    // Connect to terminal session
    if (sessionId) {
      connectToSession(sessionId)
    } else if (projectId) {
      createAndConnectSession()
    }

    // Handle window resize
    const handleResize = () => {
      if (fitAddonRef.current) {
        fitAddonRef.current.fit()
      }
    }

    window.addEventListener('resize', handleResize)

    return () => {
      window.removeEventListener('resize', handleResize)
      if (socketRef.current) {
        socketRef.current.disconnect()
      }
      if (xtermRef.current) {
        xtermRef.current.dispose()
      }
    }
  }, [])

  const createAndConnectSession = async () => {
    if (!projectId) {
      setError('No project selected')
      return
    }

    setIsLoading(true)
    setError(null)

    try {
      console.log('Creating terminal session for project:', projectId)

      // First, ensure the project container is running
      await ensureProjectRunning()

      const session = await terminalService.createSession({
        projectId,
        shell: 'sh',
        cwd: '/workspace'
      })

      console.log('Terminal session created:', session)

      if (onSessionCreated) {
        onSessionCreated(session)
      }

      await connectToSession(session.sessionId)
    } catch (error) {
      console.error('Failed to create terminal session:', error)
      const errorMessage = error.message || 'Failed to create terminal session'
      setError(errorMessage)
      if (xtermRef.current) {
        xtermRef.current.write(`\r\n\x1b[31m${errorMessage}\x1b[0m\r\n`)
        if (error.message.includes('No running container')) {
          xtermRef.current.write('\r\n\x1b[33mStarting project container...\x1b[0m\r\n')
          // Retry after attempting to start the project
          setTimeout(() => createAndConnectSession(), 2000)
        } else {
          xtermRef.current.write('\r\n\x1b[33mTip: Make sure the project is started first\x1b[0m\r\n')
        }
      }
    } finally {
      setIsLoading(false)
    }
  }

  const ensureProjectRunning = async () => {
    try {
      // Check if project is running, if not start it
      const response = await fetch(`http://localhost:3000/api/projects/${projectId}`)
      if (response.ok) {
        const project = await response.json()
        if (project.status !== 'running') {
          console.log('Project not running, starting...')
          await terminalService.startProject(projectId)
          // Wait a moment for container to be ready
          await new Promise(resolve => setTimeout(resolve, 3000))
        }
      }
    } catch (error) {
      console.warn('Could not check/start project:', error)
      // Continue anyway, let the terminal creation handle the error
    }
  }

  const connectToSession = async (targetSessionId) => {
    if (!targetSessionId) return

    setIsLoading(true)
    setError(null)

    try {
      // Clean up existing connection
      if (socketRef.current) {
        socketRef.current.disconnect()
        socketRef.current = null
      }

      console.log(`Connecting to terminal session: ${targetSessionId}`)

      // Create Socket.IO connection
      const socket = io('http://localhost:3000', {
        transports: ['websocket', 'polling'],
        timeout: 15000,
        reconnection: true,
        reconnectionAttempts: 5,
        reconnectionDelay: 2000
      })
      socketRef.current = socket

      // Store current session ID
      let currentSessionId = targetSessionId

      socket.on('connect', () => {
        console.log('✅ Terminal Socket.IO connected')
        // Join terminal session with project ID for auto-creation
        socket.emit('terminal:join', {
          sessionId: currentSessionId,
          projectId: projectId
        })
      })

      socket.on('terminal:joined', (data) => {
        console.log('✅ Joined terminal session:', data)
        setIsConnected(true)
        setError(null)
        setIsLoading(false)

        if (xtermRef.current) {
          xtermRef.current.write('\r\n\x1b[32m✅ Terminal connected successfully\x1b[0m\r\n')
          xtermRef.current.focus()

          // Clear any existing handlers to prevent duplicates
          xtermRef.current.onData(() => {})

          // Update current session ID if it changed
          if (data.sessionId && data.sessionId !== currentSessionId) {
            console.log('🔄 Session ID updated from', currentSessionId, 'to', data.sessionId)
            currentSessionId = data.sessionId
          }

          // Set up input handling with proper error handling
          xtermRef.current.onData((inputData) => {
            try {
              if (socket.connected && inputData !== undefined && inputData !== null && currentSessionId) {
                console.log('📤 Sending input to session', currentSessionId, ':', JSON.stringify(inputData))
                socket.emit('terminal:input', {
                  sessionId: currentSessionId,
                  data: String(inputData) // Ensure it's a string
                })
              } else {
                console.warn('⚠️ Cannot send input - socket connected:', socket.connected, 'inputData:', inputData !== undefined && inputData !== null, 'sessionId:', currentSessionId)
              }
            } catch (error) {
              console.error('❌ Error sending input:', error)
            }
          })
        }
      })

      socket.on('terminal:session-created', (data) => {
        console.log('New terminal session created:', data)
        // Update session ID if backend created a new one
        if (data.newSessionId && data.oldSessionId === currentSessionId) {
          currentSessionId = data.newSessionId
          console.log(`Session ID updated from ${data.oldSessionId} to ${data.newSessionId}`)
          if (onSessionCreated) {
            onSessionCreated({ sessionId: data.newSessionId })
          }
        }
      })

      socket.on('terminal:output', (data) => {
        try {
          if (xtermRef.current && data.data && data.data.length > 0) {
            console.log('📥 Received output for session', data.sessionId, ':', JSON.stringify(data.data.substring(0, 100)) + (data.data.length > 100 ? '...' : ''))
            // Write the output directly to the terminal
            xtermRef.current.write(data.data)
          } else {
            console.warn('⚠️ Cannot write output - terminal:', !!xtermRef.current, 'data:', !!data.data, 'data length:', data.data ? data.data.length : 0)
          }
        } catch (error) {
          console.error('❌ Error writing output:', error)
        }
      })

      socket.on('terminal:ended', (data) => {
        setIsConnected(false)
        if (xtermRef.current) {
          xtermRef.current.write(`\r\n\x1b[33mProcess exited with code ${data.exitCode}\x1b[0m\r\n`)
        }
        if (onSessionEnded) {
          onSessionEnded(data.exitCode)
        }
      })

      socket.on('terminal:error', (data) => {
        console.error('❌ Terminal error:', data.error)
        setError(data.error)
        setIsLoading(false)

        if (xtermRef.current) {
          xtermRef.current.write(`\r\n\x1b[31m❌ Error: ${data.error}\x1b[0m\r\n`)

          // If container not found, try to start it
          if (data.error.includes('No running container') || data.error.includes('not found') || data.error.includes('Container is not running')) {
            xtermRef.current.write('\r\n\x1b[33m🔄 Attempting to start project container...\x1b[0m\r\n')
            setTimeout(() => {
              ensureProjectRunning().then(() => {
                setTimeout(() => createAndConnectSession(), 1000)
              })
            }, 2000)
          }
        }
      })

      socket.on('disconnect', (reason) => {
        console.log('Terminal Socket.IO disconnected:', reason)
        setIsConnected(false)
        setIsLoading(false)

        if (xtermRef.current) {
          xtermRef.current.write('\r\n\x1b[33mConnection closed\x1b[0m\r\n')
        }
      })

      socket.on('reconnect', (attemptNumber) => {
        console.log('Terminal Socket.IO reconnected after', attemptNumber, 'attempts')
        if (xtermRef.current) {
          xtermRef.current.write('\r\n\x1b[32mReconnected to terminal\x1b[0m\r\n')
        }
      })

      socket.on('connect_error', (error) => {
        console.error('Terminal Socket.IO connection error:', error)
        setError('WebSocket connection failed')
        setIsConnected(false)
        if (xtermRef.current) {
          xtermRef.current.write('\r\n\x1b[31mConnection error\x1b[0m\r\n')
        }
      })
    } catch (error) {
      console.error('Failed to connect to terminal session:', error)
      setError('Failed to connect to terminal')
    } finally {
      setIsLoading(false)
    }
  }



  const executeCommand = (command) => {
    if (socketRef.current && socketRef.current.connected) {
      socketRef.current.emit('terminal:input', {
        sessionId: sessionId,
        data: command + '\n'
      })
    }
  }

  const clear = () => {
    if (xtermRef.current) {
      xtermRef.current.clear()
    }
  }

  const focus = () => {
    if (xtermRef.current) {
      xtermRef.current.focus()
    }
  }

  const fit = () => {
    if (fitAddonRef.current) {
      fitAddonRef.current.fit()
    }
  }

  // Expose methods to parent component
  React.useImperativeHandle(ref, () => ({
    executeCommand,
    clear,
    focus,
    fit,
    isConnected,
    terminal: xtermRef.current
  }), [isConnected])

  return (
    <div className={`terminal-container ${className}`} style={style}>
      {isLoading && (
        <div className="absolute inset-0 bg-ide-bg/80 flex items-center justify-center z-10">
          <div className="flex items-center space-x-2 text-ide-text">
            <div className="animate-spin w-4 h-4 border-2 border-ide-accent border-t-transparent rounded-full" />
            <span>Connecting to terminal...</span>
          </div>
        </div>
      )}
      
      {error && (
        <div className="absolute top-2 right-2 bg-red-500/10 border border-red-500/20 rounded-lg p-2 z-10">
          <p className="text-red-500 text-sm">{error}</p>
        </div>
      )}

      <div 
        ref={terminalRef} 
        className="w-full h-full"
        style={{ minHeight: '200px' }}
      />
      
      {!isConnected && !isLoading && (
        <div className="absolute inset-0 bg-ide-bg/90 flex items-center justify-center">
          <div className="text-center">
            <p className="text-ide-text-muted mb-2">Terminal not connected</p>
            <button
              onClick={() => projectId ? createAndConnectSession() : null}
              className="px-3 py-1 bg-ide-accent text-white rounded text-sm hover:bg-ide-accent/80"
              disabled={!projectId}
            >
              Reconnect
            </button>
          </div>
        </div>
      )}
    </div>
  )
})

Terminal.displayName = 'Terminal'

export default Terminal
export { Terminal }
