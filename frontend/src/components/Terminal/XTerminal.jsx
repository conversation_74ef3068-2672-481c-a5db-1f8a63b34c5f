import React, { useRef, useEffect, useState } from 'react'
import Terminal from './Terminal'
import { useAppStore } from '../../stores/appStore'

const XTerminal = React.forwardRef(({
  projectId: propProjectId,
  sessionId,
  onData,
  className = ''
}, ref) => {
  const terminalRef = useRef(null)
  const { currentProject } = useAppStore()
  const [currentSessionId, setCurrentSessionId] = useState(sessionId)

  // Use prop projectId first, then current project ID
  const projectId = propProjectId || currentProject?.id

  useEffect(() => {
    if (sessionId) {
      setCurrentSessionId(sessionId)
    }
  }, [sessionId])

  const handleSessionCreated = (session) => {
    setCurrentSessionId(session.sessionId)
  }

  const handleSessionEnded = (exitCode) => {
    console.log('Terminal session ended with code:', exitCode)
  }

  // Expose methods via ref for backward compatibility
  React.useImperativeHandle(ref, () => ({
    writeData: (data) => {
      if (terminalRef.current) {
        terminalRef.current.executeCommand(data)
      }
    },
    clear: () => {
      if (terminalRef.current) {
        terminalRef.current.clear()
      }
    },
    focus: () => {
      if (terminalRef.current) {
        terminalRef.current.focus()
      }
    },
    terminal: terminalRef.current?.terminal,
  }), [])

  return (
    <div className={`h-full w-full ${className}`}>
      <Terminal
        ref={terminalRef}
        projectId={projectId}
        sessionId={currentSessionId}
        onSessionCreated={handleSessionCreated}
        onSessionEnded={handleSessionEnded}
        className="h-full w-full"
        style={{ minHeight: '200px' }}
      />
    </div>
  )
})

export default XTerminal
