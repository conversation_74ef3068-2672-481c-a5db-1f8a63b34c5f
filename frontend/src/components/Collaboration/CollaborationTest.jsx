import React, { useState, useEffect } from 'react';
import { Play, Users, FileText, Wifi, WifiOff } from 'lucide-react';
import collaborationService from '../../services/collaboration';

const CollaborationTest = () => {
  const [testResults, setTestResults] = useState([]);
  const [isRunning, setIsRunning] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState('disconnected');
  const [currentRoom, setCurrentRoom] = useState(null);
  const [collaborators, setCollaborators] = useState([]);

  useEffect(() => {
    // Set up collaboration event listeners
    const handleRoomJoined = (data) => {
      setCurrentRoom(data.roomId);
      setCollaborators(data.collaborators);
      addTestResult('✅ Successfully joined collaboration room', 'success');
    };

    const handleUserJoined = (data) => {
      setCollaborators(data.collaborators);
      addTestResult(`👋 User ${data.user.nickname} joined the room`, 'info');
    };

    const handleUserLeft = (data) => {
      setCollaborators(data.collaborators);
      addTestResult(`👋 User left the room`, 'info');
    };

    const handleDocumentChanged = (data) => {
      addTestResult(`📝 Document updated by user ${data.userId}`, 'info');
    };

    const handleCursorChanged = (data) => {
      addTestResult(`🖱️ Cursor moved by user ${data.userId}`, 'info');
    };

    const handleError = (data) => {
      addTestResult(`❌ Collaboration error: ${data.error}`, 'error');
    };

    const handleConnected = () => {
      setConnectionStatus('connected');
      addTestResult('🔗 Connected to collaboration server', 'success');
    };

    const handleDisconnected = () => {
      setConnectionStatus('disconnected');
      setCurrentRoom(null);
      setCollaborators([]);
      addTestResult('🔌 Disconnected from collaboration server', 'warning');
    };

    // Add event listeners
    collaborationService.on('room-joined', handleRoomJoined);
    collaborationService.on('user-joined', handleUserJoined);
    collaborationService.on('user-left', handleUserLeft);
    collaborationService.on('document-changed', handleDocumentChanged);
    collaborationService.on('cursor-changed', handleCursorChanged);
    collaborationService.on('error', handleError);
    collaborationService.on('connected', handleConnected);
    collaborationService.on('disconnected', handleDisconnected);

    return () => {
      // Clean up event listeners
      collaborationService.off('room-joined', handleRoomJoined);
      collaborationService.off('user-joined', handleUserJoined);
      collaborationService.off('user-left', handleUserLeft);
      collaborationService.off('document-changed', handleDocumentChanged);
      collaborationService.off('cursor-changed', handleCursorChanged);
      collaborationService.off('error', handleError);
      collaborationService.off('connected', handleConnected);
      collaborationService.off('disconnected', handleDisconnected);
    };
  }, []);

  const addTestResult = (message, type = 'info') => {
    const timestamp = new Date().toLocaleTimeString();
    setTestResults(prev => [...prev, { message, type, timestamp }]);
  };

  const runCollaborationTests = async () => {
    setIsRunning(true);
    setTestResults([]);
    addTestResult('🚀 Starting collaboration tests...', 'info');

    try {
      // Test 1: Connect to collaboration server
      addTestResult('Test 1: Connecting to collaboration server...', 'info');
      await collaborationService.connect();
      
      // Test 2: Join a test room
      addTestResult('Test 2: Joining test collaboration room...', 'info');
      await collaborationService.joinRoom('test-project', '/test-file.js', 'Test User');
      
      // Test 3: Send document change
      addTestResult('Test 3: Sending document change...', 'info');
      collaborationService.sendDocumentChange('console.log("Hello from collaboration test!");');
      
      // Test 4: Send cursor update
      addTestResult('Test 4: Sending cursor update...', 'info');
      collaborationService.sendCursorUpdate({ line: 0, column: 10 });
      
      // Test 5: Update nickname
      addTestResult('Test 5: Updating nickname...', 'info');
      await collaborationService.updateNickname('Test User Updated');
      
      // Test 6: Get room info
      addTestResult('Test 6: Getting room information...', 'info');
      const roomInfo = await collaborationService.getRoomInfo();
      addTestResult(`📊 Room info: ${roomInfo.collaborators.length} collaborators`, 'success');
      
      addTestResult('🎉 All collaboration tests completed successfully!', 'success');
      
    } catch (error) {
      addTestResult(`❌ Test failed: ${error.message}`, 'error');
    } finally {
      setIsRunning(false);
    }
  };

  const leaveRoom = async () => {
    try {
      await collaborationService.leaveRoom();
      addTestResult('👋 Left collaboration room', 'info');
    } catch (error) {
      addTestResult(`❌ Failed to leave room: ${error.message}`, 'error');
    }
  };

  const clearResults = () => {
    setTestResults([]);
  };

  const getStatusColor = () => {
    switch (connectionStatus) {
      case 'connected': return 'text-green-500';
      case 'connecting': return 'text-yellow-500';
      default: return 'text-red-500';
    }
  };

  const getStatusIcon = () => {
    return connectionStatus === 'connected' ? <Wifi size={16} /> : <WifiOff size={16} />;
  };

  return (
    <div className="max-w-4xl mx-auto p-6 bg-ide-bg text-ide-text">
      <div className="bg-ide-surface rounded-lg p-6 shadow-lg">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <Users size={24} className="text-ide-accent" />
            <h2 className="text-2xl font-bold">Collaboration Test Suite</h2>
          </div>
          <div className={`flex items-center space-x-2 ${getStatusColor()}`}>
            {getStatusIcon()}
            <span className="capitalize">{connectionStatus}</span>
          </div>
        </div>

        {/* Current Status */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <div className="bg-ide-bg rounded-lg p-4">
            <div className="flex items-center space-x-2 mb-2">
              <FileText size={16} className="text-ide-accent" />
              <span className="font-medium">Current Room</span>
            </div>
            <p className="text-sm text-ide-text-muted">
              {currentRoom || 'Not in any room'}
            </p>
          </div>
          
          <div className="bg-ide-bg rounded-lg p-4">
            <div className="flex items-center space-x-2 mb-2">
              <Users size={16} className="text-ide-accent" />
              <span className="font-medium">Collaborators</span>
            </div>
            <p className="text-sm text-ide-text-muted">
              {collaborators.length} active
            </p>
          </div>
          
          <div className="bg-ide-bg rounded-lg p-4">
            <div className="flex items-center space-x-2 mb-2">
              <Play size={16} className="text-ide-accent" />
              <span className="font-medium">Test Status</span>
            </div>
            <p className="text-sm text-ide-text-muted">
              {isRunning ? 'Running...' : 'Ready'}
            </p>
          </div>
        </div>

        {/* Controls */}
        <div className="flex flex-wrap gap-3 mb-6">
          <button
            onClick={runCollaborationTests}
            disabled={isRunning}
            className="flex items-center space-x-2 bg-ide-accent hover:bg-ide-accent-hover disabled:bg-ide-border disabled:text-ide-text-muted text-white px-4 py-2 rounded-lg transition-colors"
          >
            <Play size={16} />
            <span>{isRunning ? 'Running Tests...' : 'Run Tests'}</span>
          </button>
          
          {currentRoom && (
            <button
              onClick={leaveRoom}
              className="bg-red-500/10 hover:bg-red-500/20 border border-red-500/20 text-red-400 px-4 py-2 rounded-lg transition-colors"
            >
              Leave Room
            </button>
          )}
          
          <button
            onClick={clearResults}
            className="bg-ide-border hover:bg-ide-border-hover text-ide-text px-4 py-2 rounded-lg transition-colors"
          >
            Clear Results
          </button>
        </div>

        {/* Test Results */}
        <div className="bg-ide-bg rounded-lg p-4">
          <h3 className="font-medium mb-3">Test Results</h3>
          <div className="space-y-2 max-h-96 overflow-y-auto">
            {testResults.length === 0 ? (
              <p className="text-ide-text-muted text-sm">No test results yet. Click "Run Tests" to start.</p>
            ) : (
              testResults.map((result, index) => (
                <div
                  key={index}
                  className={`flex items-start space-x-3 p-2 rounded text-sm ${
                    result.type === 'success' ? 'bg-green-500/10 text-green-400' :
                    result.type === 'error' ? 'bg-red-500/10 text-red-400' :
                    result.type === 'warning' ? 'bg-yellow-500/10 text-yellow-400' :
                    'bg-ide-surface text-ide-text'
                  }`}
                >
                  <span className="text-xs text-ide-text-muted font-mono">
                    {result.timestamp}
                  </span>
                  <span className="flex-1">{result.message}</span>
                </div>
              ))
            )}
          </div>
        </div>

        {/* Collaborators List */}
        {collaborators.length > 0 && (
          <div className="mt-6 bg-ide-bg rounded-lg p-4">
            <h3 className="font-medium mb-3">Active Collaborators</h3>
            <div className="space-y-2">
              {collaborators.map((collaborator, index) => (
                <div key={index} className="flex items-center space-x-3 p-2 bg-ide-surface rounded">
                  <div
                    className="w-3 h-3 rounded-full"
                    style={{ backgroundColor: collaborator.color }}
                  />
                  <span className="font-medium">{collaborator.nickname}</span>
                  <span className="text-xs text-ide-text-muted">
                    {collaborator.userId === collaborationService.getState().currentUser?.userId ? '(You)' : ''}
                  </span>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Instructions */}
        <div className="mt-6 bg-ide-surface rounded-lg p-4">
          <h3 className="font-medium mb-2">How to Test Collaboration</h3>
          <ol className="text-sm text-ide-text-muted space-y-1 list-decimal list-inside">
            <li>Make sure the container manager service is running</li>
            <li>Click "Run Tests" to test basic collaboration functionality</li>
            <li>Open this page in multiple browser tabs to test multi-user collaboration</li>
            <li>Try editing files in the main IDE while collaboration is active</li>
            <li>Check the browser console for detailed WebSocket logs</li>
          </ol>
        </div>
      </div>
    </div>
  );
};

export default CollaborationTest;
