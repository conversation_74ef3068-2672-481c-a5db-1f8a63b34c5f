import React, { useState, useEffect } from 'react';
import { Users, Edit3, Wifi, WifiOff, Settings } from 'lucide-react';
import collaborationService from '../../services/collaboration';

const CollaborationPanel = ({ isOpen, onToggle, currentFile, currentProject }) => {
  const [collaborationState, setCollaborationState] = useState({
    isConnected: false,
    currentRoom: null,
    currentUser: null,
    collaborators: []
  });
  const [isJoining, setIsJoining] = useState(false);
  const [showNicknameEdit, setShowNicknameEdit] = useState(false);
  const [newNickname, setNewNickname] = useState('');
  const [error, setError] = useState(null);

  useEffect(() => {
    // Update state when collaboration service state changes
    const updateState = () => {
      setCollaborationState(collaborationService.getState());
    };

    // Set up event listeners
    collaborationService.on('room-joined', updateState);
    collaborationService.on('room-left', updateState);
    collaborationService.on('user-joined', updateState);
    collaborationService.on('user-left', updateState);
    collaborationService.on('nickname-updated', updateState);
    collaborationService.on('disconnected', updateState);
    collaborationService.on('error', (data) => {
      setError(data.error);
      setTimeout(() => setError(null), 5000);
    });

    // Initial state update
    updateState();

    return () => {
      // Clean up event listeners
      collaborationService.off('room-joined', updateState);
      collaborationService.off('room-left', updateState);
      collaborationService.off('user-joined', updateState);
      collaborationService.off('user-left', updateState);
      collaborationService.off('nickname-updated', updateState);
      collaborationService.off('disconnected', updateState);
    };
  }, []);

  const handleJoinCollaboration = async () => {
    if (!currentFile || !currentProject) {
      setError('Please open a file to start collaboration');
      return;
    }

    setIsJoining(true);
    setError(null);

    try {
      await collaborationService.joinRoom(
        currentProject.id,
        currentFile.path,
        collaborationState.currentUser?.nickname
      );
    } catch (error) {
      console.error('Failed to join collaboration:', error);
      setError(error.message);
    } finally {
      setIsJoining(false);
    }
  };

  const handleLeaveCollaboration = async () => {
    try {
      await collaborationService.leaveRoom();
    } catch (error) {
      console.error('Failed to leave collaboration:', error);
      setError(error.message);
    }
  };

  const handleNicknameUpdate = async () => {
    if (!newNickname.trim()) {
      setError('Nickname cannot be empty');
      return;
    }

    try {
      await collaborationService.updateNickname(newNickname.trim());
      setShowNicknameEdit(false);
      setNewNickname('');
    } catch (error) {
      console.error('Failed to update nickname:', error);
      setError(error.message);
    }
  };

  const formatJoinTime = (joinedAt) => {
    const now = new Date();
    const joined = new Date(joinedAt);
    const diffMs = now - joined;
    const diffMins = Math.floor(diffMs / 60000);
    
    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    const diffHours = Math.floor(diffMins / 60);
    if (diffHours < 24) return `${diffHours}h ago`;
    return joined.toLocaleDateString();
  };

  if (!isOpen) {
    return (
      <button
        onClick={onToggle}
        className="fixed top-4 right-4 z-50 bg-ide-accent text-white p-3 rounded-full shadow-lg hover:bg-ide-accent-hover transition-colors"
        title="Toggle Collaboration Panel"
      >
        <Users size={20} />
      </button>
    );
  }

  return (
    <div className="fixed top-0 right-0 h-full w-80 bg-ide-bg border-l border-ide-border shadow-xl z-40 flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-ide-border">
        <div className="flex items-center space-x-2">
          <Users size={20} className="text-ide-accent" />
          <h3 className="font-semibold text-ide-text">Collaboration</h3>
          {collaborationState.isConnected ? (
            <Wifi size={16} className="text-green-500" />
          ) : (
            <WifiOff size={16} className="text-red-500" />
          )}
        </div>
        <button
          onClick={onToggle}
          className="text-ide-text-muted hover:text-ide-text transition-colors"
        >
          ×
        </button>
      </div>

      {/* Error Display */}
      {error && (
        <div className="mx-4 mt-4 p-3 bg-red-500/10 border border-red-500/20 rounded-lg text-red-400 text-sm">
          {error}
        </div>
      )}

      {/* Content */}
      <div className="flex-1 overflow-y-auto p-4">
        {!collaborationState.currentRoom ? (
          /* Not in collaboration */
          <div className="space-y-4">
            <div className="text-center text-ide-text-muted">
              <Users size={48} className="mx-auto mb-4 opacity-50" />
              <p className="mb-2">Start collaborating on this file</p>
              <p className="text-sm">
                {currentFile ? `Ready to collaborate on ${currentFile.name}` : 'Open a file to start collaboration'}
              </p>
            </div>

            <button
              onClick={handleJoinCollaboration}
              disabled={!currentFile || !currentProject || isJoining}
              className="w-full bg-ide-accent hover:bg-ide-accent-hover disabled:bg-ide-border disabled:text-ide-text-muted text-white py-2 px-4 rounded-lg transition-colors"
            >
              {isJoining ? 'Joining...' : 'Start Collaboration'}
            </button>

            <div className="text-xs text-ide-text-muted space-y-1">
              <p>• Share this file with others in real-time</p>
              <p>• See cursors and selections</p>
              <p>• No authentication required</p>
            </div>
          </div>
        ) : (
          /* In collaboration */
          <div className="space-y-4">
            {/* Current User */}
            <div className="bg-ide-surface rounded-lg p-3">
              <div className="flex items-center justify-between mb-2">
                <h4 className="font-medium text-ide-text">You</h4>
                <button
                  onClick={() => {
                    setShowNicknameEdit(true);
                    setNewNickname(collaborationState.currentUser?.nickname || '');
                  }}
                  className="text-ide-text-muted hover:text-ide-text transition-colors"
                  title="Edit nickname"
                >
                  <Edit3 size={14} />
                </button>
              </div>
              
              {showNicknameEdit ? (
                <div className="space-y-2">
                  <input
                    type="text"
                    value={newNickname}
                    onChange={(e) => setNewNickname(e.target.value)}
                    className="w-full bg-ide-bg border border-ide-border rounded px-2 py-1 text-sm text-ide-text"
                    placeholder="Enter nickname"
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') handleNicknameUpdate();
                      if (e.key === 'Escape') setShowNicknameEdit(false);
                    }}
                    autoFocus
                  />
                  <div className="flex space-x-2">
                    <button
                      onClick={handleNicknameUpdate}
                      className="flex-1 bg-ide-accent hover:bg-ide-accent-hover text-white py-1 px-2 rounded text-xs"
                    >
                      Save
                    </button>
                    <button
                      onClick={() => setShowNicknameEdit(false)}
                      className="flex-1 bg-ide-border hover:bg-ide-border-hover text-ide-text py-1 px-2 rounded text-xs"
                    >
                      Cancel
                    </button>
                  </div>
                </div>
              ) : (
                <div className="flex items-center space-x-2">
                  <div
                    className="w-3 h-3 rounded-full"
                    style={{ backgroundColor: collaborationState.currentUser?.color }}
                  />
                  <span className="text-sm text-ide-text">
                    {collaborationState.currentUser?.nickname || 'You'}
                  </span>
                </div>
              )}
            </div>

            {/* Other Collaborators */}
            <div>
              <h4 className="font-medium text-ide-text mb-2">
                Collaborators ({collaborationState.collaborators.filter(u => u.userId !== collaborationState.currentUser?.userId).length})
              </h4>
              
              {collaborationState.collaborators.filter(u => u.userId !== collaborationState.currentUser?.userId).length === 0 ? (
                <div className="text-center text-ide-text-muted py-4">
                  <p className="text-sm">No other collaborators yet</p>
                  <p className="text-xs mt-1">Share this project to invite others</p>
                </div>
              ) : (
                <div className="space-y-2">
                  {collaborationState.collaborators
                    .filter(user => user.userId !== collaborationState.currentUser?.userId)
                    .map(user => (
                      <div key={user.userId} className="bg-ide-surface rounded-lg p-3">
                        <div className="flex items-center space-x-2">
                          <div
                            className="w-3 h-3 rounded-full"
                            style={{ backgroundColor: user.color }}
                          />
                          <div className="flex-1">
                            <div className="text-sm text-ide-text font-medium">
                              {user.nickname}
                            </div>
                            <div className="text-xs text-ide-text-muted">
                              Joined {formatJoinTime(user.joinedAt)}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                </div>
              )}
            </div>

            {/* Room Info */}
            <div className="bg-ide-surface rounded-lg p-3">
              <h4 className="font-medium text-ide-text mb-2">Session Info</h4>
              <div className="text-xs text-ide-text-muted space-y-1">
                <p>File: {currentFile?.name}</p>
                <p>Project: {currentProject?.name || currentProject?.id}</p>
                <p>Room: {collaborationState.currentRoom}</p>
              </div>
            </div>

            {/* Leave Button */}
            <button
              onClick={handleLeaveCollaboration}
              className="w-full bg-red-500/10 hover:bg-red-500/20 border border-red-500/20 text-red-400 py-2 px-4 rounded-lg transition-colors"
            >
              Leave Collaboration
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default CollaborationPanel;
