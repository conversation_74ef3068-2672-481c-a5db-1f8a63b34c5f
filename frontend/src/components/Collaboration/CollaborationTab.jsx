import React, { useState, useEffect } from 'react';
import { Users, UserPlus, UserMinus, Wifi, WifiOff, Edit3, RefreshCw } from 'lucide-react';
import collaborationService from '../../services/collaboration';
import { useAppStore } from '../../stores/appStore';

const CollaborationTab = () => {
  const { currentProject, currentFile } = useAppStore();
  const [collaborationState, setCollaborationState] = useState({
    isConnected: false,
    isInRoom: false,
    currentRoom: null,
    currentUser: null,
    collaborators: []
  });
  const [isJoining, setIsJoining] = useState(false);
  const [showNicknameEdit, setShowNicknameEdit] = useState(false);
  const [newNickname, setNewNickname] = useState('');
  const [error, setError] = useState(null);

  useEffect(() => {
    const updateState = () => {
      const state = collaborationService.getState();
      setCollaborationState({
        isConnected: collaborationService.isConnected,
        isInRoom: collaborationService.isInRoom(),
        currentRoom: state.currentRoom,
        currentUser: state.currentUser,
        collaborators: state.collaborators || []
      });
    };

    // Update state initially
    updateState();

    // Set up event listeners
    const events = [
      'connected', 'disconnected', 'room-joined', 'room-left',
      'user-joined', 'user-left', 'nickname-updated'
    ];

    events.forEach(event => {
      collaborationService.on(event, updateState);
    });

    collaborationService.on('error', (data) => {
      setError(data.error);
      setTimeout(() => setError(null), 5000);
    });

    // Update status every 3 seconds
    const interval = setInterval(updateState, 3000);

    return () => {
      events.forEach(event => {
        collaborationService.off(event, updateState);
      });
      clearInterval(interval);
    };
  }, []);

  const handleStartCollaboration = async () => {
    if (!currentFile || !currentProject) {
      setError('Please open a file to start collaboration');
      return;
    }

    setIsJoining(true);
    setError(null);

    try {
      await collaborationService.connect();
      await collaborationService.joinRoom(currentProject.id, currentFile.path);
    } catch (error) {
      console.error('Failed to start collaboration:', error);
      setError(error.message);
    } finally {
      setIsJoining(false);
    }
  };

  const handleEndCollaboration = async () => {
    try {
      await collaborationService.leaveRoom();
    } catch (error) {
      console.error('Failed to end collaboration:', error);
      setError(error.message);
    }
  };

  const handleNicknameUpdate = async () => {
    if (!newNickname.trim()) {
      setError('Nickname cannot be empty');
      return;
    }

    try {
      await collaborationService.updateNickname(newNickname.trim());
      setShowNicknameEdit(false);
      setNewNickname('');
    } catch (error) {
      console.error('Failed to update nickname:', error);
      setError(error.message);
    }
  };

  const formatJoinTime = (joinedAt) => {
    const now = new Date();
    const joined = new Date(joinedAt);
    const diffMs = now - joined;
    const diffMins = Math.floor(diffMs / 60000);
    
    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    const diffHours = Math.floor(diffMins / 60);
    if (diffHours < 24) return `${diffHours}h ago`;
    return joined.toLocaleDateString();
  };

  return (
    <div className="sidebar-content-section">
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-sm font-semibold text-ide-text flex items-center space-x-2">
          <Users size={16} />
          <span>Collaboration</span>
        </h3>
        <div className="flex items-center space-x-2">
          {collaborationState.isConnected ? (
            <Wifi size={12} className="text-green-500" />
          ) : (
            <WifiOff size={12} className="text-red-500" />
          )}
          <button
            onClick={() => window.location.reload()}
            className="p-1 hover:bg-ide-surface-hover rounded transition-colors"
            title="Refresh collaboration"
          >
            <RefreshCw size={12} className="text-ide-text-muted" />
          </button>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="mb-4 p-3 bg-red-500/10 border border-red-500/20 rounded-lg text-red-400 text-xs">
          {error}
        </div>
      )}

      {/* Current File Info */}
      {currentFile && (
        <div className="mb-4 p-3 ide-panel">
          <div className="text-xs font-medium text-ide-text mb-1">Current File</div>
          <div className="text-xs text-ide-text-muted truncate">{currentFile.name}</div>
          {currentProject && (
            <div className="text-xs text-ide-text-muted truncate mt-1">
              Project: {currentProject.name || currentProject.id}
            </div>
          )}
        </div>
      )}

      {!collaborationState.isInRoom ? (
        /* Not in collaboration */
        <div className="space-y-4">
          <div className="text-center p-6 ide-panel">
            <Users size={32} className="mx-auto mb-3 text-ide-text-muted opacity-50" />
            <p className="text-sm text-ide-text-muted mb-3">
              {currentFile ? 'Start collaborating on this file' : 'Open a file to start collaboration'}
            </p>
            <button
              onClick={handleStartCollaboration}
              disabled={!currentFile || !currentProject || isJoining}
              className="ide-button-primary w-full flex items-center justify-center space-x-2"
            >
              {isJoining ? (
                <>
                  <div className="w-3 h-3 border border-white border-t-transparent rounded-full animate-spin" />
                  <span>Joining...</span>
                </>
              ) : (
                <>
                  <UserPlus size={14} />
                  <span>Start Collaboration</span>
                </>
              )}
            </button>
          </div>

          <div className="ide-panel p-3">
            <div className="text-xs font-medium text-ide-text mb-2">Features</div>
            <div className="text-xs text-ide-text-muted space-y-1">
              <div>• Real-time document editing</div>
              <div>• See cursors and selections</div>
              <div>• No authentication required</div>
              <div>• Simple user names (User 1, User 2, etc.)</div>
            </div>
          </div>
        </div>
      ) : (
        /* In collaboration */
        <div className="space-y-4">
          {/* Current User */}
          <div className="ide-panel p-3">
            <div className="flex items-center justify-between mb-2">
              <div className="text-xs font-medium text-ide-text">You</div>
              <button
                onClick={() => {
                  setShowNicknameEdit(true);
                  setNewNickname(collaborationState.currentUser?.nickname || '');
                }}
                className="p-1 hover:bg-ide-surface-hover rounded transition-colors"
                title="Edit nickname"
              >
                <Edit3 size={12} className="text-ide-text-muted" />
              </button>
            </div>
            
            {showNicknameEdit ? (
              <div className="space-y-2">
                <input
                  type="text"
                  value={newNickname}
                  onChange={(e) => setNewNickname(e.target.value)}
                  className="w-full bg-ide-bg border border-ide-border rounded px-2 py-1 text-xs text-ide-text"
                  placeholder="Enter nickname"
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') handleNicknameUpdate();
                    if (e.key === 'Escape') setShowNicknameEdit(false);
                  }}
                  autoFocus
                />
                <div className="flex space-x-2">
                  <button
                    onClick={handleNicknameUpdate}
                    className="flex-1 ide-button-primary text-xs py-1"
                  >
                    Save
                  </button>
                  <button
                    onClick={() => setShowNicknameEdit(false)}
                    className="flex-1 ide-button text-xs py-1"
                  >
                    Cancel
                  </button>
                </div>
              </div>
            ) : (
              <div className="flex items-center space-x-2">
                <div
                  className="w-2 h-2 rounded-full"
                  style={{ backgroundColor: collaborationState.currentUser?.color }}
                />
                <span className="text-xs text-ide-text">
                  {collaborationState.currentUser?.nickname || 'You'}
                </span>
              </div>
            )}
          </div>

          {/* Collaborators */}
          <div className="ide-panel p-3">
            <div className="text-xs font-medium text-ide-text mb-2">
              Collaborators ({collaborationState.collaborators.filter(u => u.userId !== collaborationState.currentUser?.userId).length})
            </div>
            
            {collaborationState.collaborators.filter(u => u.userId !== collaborationState.currentUser?.userId).length === 0 ? (
              <div className="text-center py-3">
                <p className="text-xs text-ide-text-muted">No other collaborators yet</p>
                <p className="text-xs text-ide-text-muted mt-1">Share this project to invite others</p>
              </div>
            ) : (
              <div className="space-y-2">
                {collaborationState.collaborators
                  .filter(user => user.userId !== collaborationState.currentUser?.userId)
                  .map(user => (
                    <div key={user.userId} className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <div
                          className="w-2 h-2 rounded-full"
                          style={{ backgroundColor: user.color }}
                        />
                        <div>
                          <div className="text-xs text-ide-text font-medium">
                            {user.nickname}
                          </div>
                          <div className="text-xs text-ide-text-muted">
                            Joined {formatJoinTime(user.joinedAt)}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
              </div>
            )}
          </div>

          {/* Session Info */}
          <div className="ide-panel p-3">
            <div className="text-xs font-medium text-ide-text mb-2">Session Info</div>
            <div className="text-xs text-ide-text-muted space-y-1">
              <div>Room: {collaborationState.currentRoom}</div>
              <div>Status: {collaborationState.isConnected ? 'Connected' : 'Disconnected'}</div>
              <div>Total Users: {collaborationState.collaborators.length}</div>
            </div>
          </div>

          {/* End Collaboration */}
          <button
            onClick={handleEndCollaboration}
            className="w-full bg-red-500/10 hover:bg-red-500/20 border border-red-500/20 text-red-400 py-2 px-3 rounded-lg transition-colors text-xs flex items-center justify-center space-x-2"
          >
            <UserMinus size={12} />
            <span>End Collaboration</span>
          </button>
        </div>
      )}
    </div>
  );
};

export default CollaborationTab;
