import React, { useState, useEffect } from 'react';
import { Users, Wifi, WifiOff, Eye, EyeOff } from 'lucide-react';
import collaborationService from '../../services/collaboration';

const CollaborationStatus = ({ currentFile, currentProject }) => {
  const [isVisible, setIsVisible] = useState(false);
  const [status, setStatus] = useState({
    isConnected: false,
    isInRoom: false,
    currentRoom: null,
    collaborators: [],
    currentUser: null
  });

  useEffect(() => {
    const updateStatus = () => {
      const state = collaborationService.getState();
      setStatus({
        isConnected: collaborationService.isConnected,
        isInRoom: collaborationService.isInRoom(),
        currentRoom: state.currentRoom,
        collaborators: state.collaborators || [],
        currentUser: state.currentUser
      });
    };

    // Update status initially
    updateStatus();

    // Set up event listeners
    const events = [
      'connected', 'disconnected', 'room-joined', 'room-left',
      'user-joined', 'user-left', 'document-changed', 'cursor-changed'
    ];

    events.forEach(event => {
      collaborationService.on(event, updateStatus);
    });

    // Update status every 2 seconds
    const interval = setInterval(updateStatus, 2000);

    return () => {
      events.forEach(event => {
        collaborationService.off(event, updateStatus);
      });
      clearInterval(interval);
    };
  }, []);

  if (!isVisible) {
    return (
      <button
        onClick={() => setIsVisible(true)}
        className="fixed bottom-4 right-4 z-50 bg-ide-accent text-white p-2 rounded-full shadow-lg hover:bg-ide-accent-hover transition-colors"
        title="Show Collaboration Status"
      >
        <Eye size={16} />
      </button>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 z-50 bg-ide-surface border border-ide-border rounded-lg shadow-xl p-4 min-w-[300px]">
      {/* Header */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-2">
          <Users size={16} className="text-ide-accent" />
          <span className="font-medium text-ide-text">Collaboration Status</span>
        </div>
        <button
          onClick={() => setIsVisible(false)}
          className="text-ide-text-muted hover:text-ide-text transition-colors"
        >
          <EyeOff size={16} />
        </button>
      </div>

      {/* Connection Status */}
      <div className="space-y-2 text-sm">
        <div className="flex items-center justify-between">
          <span className="text-ide-text-muted">Connection:</span>
          <div className="flex items-center space-x-1">
            {status.isConnected ? (
              <>
                <Wifi size={12} className="text-green-500" />
                <span className="text-green-500">Connected</span>
              </>
            ) : (
              <>
                <WifiOff size={12} className="text-red-500" />
                <span className="text-red-500">Disconnected</span>
              </>
            )}
          </div>
        </div>

        <div className="flex items-center justify-between">
          <span className="text-ide-text-muted">In Room:</span>
          <span className={status.isInRoom ? 'text-green-500' : 'text-red-500'}>
            {status.isInRoom ? 'Yes' : 'No'}
          </span>
        </div>

        {status.currentRoom && (
          <div className="flex items-center justify-between">
            <span className="text-ide-text-muted">Room:</span>
            <span className="text-ide-text text-xs font-mono">
              {status.currentRoom}
            </span>
          </div>
        )}

        <div className="flex items-center justify-between">
          <span className="text-ide-text-muted">Collaborators:</span>
          <span className="text-ide-text">
            {status.collaborators.length}
          </span>
        </div>

        {status.currentUser && (
          <div className="flex items-center justify-between">
            <span className="text-ide-text-muted">You:</span>
            <div className="flex items-center space-x-1">
              <div
                className="w-2 h-2 rounded-full"
                style={{ backgroundColor: status.currentUser.color }}
              />
              <span className="text-ide-text text-xs">
                {status.currentUser.nickname}
              </span>
            </div>
          </div>
        )}

        {/* Current File Info */}
        {currentFile && (
          <div className="pt-2 border-t border-ide-border">
            <div className="flex items-center justify-between">
              <span className="text-ide-text-muted">File:</span>
              <span className="text-ide-text text-xs">
                {currentFile.name}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-ide-text-muted">Project:</span>
              <span className="text-ide-text text-xs">
                {currentProject?.name || currentProject?.id || 'Unknown'}
              </span>
            </div>
          </div>
        )}

        {/* Collaborators List */}
        {status.collaborators.length > 0 && (
          <div className="pt-2 border-t border-ide-border">
            <div className="text-ide-text-muted text-xs mb-1">Active Users:</div>
            <div className="space-y-1">
              {status.collaborators.map((user, index) => (
                <div key={index} className="flex items-center space-x-2">
                  <div
                    className="w-2 h-2 rounded-full"
                    style={{ backgroundColor: user.color }}
                  />
                  <span className="text-ide-text text-xs">
                    {user.nickname}
                    {user.userId === status.currentUser?.userId && ' (You)'}
                  </span>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Debug Info */}
        {process.env.NODE_ENV === 'development' && (
          <div className="pt-2 border-t border-ide-border">
            <div className="text-ide-text-muted text-xs mb-1">Debug:</div>
            <div className="text-xs text-ide-text-muted space-y-1">
              <div>Service Available: {typeof collaborationService.connect === 'function' ? 'Yes' : 'No'}</div>
              <div>Socket: {collaborationService.socket ? 'Connected' : 'None'}</div>
              <div>Methods: {typeof collaborationService.isInRoom === 'function' ? 'OK' : 'Missing'}</div>
            </div>
          </div>
        )}
      </div>

      {/* Quick Actions */}
      <div className="mt-3 pt-2 border-t border-ide-border">
        <div className="flex space-x-2">
          {!status.isInRoom && currentFile && currentProject && (
            <button
              onClick={async () => {
                try {
                  await collaborationService.connect();
                  await collaborationService.joinRoom(
                    currentProject.id,
                    currentFile.path,
                    `User-${Date.now()}`
                  );
                } catch (error) {
                  console.error('Failed to join collaboration:', error);
                }
              }}
              className="flex-1 bg-ide-accent hover:bg-ide-accent-hover text-white text-xs py-1 px-2 rounded transition-colors"
            >
              Join Room
            </button>
          )}
          
          {status.isInRoom && (
            <button
              onClick={async () => {
                try {
                  await collaborationService.leaveRoom();
                } catch (error) {
                  console.error('Failed to leave collaboration:', error);
                }
              }}
              className="flex-1 bg-red-500/10 hover:bg-red-500/20 border border-red-500/20 text-red-400 text-xs py-1 px-2 rounded transition-colors"
            >
              Leave Room
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default CollaborationStatus;
