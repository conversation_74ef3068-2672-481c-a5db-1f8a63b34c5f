import React, { useState, useEffect } from 'react';
import { Users, UserPlus, UserMinus, Wifi, WifiOff } from 'lucide-react';
import collaborationService from '../../services/collaboration';

const FloatingCollabButton = ({ currentFile, currentProject }) => {
  const [isCollaborating, setIsCollaborating] = useState(false);
  const [isConnected, setIsConnected] = useState(false);
  const [collaborators, setCollaborators] = useState([]);
  const [isJoining, setIsJoining] = useState(false);

  useEffect(() => {
    const updateStatus = () => {
      const state = collaborationService.getState();
      setIsConnected(collaborationService.isConnected);
      setIsCollaborating(collaborationService.isInRoom());
      setCollaborators(state.collaborators || []);
    };

    // Update status initially
    updateStatus();

    // Set up event listeners
    const events = [
      'connected', 'disconnected', 'room-joined', 'room-left',
      'user-joined', 'user-left'
    ];

    events.forEach(event => {
      collaborationService.on(event, updateStatus);
    });

    // Update status every 2 seconds
    const interval = setInterval(updateStatus, 2000);

    return () => {
      events.forEach(event => {
        collaborationService.off(event, updateStatus);
      });
      clearInterval(interval);
    };
  }, []);

  const handleStartCollaboration = async () => {
    if (!currentFile || !currentProject) {
      console.warn('No file or project selected');
      return;
    }

    setIsJoining(true);
    try {
      await collaborationService.connect();
      await collaborationService.joinRoom(
        currentProject.id,
        currentFile.path
      );
      console.log('🎉 Started collaboration successfully');
    } catch (error) {
      console.error('Failed to start collaboration:', error);
    } finally {
      setIsJoining(false);
    }
  };

  const handleEndCollaboration = async () => {
    try {
      await collaborationService.leaveRoom();
      console.log('👋 Ended collaboration');
    } catch (error) {
      console.error('Failed to end collaboration:', error);
    }
  };

  // Don't show if no file is open
  if (!currentFile) {
    return null;
  }

  return (
    <div className="fixed top-4 right-4 z-50 flex flex-col items-end space-y-2">
      {/* Main Collaboration Button */}
      {!isCollaborating ? (
        <button
          onClick={handleStartCollaboration}
          disabled={isJoining}
          className="flex items-center space-x-2 bg-ide-accent hover:bg-ide-accent-hover disabled:bg-ide-border disabled:text-ide-text-muted text-white px-4 py-2 rounded-lg shadow-lg transition-all duration-200 transform hover:scale-105"
          title="Start Collaboration"
        >
          {isJoining ? (
            <>
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
              <span className="text-sm font-medium">Joining...</span>
            </>
          ) : (
            <>
              <UserPlus size={16} />
              <span className="text-sm font-medium">Start Collab</span>
            </>
          )}
        </button>
      ) : (
        <div className="flex flex-col items-end space-y-2">
          {/* Active Collaboration Status */}
          <div className="bg-ide-surface border border-ide-border rounded-lg shadow-lg p-3 min-w-[200px]">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center space-x-2">
                <Users size={16} className="text-ide-accent" />
                <span className="text-sm font-medium text-ide-text">Active Collaboration</span>
              </div>
              <div className="flex items-center space-x-1">
                {isConnected ? (
                  <Wifi size={12} className="text-green-500" />
                ) : (
                  <WifiOff size={12} className="text-red-500" />
                )}
              </div>
            </div>

            {/* Collaborators Count */}
            <div className="flex items-center justify-between text-xs text-ide-text-muted mb-2">
              <span>Collaborators:</span>
              <span className="font-medium">{collaborators.length}</span>
            </div>

            {/* Active Users */}
            {collaborators.length > 0 && (
              <div className="space-y-1 mb-3">
                {collaborators.slice(0, 3).map((user, index) => (
                  <div key={index} className="flex items-center space-x-2">
                    <div
                      className="w-2 h-2 rounded-full"
                      style={{ backgroundColor: user.color }}
                    />
                    <span className="text-xs text-ide-text truncate">
                      {user.nickname}
                    </span>
                  </div>
                ))}
                {collaborators.length > 3 && (
                  <div className="text-xs text-ide-text-muted">
                    +{collaborators.length - 3} more
                  </div>
                )}
              </div>
            )}

            {/* End Collaboration Button */}
            <button
              onClick={handleEndCollaboration}
              className="w-full bg-red-500/10 hover:bg-red-500/20 border border-red-500/20 text-red-400 py-1 px-2 rounded text-xs transition-colors"
            >
              <div className="flex items-center justify-center space-x-1">
                <UserMinus size={12} />
                <span>End Collaboration</span>
              </div>
            </button>
          </div>
        </div>
      )}

      {/* File Info */}
      <div className="bg-ide-surface/80 backdrop-blur-sm border border-ide-border/50 rounded-lg px-3 py-1 text-xs text-ide-text-muted">
        <div className="flex items-center space-x-1">
          <span>📄</span>
          <span className="truncate max-w-[150px]">{currentFile.name}</span>
        </div>
      </div>
    </div>
  );
};

export default FloatingCollabButton;
