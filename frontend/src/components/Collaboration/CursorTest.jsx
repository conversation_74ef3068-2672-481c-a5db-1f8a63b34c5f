import React, { useEffect } from 'react';

const CursorTest = () => {
  useEffect(() => {
    // Test cursor styles by adding them directly to the page
    const testCursors = [
      { userId: 'test1', color: '#FF6B6B', nickname: 'Test User 1' },
      { userId: 'test2', color: '#4ECDC4', nickname: 'Test User 2' }
    ];

    const styleId = 'cursor-test-styles';
    let styleElement = document.getElementById(styleId);

    if (!styleElement) {
      styleElement = document.createElement('style');
      styleElement.id = styleId;
      document.head.appendChild(styleElement);
    }

    let css = `
      .cursor-test-container {
        position: relative;
        background: var(--ide-bg);
        border: 1px solid var(--ide-border);
        border-radius: 8px;
        padding: 20px;
        margin: 20px;
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        line-height: 1.5;
      }

      .cursor-test-line {
        position: relative;
        height: 1.5em;
        margin: 2px 0;
      }

      .test-cursor {
        position: absolute;
        top: 0;
        width: 2px;
        height: 100%;
        border-radius: 1px;
        animation: blink 1.5s infinite;
      }

      .test-cursor-label {
        position: absolute;
        top: -28px;
        left: -2px;
        padding: 4px 8px;
        border-radius: 4px;
        color: white;
        font-size: 11px;
        font-weight: 600;
        white-space: nowrap;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      }

      .test-cursor-label::after {
        content: '';
        position: absolute;
        top: 100%;
        left: 8px;
        width: 0;
        height: 0;
        border-left: 4px solid transparent;
        border-right: 4px solid transparent;
        border-top: 4px solid inherit;
      }

      @keyframes blink {
        0%, 50% { opacity: 1; }
        51%, 100% { opacity: 0.4; }
      }
    `;

    testCursors.forEach(cursor => {
      css += `
        .test-cursor-${cursor.userId} {
          background-color: ${cursor.color};
        }

        .test-cursor-label-${cursor.userId} {
          background-color: ${cursor.color};
          border-top-color: ${cursor.color};
        }
      `;
    });

    styleElement.textContent = css;

    return () => {
      if (styleElement && styleElement.parentNode) {
        styleElement.parentNode.removeChild(styleElement);
      }
    };
  }, []);

  return (
    <div className="p-4">
      <h3 className="text-lg font-semibold text-ide-text mb-4">Cursor Rendering Test</h3>
      
      <div className="cursor-test-container">
        <div className="cursor-test-line">
          <span className="text-ide-text">function hello() &#123;</span>
          <div className="test-cursor test-cursor-test1" style={{ left: '120px' }}>
            <div className="test-cursor-label test-cursor-label-test1">
              User 1
            </div>
          </div>
        </div>

        <div className="cursor-test-line">
          <span className="text-ide-text">  console.log(&quot;Hello World!&quot;);</span>
          <div className="test-cursor test-cursor-test2" style={{ left: '200px' }}>
            <div className="test-cursor-label test-cursor-label-test2">
              User 2
            </div>
          </div>
        </div>
        
        <div className="cursor-test-line">
          <span className="text-ide-text">&#125;</span>
        </div>
      </div>

      <div className="mt-4 p-3 bg-ide-surface border border-ide-border rounded-lg">
        <h4 className="text-sm font-medium text-ide-text mb-2">Test Results</h4>
        <div className="text-xs text-ide-text-muted space-y-1">
          <div>✅ Cursor lines should be visible and blinking</div>
          <div>✅ User labels should appear above cursors</div>
          <div>✅ Colors should match user assignments</div>
          <div>✅ Labels should have proper styling and arrows</div>
        </div>
      </div>
    </div>
  );
};

export default CursorTest;
