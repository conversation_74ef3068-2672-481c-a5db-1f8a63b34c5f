/* Remote Cursor Styles - Google Docs Style */

.remote-cursor-widget {
  position: absolute !important;
  pointer-events: none !important;
  z-index: 1000 !important;
}

.remote-cursor-label {
  position: relative !important;
  top: -28px !important;
  left: -2px !important;
  padding: 4px 8px !important;
  border-radius: 4px !important;
  color: white !important;
  font-size: 11px !important;
  font-weight: 600 !important;
  white-space: nowrap !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2) !important;
  opacity: 1 !important;
  transform: translateY(0) !important;
  transition: all 0.2s ease-in-out !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif !important;
}

.remote-cursor-label::after {
  content: '' !important;
  position: absolute !important;
  top: 100% !important;
  left: 8px !important;
  width: 0 !important;
  height: 0 !important;
  border-left: 4px solid transparent !important;
  border-right: 4px solid transparent !important;
  border-top: 4px solid inherit !important;
}

/* Base cursor line styles */
.remote-cursor-line::after {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  width: 2px !important;
  height: 1.2em !important;
  background-color: currentColor !important;
  border-radius: 1px !important;
  z-index: 100 !important;
  animation: blinkCursor 1.5s infinite !important;
}

@keyframes blinkCursor {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0.4; }
}

/* Selection styles */
.remote-selection {
  border-radius: 2px !important;
}

/* Color-specific styles will be generated dynamically */
