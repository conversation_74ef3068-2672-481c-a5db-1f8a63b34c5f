import React, { useEffect, useRef } from 'react';
import './RemoteCursors.css';

const RemoteCursors = ({ editor, monaco, cursors = [] }) => {
  const decorationsRef = useRef([]);
  const widgetsRef = useRef([]);

  useEffect(() => {
    if (!editor || !monaco || !Array.isArray(cursors)) {
      return;
    }

    // Clear existing decorations and widgets
    if (decorationsRef.current.length > 0) {
      decorationsRef.current = editor.deltaDecorations(decorationsRef.current, []);
    }

    widgetsRef.current.forEach(widget => {
      if (widget && typeof widget.dispose === 'function') {
        widget.dispose();
      }
    });
    widgetsRef.current = [];

    // Create new decorations and widgets for each cursor
    const newDecorations = [];
    const newWidgets = [];

    cursors.forEach(cursorData => {
      try {
        const { userId, cursor, selection, color, nickname } = cursorData;

        if (!cursor || typeof cursor.line !== 'number' || typeof cursor.column !== 'number' || !userId) {
          return;
        }

        // Convert to Monaco position (1-based line, 1-based column)
        const position = {
          lineNumber: Math.max(1, cursor.line + 1),
          column: Math.max(1, cursor.column + 1)
        };

        // Create cursor decoration
        const cursorDecoration = {
          range: new monaco.Range(
            position.lineNumber,
            position.column,
            position.lineNumber,
            position.column
          ),
          options: {
            className: `remote-cursor-${userId}`,
            stickiness: monaco.editor.TrackedRangeStickiness.NeverGrowsWhenTypingAtEdges,
            afterContentClassName: `remote-cursor-line-${userId}`,
          }
        };

        newDecorations.push(cursorDecoration);

        // Create selection decoration if exists
        if (selection && selection.startLine !== undefined && selection.endLine !== undefined) {
          const selectionDecoration = {
            range: new monaco.Range(
              Math.max(1, selection.startLine + 1),
              Math.max(1, selection.startColumn + 1),
              Math.max(1, selection.endLine + 1),
              Math.max(1, selection.endColumn + 1)
            ),
            options: {
              className: `remote-selection-${userId}`,
              stickiness: monaco.editor.TrackedRangeStickiness.NeverGrowsWhenTypingAtEdges,
            }
          };

          newDecorations.push(selectionDecoration);
        }

        // Create cursor label widget
        const cursorWidget = {
          domNode: null,
          getId: () => `remote-cursor-widget-${userId}`,
          getDomNode: function() {
            if (!this.domNode) {
              this.domNode = document.createElement('div');
              this.domNode.className = 'remote-cursor-widget';
              this.domNode.innerHTML = `
                <div class="remote-cursor-label remote-cursor-label-${userId}">
                  ${nickname || 'User'}
                </div>
              `;
              this.domNode.style.pointerEvents = 'none';
              this.domNode.style.zIndex = '1000';
            }
            return this.domNode;
          },
          getPosition: () => ({
            position: position,
            preference: [monaco.editor.ContentWidgetPositionPreference.ABOVE]
          }),
          dispose: function() {
            if (this.domNode && this.domNode.parentNode) {
              this.domNode.parentNode.removeChild(this.domNode);
            }
          }
        };

        editor.addContentWidget(cursorWidget);
        newWidgets.push(cursorWidget);
      } catch (error) {
        console.error('Failed to render remote cursor:', error);
      }
    });

    // Apply decorations
    decorationsRef.current = editor.deltaDecorations([], newDecorations);

    // Add CSS styles for cursors if not already added
    addCursorStyles(cursors);

    // Cleanup function
    return () => {
      if (decorationsRef.current.length > 0) {
        decorationsRef.current = editor.deltaDecorations(decorationsRef.current, []);
      }
      widgetsRef.current.forEach(widget => {
        if (widget && typeof widget.dispose === 'function') {
          widget.dispose();
        }
      });
      widgetsRef.current = [];
    };
  }, [editor, monaco, cursors]);

  return null; // This component doesn't render anything directly
};

// Helper function to add dynamic CSS styles for remote cursors
const addCursorStyles = (cursors) => {
  const styleId = 'remote-cursor-dynamic-styles';
  let styleElement = document.getElementById(styleId);

  if (!styleElement) {
    styleElement = document.createElement('style');
    styleElement.id = styleId;
    document.head.appendChild(styleElement);
  }

  // Generate dynamic CSS for each user's color
  let css = '';
  cursors.forEach(cursorData => {
    const { userId, color } = cursorData;
    css += `
      .remote-cursor-line-${userId}::after {
        content: '' !important;
        position: absolute !important;
        top: 0 !important;
        left: 0 !important;
        width: 2px !important;
        height: 1.2em !important;
        background-color: ${color} !important;
        border-radius: 1px !important;
        animation: blinkCursor-${userId} 1.5s infinite !important;
        z-index: 100 !important;
      }

      @keyframes blinkCursor-${userId} {
        0%, 50% { opacity: 1; }
        51%, 100% { opacity: 0.4; }
      }

      .remote-cursor-${userId} {
        color: ${color} !important;
      }

      .remote-selection-${userId} {
        background-color: ${color}20 !important;
        border: 1px solid ${color}40 !important;
        border-radius: 2px !important;
      }

      .remote-cursor-label-${userId} {
        background-color: ${color} !important;
        border-top-color: ${color} !important;
      }
    `;
  });

  styleElement.textContent = css;
};

export default RemoteCursors;
