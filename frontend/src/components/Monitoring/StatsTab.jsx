import React, { useState, useEffect, useRef } from 'react'
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from 'chart.js'
import { Line, Doughnut } from 'react-chartjs-2'
import { 
  Activity, 
  MemoryStick, 
  Network, 
  Clock, 
  Container,
  Cpu,
  HardDrive,
  Wifi,
  AlertCircle,
  CheckCircle,
  RefreshCw
} from 'lucide-react'
import { useAppStore } from '../../stores/appStore'
import { monitoringService } from '../../services/monitoring'
import { StatusIndicator } from '../UI'

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
)

const StatsTab = () => {
  const { currentProject, theme } = useAppStore()
  const [stats, setStats] = useState(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [cpuHistory, setCpuHistory] = useState([])
  const [memoryHistory, setMemoryHistory] = useState([])
  const [networkHistory, setNetworkHistory] = useState([])
  const [lastUpdate, setLastUpdate] = useState(null)
  
  const maxHistoryPoints = 30 // Keep last 30 data points (1 minute at 2s intervals)

  useEffect(() => {
    if (currentProject?.id) {
      setLoading(true)
      setError(null)
      
      // Start monitoring
      const cleanup = monitoringService.startMonitoring(
        currentProject.id,
        (newStats, error) => {
          if (error) {
            setError(error.message)
            setLoading(false)
            return
          }
          
          if (newStats) {
            setStats(newStats)
            setError(null)
            setLoading(false)
            setLastUpdate(new Date())
            
            // Update history
            const timestamp = new Date().toLocaleTimeString()
            
            setCpuHistory(prev => {
              const newHistory = [...prev, { time: timestamp, value: newStats.cpu.usage }]
              return newHistory.slice(-maxHistoryPoints)
            })
            
            setMemoryHistory(prev => {
              const newHistory = [...prev, { time: timestamp, value: newStats.memory.percentage }]
              return newHistory.slice(-maxHistoryPoints)
            })
            
            setNetworkHistory(prev => {
              const newHistory = [...prev, { 
                time: timestamp, 
                rx: newStats.network.rxBytes / 1024 / 1024, // Convert to MB
                tx: newStats.network.txBytes / 1024 / 1024 
              }]
              return newHistory.slice(-maxHistoryPoints)
            })
          }
        },
        2000 // Update every 2 seconds
      )

      return () => {
        monitoringService.stopMonitoring(currentProject.id)
      }
    } else {
      setStats(null)
      setLoading(false)
      setCpuHistory([])
      setMemoryHistory([])
      setNetworkHistory([])
    }
  }, [currentProject?.id])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (currentProject?.id) {
        monitoringService.stopMonitoring(currentProject.id)
      }
    }
  }, [])

  const getThemeColors = () => {
    const isDark = theme !== 'catppuccin-latte'
    return {
      primary: 'var(--ide-accent)',
      success: 'var(--ide-success)',
      warning: 'var(--ide-warning)',
      error: 'var(--ide-error)',
      text: 'var(--ide-text)',
      textMuted: 'var(--ide-text-muted)',
      surface: 'var(--ide-surface)',
      border: 'var(--ide-border)',
      grid: isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)',
    }
  }

  const createChartOptions = (title) => {
    const colors = getThemeColors()
    return {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: false,
        },
        title: {
          display: false,
        },
        tooltip: {
          backgroundColor: colors.surface,
          titleColor: colors.text,
          bodyColor: colors.text,
          borderColor: colors.border,
          borderWidth: 1,
          titleFont: {
            size: 12,
          },
          bodyFont: {
            size: 11,
          },
        },
      },
      scales: {
        x: {
          display: true,
          grid: {
            color: colors.grid,
            lineWidth: 0.5,
          },
          ticks: {
            color: colors.textMuted,
            maxTicksLimit: window.innerWidth < 640 ? 4 : 6,
            font: {
              size: window.innerWidth < 640 ? 9 : 10,
            },
          },
        },
        y: {
          display: true,
          grid: {
            color: colors.grid,
            lineWidth: 0.5,
          },
          ticks: {
            color: colors.textMuted,
            maxTicksLimit: 5,
            font: {
              size: window.innerWidth < 640 ? 9 : 10,
            },
          },
          beginAtZero: true,
        },
      },
      animation: {
        duration: 750,
        easing: 'easeInOutQuart',
      },
      elements: {
        point: {
          radius: 0,
          hoverRadius: window.innerWidth < 640 ? 3 : 4,
        },
        line: {
          borderWidth: window.innerWidth < 640 ? 1.5 : 2,
        },
      },
    }
  }

  const cpuChartData = {
    labels: cpuHistory.map(point => point.time),
    datasets: [
      {
        label: 'CPU Usage (%)',
        data: cpuHistory.map(point => point.value.toFixed(1)),
        borderColor: getThemeColors().primary,
        backgroundColor: getThemeColors().primary + '20',
        borderWidth: 2,
        fill: true,
        tension: 0.4,
        pointRadius: 0,
        pointHoverRadius: 4,
      },
    ],
  }

  const memoryChartData = {
    labels: memoryHistory.map(point => point.time),
    datasets: [
      {
        label: 'Memory Usage (%)',
        data: memoryHistory.map(point => point.value.toFixed(1)),
        borderColor: getThemeColors().success,
        backgroundColor: getThemeColors().success + '20',
        borderWidth: 2,
        fill: true,
        tension: 0.4,
        pointRadius: 0,
        pointHoverRadius: 4,
      },
    ],
  }

  const networkChartData = {
    labels: networkHistory.map(point => point.time),
    datasets: [
      {
        label: 'Download (MB)',
        data: networkHistory.map(point => point.rx.toFixed(2)),
        borderColor: getThemeColors().primary,
        backgroundColor: getThemeColors().primary + '20',
        borderWidth: 2,
        fill: false,
        tension: 0.4,
        pointRadius: 0,
        pointHoverRadius: 4,
      },
      {
        label: 'Upload (MB)',
        data: networkHistory.map(point => point.tx.toFixed(2)),
        borderColor: getThemeColors().warning,
        backgroundColor: getThemeColors().warning + '20',
        borderWidth: 2,
        fill: false,
        tension: 0.4,
        pointRadius: 0,
        pointHoverRadius: 4,
      },
    ],
  }

  if (!currentProject) {
    return (
      <div className="p-4 sm:p-6 text-center">
        <Container className="mx-auto text-ide-text-muted mb-4" size={32} />
        <h3 className="text-base sm:text-lg font-medium text-ide-text mb-2">No Project Selected</h3>
        <p className="text-sm text-ide-text-muted">
          Open a project to view container statistics
        </p>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="p-4 sm:p-6">
        <div className="flex items-center justify-center space-x-3 mb-6">
          <RefreshCw className="animate-spin text-ide-accent" size={16} />
          <span className="text-sm text-ide-text">Loading stats...</span>
        </div>
        <div className="space-y-3 sm:space-y-4">
          {[1, 2, 3].map(i => (
            <div key={i} className="ide-skeleton h-16 sm:h-20 rounded-lg" />
          ))}
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="p-4 sm:p-6 text-center">
        <AlertCircle className="mx-auto text-ide-error mb-4" size={32} />
        <h3 className="text-base sm:text-lg font-medium text-ide-text mb-2">Error Loading Stats</h3>
        <p className="text-sm text-ide-text-muted mb-4">{error}</p>
        <button
          onClick={() => window.location.reload()}
          className="ide-button-primary text-sm px-4 py-2"
        >
          Retry
        </button>
      </div>
    )
  }

  return (
    <div className="h-full overflow-auto ide-scrollbar">
      <div className="p-2 sm:p-4 space-y-4 sm:space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-2 sm:space-y-0">
          <div className="min-w-0">
            <h3 className="text-base sm:text-lg font-semibold text-ide-text truncate">Container Stats</h3>
            <p className="text-xs sm:text-sm text-ide-text-muted truncate">{currentProject.name}</p>
          </div>
          <div className="flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 sm:space-x-3">
            {lastUpdate && (
              <div className="text-xs text-ide-text-muted">
                {lastUpdate.toLocaleTimeString()}
              </div>
            )}
            <button
              onClick={() => {
                if (currentProject?.id) {
                  monitoringService.stopMonitoring(currentProject.id)
                  setLoading(true)
                  setError(null)
                  // Restart monitoring
                  setTimeout(() => {
                    monitoringService.startMonitoring(
                      currentProject.id,
                      (newStats, error) => {
                        if (error) {
                          setError(error.message)
                          setLoading(false)
                          return
                        }
                        if (newStats) {
                          setStats(newStats)
                          setError(null)
                          setLoading(false)
                          setLastUpdate(new Date())
                        }
                      },
                      2000
                    )
                  }, 100)
                }
              }}
              className="p-1 hover:bg-ide-surface-hover rounded transition-colors self-start sm:self-auto"
              title="Refresh stats"
            >
              <RefreshCw size={14} className="text-ide-text-muted" />
            </button>
          </div>
        </div>

        {stats && (
          <>
            {/* Status Overview */}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
              <div className="ide-panel p-3 sm:p-4">
                <div className="flex items-center space-x-2 sm:space-x-3">
                  <div className="p-1.5 sm:p-2 bg-ide-accent/10 rounded-lg flex-shrink-0">
                    <Container className="text-ide-accent" size={16} />
                  </div>
                  <div className="min-w-0 flex-1">
                    <div className="text-xs sm:text-sm font-medium text-ide-text">Status</div>
                    <div className="flex items-center space-x-2">
                      <StatusIndicator status={stats.container.status} size="xs" />
                      <span className="text-xs sm:text-sm text-ide-text-muted capitalize truncate">
                        {stats.container.status}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              <div className="ide-panel p-3 sm:p-4">
                <div className="flex items-center space-x-2 sm:space-x-3">
                  <div className="p-1.5 sm:p-2 bg-ide-success/10 rounded-lg flex-shrink-0">
                    <Clock className="text-ide-success" size={16} />
                  </div>
                  <div className="min-w-0 flex-1">
                    <div className="text-xs sm:text-sm font-medium text-ide-text">Uptime</div>
                    <div className="text-xs sm:text-sm text-ide-text-muted truncate">
                      {monitoringService.formatUptime(stats.timestamp)}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Quick Stats Grid */}
            <div className="grid grid-cols-2 lg:grid-cols-2 xl:grid-cols-4 gap-2 sm:gap-3">
              <div className="ide-panel p-2 sm:p-3">
                <div className="flex items-center space-x-1 sm:space-x-2 mb-1">
                  <Cpu className="text-ide-accent flex-shrink-0" size={12} />
                  <span className="text-xs font-medium text-ide-text truncate">CPU</span>
                </div>
                <div className="text-sm sm:text-lg font-mono text-ide-accent">
                  {monitoringService.formatPercentage(stats.cpu.usage)}
                </div>
                <div className="text-xs text-ide-text-muted truncate">
                  {stats.cpu.cores} cores
                </div>
              </div>

              <div className="ide-panel p-2 sm:p-3">
                <div className="flex items-center space-x-1 sm:space-x-2 mb-1">
                  <MemoryStick className="text-ide-success flex-shrink-0" size={12} />
                  <span className="text-xs font-medium text-ide-text truncate">Memory</span>
                </div>
                <div className="text-sm sm:text-lg font-mono text-ide-success">
                  {monitoringService.formatPercentage(stats.memory.percentage)}
                </div>
                <div className="text-xs text-ide-text-muted truncate">
                  {monitoringService.formatBytes(stats.memory.usage)}
                </div>
              </div>

              <div className="ide-panel p-2 sm:p-3">
                <div className="flex items-center space-x-1 sm:space-x-2 mb-1">
                  <Network className="text-ide-warning flex-shrink-0" size={12} />
                  <span className="text-xs font-medium text-ide-text truncate">Net RX</span>
                </div>
                <div className="text-sm sm:text-lg font-mono text-ide-warning">
                  {monitoringService.formatBytes(stats.network.rxBytes)}
                </div>
                <div className="text-xs text-ide-text-muted truncate">
                  {stats.network.rxPackets} pkts
                </div>
              </div>

              <div className="ide-panel p-2 sm:p-3">
                <div className="flex items-center space-x-1 sm:space-x-2 mb-1">
                  <Wifi className="text-ide-primary flex-shrink-0" size={12} />
                  <span className="text-xs font-medium text-ide-text truncate">Net TX</span>
                </div>
                <div className="text-sm sm:text-lg font-mono text-ide-primary">
                  {monitoringService.formatBytes(stats.network.txBytes)}
                </div>
                <div className="text-xs text-ide-text-muted truncate">
                  {stats.network.txPackets} pkts
                </div>
              </div>
            </div>

            {/* Resource Usage Charts */}
            <div className="space-y-3 sm:space-y-4">
              {/* CPU Usage Chart */}
              <div className="ide-panel p-3 sm:p-4">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-3 space-y-1 sm:space-y-0">
                  <div className="flex items-center space-x-2">
                    <Activity className="text-ide-accent flex-shrink-0" size={14} />
                    <span className="text-xs sm:text-sm font-medium text-ide-text">CPU Usage</span>
                  </div>
                  <span className="text-xs text-ide-text-muted">
                    Last {maxHistoryPoints}
                  </span>
                </div>
                <div className="h-24 sm:h-32">
                  <Line data={cpuChartData} options={createChartOptions('CPU Usage')} />
                </div>
              </div>

              {/* Memory Usage Chart */}
              <div className="ide-panel p-3 sm:p-4">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-3 space-y-1 sm:space-y-0">
                  <div className="flex items-center space-x-2">
                    <HardDrive className="text-ide-success flex-shrink-0" size={14} />
                    <span className="text-xs sm:text-sm font-medium text-ide-text">Memory Usage</span>
                  </div>
                  <span className="text-xs text-ide-text-muted truncate">
                    {monitoringService.formatBytes(stats.memory.limit)}
                  </span>
                </div>
                <div className="h-24 sm:h-32">
                  <Line data={memoryChartData} options={createChartOptions('Memory Usage')} />
                </div>
              </div>

              {/* Network I/O Chart */}
              <div className="ide-panel p-3 sm:p-4">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-3 space-y-1 sm:space-y-0">
                  <div className="flex items-center space-x-2">
                    <Network className="text-ide-warning flex-shrink-0" size={14} />
                    <span className="text-xs sm:text-sm font-medium text-ide-text">Network I/O</span>
                  </div>
                  <div className="flex items-center space-x-2 sm:space-x-4 text-xs text-ide-text-muted">
                    <div className="flex items-center space-x-1">
                      <div className="w-2 h-2 bg-ide-accent rounded-full flex-shrink-0"></div>
                      <span>RX</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <div className="w-2 h-2 bg-ide-warning rounded-full flex-shrink-0"></div>
                      <span>TX</span>
                    </div>
                  </div>
                </div>
                <div className="h-24 sm:h-32">
                  <Line data={networkChartData} options={createChartOptions('Network I/O')} />
                </div>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  )
}

export default StatsTab
