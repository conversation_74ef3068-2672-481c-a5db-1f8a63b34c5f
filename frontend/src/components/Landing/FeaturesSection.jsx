import React, { useEffect, useRef } from 'react'
import { 
  Code2, 
  Terminal, 
  GitBranch, 
  Zap, 
  Shield, 
  Users, 
  Cloud, 
  Palette,
  Database,
  Cpu,
  Globe,
  Lock
} from 'lucide-react'
import { createScrollAnimations, createCardHoverEffect } from '../../utils/gsapAnimations'

const FeaturesSection = () => {
  const sectionRef = useRef(null)
  const cardsRef = useRef([])

  useEffect(() => {
    // Animate cards on scroll
    if (cardsRef.current.length > 0) {
      createScrollAnimations(cardsRef.current)
    }

    // Add hover effects to cards
    cardsRef.current.forEach(card => {
      if (card) {
        createCardHoverEffect(card)
      }
    })
  }, [])

  const features = [
    {
      icon: Code2,
      title: "Smart Code Editor",
      description: "Advanced syntax highlighting, intelligent autocomplete, and real-time error detection for 50+ programming languages.",
      gradient: "from-blue-500/20 to-purple-500/20"
    },
    {
      icon: Terminal,
      title: "Integrated Terminal",
      description: "Full-featured terminal with multiple tabs, custom themes, and seamless integration with your development workflow.",
      gradient: "from-green-500/20 to-blue-500/20"
    },
    {
      icon: GitBranch,
      title: "Git Integration",
      description: "Built-in version control with visual diff, branch management, and collaborative features for team development.",
      gradient: "from-purple-500/20 to-pink-500/20"
    },
    {
      icon: Zap,
      title: "Lightning Fast",
      description: "Optimized performance with instant startup, real-time collaboration, and efficient resource management.",
      gradient: "from-yellow-500/20 to-orange-500/20"
    },
    {
      icon: Cloud,
      title: "Cloud Native",
      description: "Access your projects from anywhere with automatic sync, backup, and seamless device switching.",
      gradient: "from-cyan-500/20 to-blue-500/20"
    },
    {
      icon: Users,
      title: "Team Collaboration",
      description: "Real-time collaborative editing, shared workspaces, and integrated communication tools for teams.",
      gradient: "from-pink-500/20 to-red-500/20"
    },
    {
      icon: Shield,
      title: "Enterprise Security",
      description: "Bank-level security with encrypted connections, secure authentication, and compliance standards.",
      gradient: "from-indigo-500/20 to-purple-500/20"
    },
    {
      icon: Palette,
      title: "Customizable Themes",
      description: "Choose from popular IDE themes or create your own with extensive customization options.",
      gradient: "from-violet-500/20 to-purple-500/20"
    },
    {
      icon: Database,
      title: "Database Tools",
      description: "Integrated database management with query builders, schema visualization, and data exploration.",
      gradient: "from-emerald-500/20 to-teal-500/20"
    },
    {
      icon: Cpu,
      title: "Powerful Runtime",
      description: "High-performance containers with scalable resources and support for complex applications.",
      gradient: "from-orange-500/20 to-red-500/20"
    },
    {
      icon: Globe,
      title: "Global CDN",
      description: "Lightning-fast loading times with our global content delivery network and edge computing.",
      gradient: "from-blue-500/20 to-cyan-500/20"
    },
    {
      icon: Lock,
      title: "Private Workspaces",
      description: "Secure, isolated environments for sensitive projects with advanced access controls.",
      gradient: "from-gray-500/20 to-slate-500/20"
    }
  ]

  return (
    <section id="features" ref={sectionRef} className="relative py-24 px-6">
      <div className="max-w-7xl mx-auto">
        {/* Section Header */}
        <div className="text-center mb-20">
          <h2 className="text-4xl md:text-6xl font-bold mb-6">
            <span className="bg-gradient-to-r from-[var(--ide-text)] to-[var(--ide-accent)] bg-clip-text text-transparent">
              Powerful Features
            </span>
          </h2>
          <p className="text-xl text-[var(--ide-text-muted)] max-w-3xl mx-auto">
            Everything you need for modern development, from code editing to deployment, 
            all in one powerful cloud-based platform.
          </p>
        </div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <div
              key={index}
              ref={el => cardsRef.current[index] = el}
              className="group relative p-8 rounded-3xl bg-[var(--ide-surface)]/50 backdrop-blur-sm border border-[var(--ide-border)] hover:border-[var(--ide-accent)]/50 transition-all duration-300 overflow-hidden"
            >
              {/* Background Gradient */}
              <div className={`absolute inset-0 bg-gradient-to-br ${feature.gradient} opacity-0 group-hover:opacity-100 transition-opacity duration-300`} />
              
              {/* Content */}
              <div className="relative z-10">
                {/* Icon */}
                <div className="mb-6">
                  <div className="w-16 h-16 rounded-2xl bg-gradient-to-r from-[var(--ide-accent)]/20 to-[var(--ide-primary)]/20 flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                    <feature.icon className="w-8 h-8 text-[var(--ide-accent)]" />
                  </div>
                </div>

                {/* Title */}
                <h3 className="text-xl font-bold text-[var(--ide-text)] mb-4 group-hover:text-[var(--ide-accent)] transition-colors duration-300">
                  {feature.title}
                </h3>

                {/* Description */}
                <p className="text-[var(--ide-text-muted)] leading-relaxed">
                  {feature.description}
                </p>
              </div>

              {/* Hover Glow Effect */}
              <div className="absolute inset-0 rounded-3xl bg-gradient-to-r from-[var(--ide-accent)]/10 to-[var(--ide-primary)]/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-xl" />
            </div>
          ))}
        </div>

        {/* Bottom CTA */}
        <div className="text-center mt-20">
          <p className="text-lg text-[var(--ide-text-muted)] mb-8">
            Ready to experience the future of development?
          </p>
          <button className="px-8 py-4 bg-gradient-to-r from-[var(--ide-accent)] to-[var(--ide-primary)] rounded-2xl font-semibold text-white text-lg transition-all duration-300 hover:shadow-2xl hover:shadow-[var(--ide-accent)]/25 hover:scale-105">
            Start Building Today
          </button>
        </div>
      </div>
    </section>
  )
}

export default FeaturesSection
