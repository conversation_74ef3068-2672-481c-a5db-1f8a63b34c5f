import React, { useEffect, useRef } from 'react'
import { useNavigate } from 'react-router-dom'
import { Play, Code, Zap, <PERSON>R<PERSON>, <PERSON><PERSON><PERSON>, Rocket } from 'lucide-react'
import { animateHeroEntry, createMagneticEffect, typewriterEffect } from '../../utils/gsapAnimations'

const HeroSection = () => {
  const navigate = useNavigate()
  const heroRef = useRef(null)
  const titleRef = useRef(null)
  const subtitleRef = useRef(null)
  const ctaRef = useRef(null)
  const floatingRef = useRef([])
  const magneticButtonRef = useRef(null)

  useEffect(() => {
    const elements = {
      title: titleRef.current,
      subtitle: subtitleRef.current,
      cta: ctaRef.current,
      floatingElements: floatingRef.current
    }

    // Start hero animations
    const heroTimeline = animateHeroEntry(elements)

    // Add magnetic effect to CTA button
    if (magneticButtonRef.current) {
      createMagneticEffect(magneticButtonRef.current, 0.4)
    }

    return () => {
      heroTimeline.kill()
    }
  }, [])

  const handleGetStarted = () => {
    navigate('/dashboard')
  }

  const handleWatchDemo = () => {
    // For now, just scroll to features or show a demo modal
    document.getElementById('features')?.scrollIntoView({ behavior: 'smooth' })
  }

  return (
    <section ref={heroRef} className="relative min-h-screen flex items-center justify-center px-6 py-20">
      <div className="max-w-6xl mx-auto text-center relative z-10">
        {/* Floating Icons */}
        <div className="absolute inset-0 pointer-events-none">
          <div 
            ref={el => floatingRef.current[0] = el}
            className="absolute top-20 left-10 p-3 rounded-2xl bg-gradient-to-r from-[var(--ide-accent)]/20 to-[var(--ide-primary)]/20 backdrop-blur-sm border border-white/10"
          >
            <Code className="w-6 h-6 text-[var(--ide-accent)]" />
          </div>
          
          <div 
            ref={el => floatingRef.current[1] = el}
            className="absolute top-32 right-16 p-3 rounded-2xl bg-gradient-to-r from-[var(--ide-success)]/20 to-[var(--ide-accent)]/20 backdrop-blur-sm border border-white/10"
          >
            <Zap className="w-6 h-6 text-[var(--ide-success)]" />
          </div>
          
          <div 
            ref={el => floatingRef.current[2] = el}
            className="absolute bottom-32 left-20 p-3 rounded-2xl bg-gradient-to-r from-[var(--ide-primary)]/20 to-[var(--ide-success)]/20 backdrop-blur-sm border border-white/10"
          >
            <Sparkles className="w-6 h-6 text-[var(--ide-primary)]" />
          </div>
          
          <div 
            ref={el => floatingRef.current[3] = el}
            className="absolute bottom-20 right-12 p-3 rounded-2xl bg-gradient-to-r from-[var(--ide-accent)]/20 to-[var(--ide-primary)]/20 backdrop-blur-sm border border-white/10"
          >
            <Rocket className="w-6 h-6 text-[var(--ide-accent)]" />
          </div>
        </div>

        {/* Main Content */}
        <div className="space-y-8">
          {/* Title */}
          <h1 
            ref={titleRef}
            className="text-6xl md:text-8xl font-bold leading-tight"
          >
            <span className="bg-gradient-to-r from-[var(--ide-text)] via-[var(--ide-accent)] to-[var(--ide-primary)] bg-clip-text text-transparent">
              Code
            </span>
            <br />
            <span className="bg-gradient-to-r from-[var(--ide-primary)] via-[var(--ide-success)] to-[var(--ide-accent)] bg-clip-text text-transparent">
              Anywhere
            </span>
          </h1>

          {/* Subtitle */}
          <p 
            ref={subtitleRef}
            className="text-xl md:text-2xl text-[var(--ide-text-muted)] max-w-3xl mx-auto leading-relaxed"
          >
            Experience the future of development with our cloud-based IDE. 
            Write, test, and deploy your code from anywhere with lightning-fast performance 
            and collaborative features.
          </p>

          {/* CTA Buttons */}
          <div ref={ctaRef} className="flex flex-col sm:flex-row gap-6 justify-center items-center pt-8">
            <button
              ref={magneticButtonRef}
              onClick={handleGetStarted}
              className="group relative px-8 py-4 bg-gradient-to-r from-[var(--ide-accent)] to-[var(--ide-primary)] rounded-2xl font-semibold text-white text-lg transition-all duration-300 hover:shadow-2xl hover:shadow-[var(--ide-accent)]/25 overflow-hidden"
            >
              <span className="relative z-10 flex items-center gap-3">
                Get Started Free
                <ArrowRight className="w-5 h-5 transition-transform group-hover:translate-x-1" />
              </span>
              <div className="absolute inset-0 bg-gradient-to-r from-[var(--ide-primary)] to-[var(--ide-accent)] opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
            </button>

            <button
              onClick={handleWatchDemo}
              className="group flex items-center gap-3 px-8 py-4 rounded-2xl border border-[var(--ide-border)] bg-[var(--ide-surface)]/50 backdrop-blur-sm text-[var(--ide-text)] font-semibold text-lg transition-all duration-300 hover:border-[var(--ide-accent)] hover:bg-[var(--ide-surface)]"
            >
              <Play className="w-5 h-5 transition-transform group-hover:scale-110" />
              Watch Demo
            </button>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 pt-16 max-w-4xl mx-auto">
            <div className="text-center">
              <div className="text-3xl md:text-4xl font-bold text-[var(--ide-accent)] mb-2">10K+</div>
              <div className="text-[var(--ide-text-muted)]">Active Developers</div>
            </div>
            <div className="text-center">
              <div className="text-3xl md:text-4xl font-bold text-[var(--ide-success)] mb-2">99.9%</div>
              <div className="text-[var(--ide-text-muted)]">Uptime</div>
            </div>
            <div className="text-center">
              <div className="text-3xl md:text-4xl font-bold text-[var(--ide-primary)] mb-2">50+</div>
              <div className="text-[var(--ide-text-muted)]">Languages Supported</div>
            </div>
          </div>
        </div>
      </div>

      {/* Scroll Indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <div className="w-6 h-10 border-2 border-[var(--ide-text-muted)] rounded-full flex justify-center">
          <div className="w-1 h-3 bg-[var(--ide-text-muted)] rounded-full mt-2 animate-pulse" />
        </div>
      </div>
    </section>
  )
}

export default HeroSection
