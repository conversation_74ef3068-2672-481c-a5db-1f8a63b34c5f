import React, { useEffect, useRef, useState } from 'react'
import { Users, Code, Zap, Globe, TrendingUp, Star } from 'lucide-react'
import { createScrollAnimations, animateCounter } from '../../utils/gsapAnimations'
import { gsap } from 'gsap'

const StatsSection = () => {
  const sectionRef = useRef(null)
  const statsRef = useRef([])
  const [hasAnimated, setHasAnimated] = useState(false)

  const stats = [
    {
      icon: Users,
      value: 25000,
      suffix: '+',
      label: 'Active Developers',
      description: 'Developers trust our platform',
      color: 'text-[var(--ide-accent)]'
    },
    {
      icon: Code,
      value: 1200000,
      suffix: '+',
      label: 'Lines of Code',
      description: 'Written on our platform',
      color: 'text-[var(--ide-success)]'
    },
    {
      icon: Globe,
      value: 150,
      suffix: '+',
      label: 'Countries',
      description: 'Worldwide coverage',
      color: 'text-[var(--ide-primary)]'
    },
    {
      icon: Zap,
      value: 99.9,
      suffix: '%',
      label: 'Uptime',
      description: 'Reliable performance',
      color: 'text-[var(--ide-warning)]'
    },
    {
      icon: TrendingUp,
      value: 500,
      suffix: '%',
      label: 'Faster Deployment',
      description: 'Compared to traditional IDEs',
      color: 'text-[var(--ide-accent)]'
    },
    {
      icon: Star,
      value: 4.9,
      suffix: '/5',
      label: 'User Rating',
      description: 'Based on 10k+ reviews',
      color: 'text-[var(--ide-success)]'
    }
  ]

  useEffect(() => {
    // Set up scroll trigger for counter animations
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting && !hasAnimated) {
            setHasAnimated(true)
            
            // Animate counters
            statsRef.current.forEach((statElement, index) => {
              if (statElement) {
                const counterElement = statElement.querySelector('.counter')
                if (counterElement) {
                  animateCounter(counterElement, stats[index].value, 2.5)
                }
              }
            })
          }
        })
      },
      { threshold: 0.3 }
    )

    if (sectionRef.current) {
      observer.observe(sectionRef.current)
    }

    // Animate cards on scroll
    if (statsRef.current.length > 0) {
      createScrollAnimations(statsRef.current)
    }

    return () => {
      if (sectionRef.current) {
        observer.unobserve(sectionRef.current)
      }
    }
  }, [hasAnimated])

  return (
    <section ref={sectionRef} className="relative py-24 px-6 bg-[var(--ide-surface)]/30">
      <div className="max-w-7xl mx-auto">
        {/* Section Header */}
        <div className="text-center mb-20">
          <h2 className="text-4xl md:text-6xl font-bold mb-6">
            <span className="bg-gradient-to-r from-[var(--ide-text)] to-[var(--ide-primary)] bg-clip-text text-transparent">
              Trusted by Developers
            </span>
          </h2>
          <p className="text-xl text-[var(--ide-text-muted)] max-w-3xl mx-auto">
            Join thousands of developers who have already transformed their workflow 
            with our cloud-based development platform.
          </p>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {stats.map((stat, index) => (
            <div
              key={index}
              ref={el => statsRef.current[index] = el}
              className="group relative p-8 rounded-3xl bg-[var(--ide-bg)]/50 backdrop-blur-sm border border-[var(--ide-border)] hover:border-[var(--ide-accent)]/50 transition-all duration-300 text-center overflow-hidden"
            >
              {/* Background Glow */}
              <div className="absolute inset-0 bg-gradient-to-br from-[var(--ide-accent)]/5 to-[var(--ide-primary)]/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
              
              {/* Content */}
              <div className="relative z-10">
                {/* Icon */}
                <div className="mb-6 flex justify-center">
                  <div className="w-16 h-16 rounded-2xl bg-gradient-to-r from-[var(--ide-accent)]/20 to-[var(--ide-primary)]/20 flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                    <stat.icon className={`w-8 h-8 ${stat.color}`} />
                  </div>
                </div>

                {/* Counter */}
                <div className="mb-4">
                  <div className={`text-4xl md:text-5xl font-bold ${stat.color} mb-2`}>
                    <span className="counter">0</span>
                    <span>{stat.suffix}</span>
                  </div>
                  <h3 className="text-xl font-semibold text-[var(--ide-text)] mb-2">
                    {stat.label}
                  </h3>
                  <p className="text-[var(--ide-text-muted)]">
                    {stat.description}
                  </p>
                </div>
              </div>

              {/* Animated Border */}
              <div className="absolute inset-0 rounded-3xl bg-gradient-to-r from-[var(--ide-accent)] via-[var(--ide-primary)] to-[var(--ide-success)] opacity-0 group-hover:opacity-20 transition-opacity duration-300 blur-sm" />
            </div>
          ))}
        </div>

        {/* Testimonial Section */}
        <div className="mt-24 text-center">
          <div className="max-w-4xl mx-auto p-8 rounded-3xl bg-gradient-to-r from-[var(--ide-surface)]/50 to-[var(--ide-bg)]/50 backdrop-blur-sm border border-[var(--ide-border)]">
            <div className="mb-6">
              <div className="flex justify-center mb-4">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="w-6 h-6 text-[var(--ide-warning)] fill-current" />
                ))}
              </div>
              <blockquote className="text-xl md:text-2xl text-[var(--ide-text)] font-medium leading-relaxed mb-6">
                "This cloud IDE has completely transformed our development workflow. 
                The performance is incredible, and the collaboration features are game-changing."
              </blockquote>
              <div className="flex items-center justify-center space-x-4">
                <div className="w-12 h-12 rounded-full bg-gradient-to-r from-[var(--ide-accent)] to-[var(--ide-primary)] flex items-center justify-center">
                  <span className="text-white font-bold">JS</span>
                </div>
                <div className="text-left">
                  <div className="font-semibold text-[var(--ide-text)]">Jane Smith</div>
                  <div className="text-[var(--ide-text-muted)]">Senior Developer at TechCorp</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default StatsSection
