import React, { useEffect, useRef } from 'react'
import { gsap } from 'gsap'

const TechStrip = () => {
  const stripRef = useRef(null)
  const containerRef = useRef(null)

  // Curated core technologies - focused on the most important ones
  const technologies = [
    'React', 'Node.js', 'Docker', 'MongoDB', 'Redis', 'Nginx',
    'Express.js', 'Socket.IO', 'Monaco Editor', 'XTerm.js',
    'GSAP', 'Tailwind CSS', 'TypeScript', 'Vite'
  ]

  useEffect(() => {
    if (!stripRef.current || !containerRef.current) return

    const strip = stripRef.current
    const container = containerRef.current
    let animationId = null

    // Wait for DOM to be ready and fonts to load
    const initAnimation = () => {
      // Create clone for seamless infinite scroll
      const clone = strip.cloneNode(true)
      clone.setAttribute('aria-hidden', 'true')
      container.appendChild(clone)

      // Get the actual width after DOM is ready
      const stripWidth = strip.getBoundingClientRect().width

      // Position strips side by side with no gap
      gsap.set(strip, { x: 0 })
      gsap.set(clone, { x: stripWidth })

      // Create seamless infinite animation
      const tl = gsap.timeline({ repeat: -1 })

      tl.to([strip, clone], {
        x: `-=${stripWidth}`,
        duration: 25,
        ease: 'none'
      })

      // Store references for cleanup
      container._timeline = tl
      container._clone = clone
    }

    // Use requestAnimationFrame for better timing
    animationId = requestAnimationFrame(() => {
      setTimeout(initAnimation, 100)
    })

    // Cleanup function
    return () => {
      if (animationId) {
        cancelAnimationFrame(animationId)
      }
      if (container._timeline) {
        container._timeline.kill()
      }
      if (container._clone && container._clone.parentNode) {
        container._clone.parentNode.removeChild(container._clone)
      }
    }
  }, [])

  return (
    <div className="relative w-full overflow-hidden py-12">
      {/* Minimal Section Header */}
      <div className="text-center mb-8">
        <div className="inline-flex items-center space-x-2 px-4 py-2 rounded-full bg-white/5 backdrop-blur-sm border border-white/10">
          <div className="w-2 h-2 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full animate-pulse" />
          <span className="text-sm font-medium text-gray-300">Tech Stack</span>
        </div>
      </div>

      {/* Modern Tech Strip Container */}
      <div
        ref={containerRef}
        className="relative flex items-center overflow-hidden"
        style={{ height: '70px' }}
      >
        {/* Main Strip */}
        <div
          ref={stripRef}
          className="flex items-center space-x-12 whitespace-nowrap"
          style={{ minWidth: 'max-content' }}
        >
          {technologies.map((tech, index) => (
            <div
              key={`tech-${index}`}
              className="group flex items-center space-x-3 px-6 py-3 rounded-2xl bg-white/5 backdrop-blur-sm border border-white/10 hover:border-blue-400/30 transition-all duration-500 hover:scale-105 hover:bg-white/10"
            >
              {/* Tech Icon/Dot */}
              <div className="w-2 h-2 rounded-full bg-gradient-to-r from-blue-400 to-purple-400 group-hover:scale-125 transition-transform duration-300" />

              {/* Tech Name */}
              <span className="text-sm font-medium text-gray-300 group-hover:text-white transition-colors duration-300">
                {tech}
              </span>
            </div>
          ))}
        </div>

        {/* Elegant Edge Indicators - Positioned relative to tech strip container */}
        {/*<div className="absolute left--2 top-1/2 -translate-y-1/2 w-1 h-8 bg-gradient-to-b from-transparent via-blue-400/60 to-transparent rounded-full pointer-events-none z-10" />*/}
        {/*<div className="absolute right-1 top-1/2 -translate-y-1/2 w-1 h-8 bg-gradient-to-b from-transparent via-purple-400/60 to-transparent rounded-full pointer-events-none z-10" />*/}
      </div>
    </div>
  )
}

export default TechStrip
