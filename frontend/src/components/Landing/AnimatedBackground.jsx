import React, { useEffect, useRef } from 'react'
import { createParticleAnimation } from '../../utils/gsapAnimations'

const AnimatedBackground = () => {
  const containerRef = useRef(null)
  const particlesRef = useRef([])

  useEffect(() => {
    if (containerRef.current) {
      // Create animated particles
      particlesRef.current = createParticleAnimation(containerRef.current, 60)
    }

    // Cleanup on unmount
    return () => {
      if (particlesRef.current) {
        particlesRef.current.forEach(particle => {
          if (particle.parentNode) {
            particle.parentNode.removeChild(particle)
          }
        })
      }
    }
  }, [])

  return (
    <div className="fixed inset-0 overflow-hidden pointer-events-none z-0">
      {/* Gradient Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-[var(--ide-bg)] via-[var(--ide-surface)] to-[var(--ide-bg)]" />
      
      {/* Animated Gradient Overlay */}
      <div className="absolute inset-0 bg-gradient-to-r from-[var(--ide-accent)]/5 via-transparent to-[var(--ide-primary)]/5 animate-gradient" />
      
      {/* Particle Container */}
      <div ref={containerRef} className="absolute inset-0" />
      
      {/* Static Stars */}
      <div className="absolute inset-0">
        {[...Array(30)].map((_, i) => (
          <div
            key={i}
            className="absolute w-0.5 h-0.5 bg-white/30 rounded-full animate-pulse"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 3}s`,
              animationDuration: `${2 + Math.random() * 2}s`
            }}
          />
        ))}
      </div>
      
      {/* Enhanced Floating Geometric Shapes */}
      <div className="absolute inset-0">
        {/* Large Gradient Orb - Top Right */}
        <div className="absolute top-1/4 right-1/4 w-96 h-96 rounded-full bg-gradient-to-r from-blue-500/15 via-purple-500/10 to-pink-500/15 blur-3xl animate-pulse opacity-60" />

        {/* Medium Gradient Orb - Bottom Left */}
        <div className="absolute bottom-1/3 left-1/5 w-64 h-64 rounded-full bg-gradient-to-r from-green-500/12 via-cyan-500/8 to-blue-500/12 blur-2xl animate-pulse opacity-50" style={{ animationDelay: '1s' }} />

        {/* Small Accent Orb - Center */}
        <div className="absolute top-1/2 left-1/2 w-40 h-40 rounded-full bg-gradient-to-r from-purple-500/10 via-pink-500/8 to-orange-500/10 blur-xl animate-pulse opacity-40" style={{ animationDelay: '2s' }} />

        {/* Additional Floating Elements */}
        <div className="absolute top-1/6 left-1/3 w-24 h-24 rounded-full bg-gradient-to-r from-yellow-500/8 to-orange-500/8 blur-lg animate-pulse opacity-30" style={{ animationDelay: '3s' }} />

        <div className="absolute bottom-1/6 right-1/3 w-32 h-32 rounded-full bg-gradient-to-r from-indigo-500/8 to-purple-500/8 blur-lg animate-pulse opacity-35" style={{ animationDelay: '4s' }} />

        {/* Morphing Background Shapes */}
        <div className="absolute top-0 left-0 w-full h-full overflow-hidden">
          <div className="absolute top-10 left-10 w-2 h-2 bg-blue-400/30 rounded-full animate-ping" style={{ animationDelay: '0s', animationDuration: '3s' }} />
          <div className="absolute top-20 right-20 w-1 h-1 bg-purple-400/40 rounded-full animate-ping" style={{ animationDelay: '1s', animationDuration: '4s' }} />
          <div className="absolute bottom-20 left-20 w-1.5 h-1.5 bg-green-400/35 rounded-full animate-ping" style={{ animationDelay: '2s', animationDuration: '5s' }} />
          <div className="absolute bottom-10 right-10 w-2 h-2 bg-pink-400/30 rounded-full animate-ping" style={{ animationDelay: '3s', animationDuration: '3.5s' }} />
        </div>
      </div>
      
      {/* Grid Pattern Overlay */}
      <div className="absolute inset-0 opacity-[0.02]">
        <div className="absolute inset-0" style={{
          backgroundImage: `
            linear-gradient(rgba(255,255,255,0.1) 1px, transparent 1px),
            linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px)
          `,
          backgroundSize: '50px 50px'
        }} />
      </div>
    </div>
  )
}

export default AnimatedBackground
