import React, { useEffect, useRef, useState } from 'react'
import { gsap } from 'gsap'
import { Minimize2, Maximize2, X, Keyboard } from 'lucide-react'
import { useAppStore } from '../../stores/appStore'

const ZenMode = ({ children }) => {
  const { settings, toggleZenMode } = useAppStore()
  const [showExitHint, setShowExitHint] = useState(false)
  const [isTransitioning, setIsTransitioning] = useState(false)
  const [isInitialized, setIsInitialized] = useState(false)
  const containerRef = useRef(null)
  const topbarRef = useRef(null)
  const exitHintRef = useRef(null)
  const overlayRef = useRef(null)
  const previousZenMode = useRef(settings.ui.zenMode)

  const isZenMode = settings.ui.zenMode

  // Cleanup function to reset all zen mode styles
  const cleanupZenModeStyles = () => {
    const sidebar = document.querySelector('.sidebar-container')
    const terminal = document.querySelector('.ide-terminal-panel')
    const topbar = document.querySelector('.ide-topbar')
    const editorContainer = document.querySelector('.split-editor-container')

    // Reset all transforms and styles
    if (sidebar) {
      gsap.set(sidebar, { clearProps: "all" })
    }
    if (terminal) {
      gsap.set(terminal, { clearProps: "all" })
    }
    if (topbar) {
      gsap.set(topbar, { clearProps: "all" })
    }
    if (editorContainer) {
      gsap.set(editorContainer, { clearProps: "all" })
    }
  }

  // Show exit hint when entering zen mode with improved timing
  useEffect(() => {
    if (isZenMode) {
      // Delay showing the hint until after the enter animation
      const showTimer = setTimeout(() => {
        setShowExitHint(true)
      }, 1000) // Show after 1 second delay

      const hideTimer = setTimeout(() => {
        setShowExitHint(false)
      }, 6000) // Show for 5 seconds total (1s delay + 5s visible)

      return () => {
        clearTimeout(showTimer)
        clearTimeout(hideTimer)
      }
    }
  }, [isZenMode])

  // Zen mode enter animation with improved timing and duration
  const enterZenMode = () => {
    if (isTransitioning) return
    setIsTransitioning(true)

    const tl = gsap.timeline({
      onComplete: () => {
        setIsTransitioning(false)
      }
    })

    // Create overlay for smooth transition with longer duration
    if (overlayRef.current) {
      tl.set(overlayRef.current, { display: 'block', opacity: 0 })
        .to(overlayRef.current, {
          opacity: 0.95,
          duration: 0.6,
          ease: "power2.inOut"
        })
    }

    // Hide sidebar and terminal with longer, more elegant slide animations
    const sidebar = document.querySelector('.sidebar-container')
    const terminal = document.querySelector('.ide-terminal-panel')
    const topbar = document.querySelector('.ide-topbar')

    if (sidebar) {
      tl.to(sidebar, {
        x: '-100%',
        opacity: 0,
        duration: 0.8,
        ease: "power3.inOut"
      }, 0.3)
    }

    if (terminal) {
      tl.to(terminal, {
        y: '100%',
        opacity: 0,
        duration: 0.8,
        ease: "power3.inOut"
      }, 0.35)
    }

    if (topbar) {
      tl.to(topbar, {
        y: '-100%',
        opacity: 0,
        duration: 0.8,
        ease: "power3.inOut"
      }, 0.3)
    }

    // Expand editor to full screen with more dramatic effect
    const editorContainer = document.querySelector('.split-editor-container')
    if (editorContainer) {
      tl.to(editorContainer, {
        scale: 1.05,
        duration: 0.5,
        ease: "power2.out"
      }, 0.6)
      .to(editorContainer, {
        scale: 1,
        duration: 0.6,
        ease: "power2.out"
      })
      .to(editorContainer, {
        filter: "brightness(1.1) contrast(1.05)",
        duration: 0.4,
        ease: "power2.out"
      }, 0.8)
      .to(editorContainer, {
        filter: "brightness(1) contrast(1)",
        duration: 0.4,
        ease: "power2.out"
      })
    }

    // Fade out overlay with longer duration
    if (overlayRef.current) {
      tl.to(overlayRef.current, {
        opacity: 0,
        duration: 0.5,
        ease: "power2.inOut",
        onComplete: () => {
          gsap.set(overlayRef.current, { display: 'none' })
        }
      }, 1.8)
    }
  }

  // Zen mode exit animation with improved timing and duration
  const exitZenMode = () => {
    if (isTransitioning) return
    setIsTransitioning(true)

    const tl = gsap.timeline({
      onComplete: () => {
        setIsTransitioning(false)
        // Clean up all transforms and ensure proper layout
        cleanupZenModeStyles()
      }
    })

    // Create overlay for smooth transition with longer duration
    if (overlayRef.current) {
      tl.set(overlayRef.current, { display: 'block', opacity: 0 })
        .to(overlayRef.current, {
          opacity: 0.9,
          duration: 0.4,
          ease: "power2.inOut"
        })
    }

    // Reset editor effects first
    const editorContainer = document.querySelector('.split-editor-container')
    if (editorContainer) {
      tl.to(editorContainer, {
        filter: "brightness(0.95) contrast(0.95)",
        duration: 0.3,
        ease: "power2.out"
      }, 0.2)
      .to(editorContainer, {
        filter: "brightness(1) contrast(1)",
        duration: 0.4,
        ease: "power2.out"
      })
    }

    // Show topbar, sidebar and terminal with smooth, coordinated animations
    const sidebar = document.querySelector('.sidebar-container')
    const terminal = document.querySelector('.ide-terminal-panel')
    const topbar = document.querySelector('.ide-topbar')

    // Reset any existing transforms and filters first
    if (topbar) {
      gsap.set(topbar, {
        clearProps: "filter,scale",
        y: '-100%',
        opacity: 0
      })
      tl.to(topbar, {
        y: '0%',
        opacity: 1,
        duration: 0.6,
        ease: "power2.out"
      }, 0.2)
    }

    if (sidebar) {
      gsap.set(sidebar, {
        clearProps: "all",
        x: '-100%',
        opacity: 0
      })
      tl.to(sidebar, {
        x: '0%',
        opacity: 1,
        duration: 0.6,
        ease: "power2.out"
      }, 0.25)
    }

    if (terminal) {
      gsap.set(terminal, {
        clearProps: "all",
        y: '100%',
        opacity: 0
      })
      tl.to(terminal, {
        y: '0%',
        opacity: 1,
        duration: 0.6,
        ease: "power2.out"
      }, 0.3)
    }

    // Fade out overlay with proper timing
    if (overlayRef.current) {
      tl.to(overlayRef.current, {
        opacity: 0,
        duration: 0.4,
        ease: "power2.inOut",
        onComplete: () => {
          gsap.set(overlayRef.current, { display: 'none' })
        }
      }, 0.8)
    }
  }

  // Initialize component and handle zen mode toggle with animations
  useEffect(() => {
    // Set initial state without animation on first load
    if (!isInitialized) {
      setIsInitialized(true)
      previousZenMode.current = isZenMode

      // Apply initial zen mode state without animation
      if (isZenMode) {
        // Immediately apply zen mode styles without animation
        const sidebar = document.querySelector('.sidebar-container')
        const terminal = document.querySelector('.ide-terminal-panel')
        const topbar = document.querySelector('.ide-topbar')

        if (sidebar) {
          gsap.set(sidebar, { x: '-100%', opacity: 0 })
        }
        if (terminal) {
          gsap.set(terminal, { y: '100%', opacity: 0 })
        }
        if (topbar) {
          gsap.set(topbar, { y: '-100%', opacity: 0 })
        }
      }
      return
    }

    // Only animate if zen mode actually changed
    if (previousZenMode.current !== isZenMode) {
      if (isZenMode) {
        enterZenMode()
      } else {
        exitZenMode()
      }
      previousZenMode.current = isZenMode
    }
  }, [isZenMode, isInitialized])

  // Global keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e) => {
      // Ctrl+K Z for zen mode toggle
      if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
        e.preventDefault()
        const handleZKey = (ze) => {
          if (ze.key === 'z' || ze.key === 'Z') {
            ze.preventDefault()
            toggleZenMode()
            document.removeEventListener('keydown', handleZKey)
          }
        }
        document.addEventListener('keydown', handleZKey)
        
        // Remove listener after 2 seconds if Z is not pressed
        setTimeout(() => {
          document.removeEventListener('keydown', handleZKey)
        }, 2000)
      }
      
      // Escape to exit zen mode
      if (e.key === 'Escape' && isZenMode) {
        e.preventDefault()
        toggleZenMode()
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [isZenMode, toggleZenMode])

  // Enhanced floating topbar with hover zone approach
  useEffect(() => {
    if (!isZenMode) return

    let showTimeout, hideTimeout
    let isTopbarVisible = false

    // Create a hover zone at the top of the screen
    const hoverZone = document.createElement('div')
    hoverZone.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      height: 50px;
      z-index: 999;
      pointer-events: auto;
      background: transparent;
    `
    document.body.appendChild(hoverZone)

    const showTopbar = () => {
      if (isTopbarVisible) return
      clearTimeout(hideTimeout)

      const topbar = document.querySelector('.ide-topbar')
      if (!topbar) return

      isTopbarVisible = true
      gsap.to(topbar, {
        y: '0%',
        opacity: 1,
        duration: 0.3,
        ease: "power2.out"
      })
    }

    const hideTopbar = () => {
      if (!isTopbarVisible) return
      clearTimeout(showTimeout)

      hideTimeout = setTimeout(() => {
        const topbar = document.querySelector('.ide-topbar')
        if (!topbar) return

        isTopbarVisible = false
        gsap.to(topbar, {
          y: '-100%',
          opacity: 0,
          duration: 0.3,
          ease: "power2.in"
        })
      }, 500) // Delay before hiding
    }

    // Add event listeners to hover zone
    hoverZone.addEventListener('mouseenter', showTopbar)
    hoverZone.addEventListener('mouseleave', hideTopbar)

    // Also add listeners to the topbar itself to keep it visible when hovering over it
    const topbar = document.querySelector('.ide-topbar')
    if (topbar) {
      topbar.addEventListener('mouseenter', () => {
        clearTimeout(hideTimeout)
      })
      topbar.addEventListener('mouseleave', hideTopbar)
    }

    return () => {
      document.body.removeChild(hoverZone)
      clearTimeout(showTimeout)
      clearTimeout(hideTimeout)
      if (topbar) {
        topbar.removeEventListener('mouseenter', () => {})
        topbar.removeEventListener('mouseleave', hideTopbar)
      }
    }
  }, [isZenMode])

  return (
    <div ref={containerRef} className="zen-mode-container">
      {children}
      
      {/* Transition Overlay */}
      <div 
        ref={overlayRef}
        className="zen-mode-overlay"
        style={{ display: 'none' }}
      />

      {/* Exit Hint */}
      {isZenMode && showExitHint && (
        <div 
          ref={exitHintRef}
          className="zen-mode-exit-hint"
        >
          <div className="zen-mode-hint-content">
            <div className="flex items-center space-x-3 mb-2">
              <Minimize2 size={20} className="text-ide-accent" />
              <span className="text-lg font-medium text-ide-text">Zen Mode Active</span>
            </div>
            <div className="space-y-1 text-sm text-ide-text-muted">
              <div className="flex items-center space-x-2">
                <X size={14} />
                <span>Press <kbd className="zen-kbd">Esc</kbd> to exit</span>
              </div>
              <div className="flex items-center space-x-2">
                <Keyboard size={14} />
                <span>Or <kbd className="zen-kbd">Ctrl</kbd> + <kbd className="zen-kbd">K</kbd> then <kbd className="zen-kbd">Z</kbd></span>
              </div>
              <div className="text-xs opacity-75 mt-2">
                Move mouse to top edge to show menu bar
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default ZenMode
