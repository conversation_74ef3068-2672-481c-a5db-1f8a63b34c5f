import React, { useState } from 'react'
import { Files, Settings, Terminal, Container, Activity, Users } from 'lucide-react'
import { useAppStore } from '../../stores/appStore'
import { FileTree } from '../FileExplorer'
import { StatusIndicator } from '../UI'
import { StatsTab } from '../Monitoring'
import CollaborationTab from '../Collaboration/CollaborationTab'
import SettingsPanel from './SettingsPanel'
import { clsx } from 'clsx'

const SidebarTab = ({ icon: Icon, label, isActive, onClick }) => {
  const [showTooltip, setShowTooltip] = useState(false)

  return (
    <button
      className={clsx(
        'modern-sidebar-tab group text-ide-text-muted hover:text-ide-text',
        isActive && 'active'
      )}
      onClick={onClick}
      onMouseEnter={() => setShowTooltip(true)}
      onMouseLeave={() => setShowTooltip(false)}
    >
      <Icon size={20} className="transition-transform duration-200 group-hover:scale-110 relative z-10" />

      {/* Tooltip */}
      {showTooltip && (
        <div className="sidebar-tooltip">
          {label}
        </div>
      )}
    </button>
  )
}

const Sidebar = ({ onFileSelect }) => {
  const { layout, updateLayout, systemStatus } = useAppStore()
  const [activeTab, setActiveTab] = React.useState('files')

  const tabs = [
    { id: 'files', icon: Files, label: 'Files' },
    { id: 'collaboration', icon: Users, label: 'Collaboration' },
    { id: 'stats', icon: Activity, label: 'Stats' },
    { id: 'terminal', icon: Terminal, label: 'Terminal' },
    { id: 'containers', icon: Container, label: 'Containers' },
    { id: 'settings', icon: Settings, label: 'Settings' },
  ]

  const handleTabClick = (tabId) => {
    if (activeTab === tabId && layout.showSidebarContent) {
      // If clicking the same active tab, hide content
      updateLayout({ showSidebarContent: false })
    } else {
      // If clicking a different tab or content is hidden, show content
      setActiveTab(tabId)
      updateLayout({ showSidebarContent: true })
    }
  }

  const renderTabContent = () => {
    switch (activeTab) {
      case 'files':
        return <FileTree onFileSelect={onFileSelect} />

      case 'collaboration':
        return <CollaborationTab />

      case 'stats':
        return <StatsTab />

      case 'terminal':
        return (
          <div className="sidebar-content-section">
            <h3>
              <Terminal size={16} />
              <span>Terminal Sessions</span>
            </h3>
            <p className="text-ide-text-muted text-sm">
              Terminal sessions will appear here when created.
            </p>
          </div>
        )

      case 'containers':
        return (
          <div className="sidebar-content-section">
            <h3>
              <Container size={16} />
              <span>Containers</span>
            </h3>
            <div className="space-y-1">
              <div className="content-item">
                <span className="text-sm text-ide-text-muted">Container Manager</span>
                <StatusIndicator status={systemStatus.containerManager} />
              </div>
              <div className="content-item">
                <span className="text-sm text-ide-text-muted">DNS Server</span>
                <StatusIndicator status={systemStatus.dnsServer} />
              </div>
              <div className="content-item">
                <span className="text-sm text-ide-text-muted">Redis</span>
                <StatusIndicator status={systemStatus.redis} />
              </div>
            </div>
          </div>
        )

      case 'settings':
        return <SettingsPanel />
      
      default:
        return null
    }
  }

  if (!layout.showSidebar) {
    return null
  }

  return (
    <div className="flex h-full modern-sidebar sidebar-container">
      {/* Modern Tab Navigation */}
      <div className="w-16 modern-sidebar-nav flex flex-col flex-shrink-0 py-2">
        {tabs.map((tab) => (
          <SidebarTab
            key={tab.id}
            icon={tab.icon}
            label={tab.label}
            isActive={activeTab === tab.id && layout.showSidebarContent}
            onClick={() => handleTabClick(tab.id)}
          />
        ))}
      </div>

      {/* Tab Content */}
      {layout.showSidebarContent && (
        <div className="flex-1 overflow-hidden min-w-0 ide-content-panel animate-slide-in">
          <div className="h-full animate-fade-in">
            {renderTabContent()}
          </div>
        </div>
      )}
    </div>
  )
}

export default Sidebar
