import React from 'react'
import { 
  GitBranch, 
  AlertCircle, 
  CheckCircle, 
  Info, 
  Zap,
  FileText,
  Clock
} from 'lucide-react'
import { useAppStore } from '../../stores/appStore'
import { StatusIndicator } from '../UI'

const StatusBar = () => {
  const { 
    currentFile, 
    currentProject, 
    systemStatus,
    theme,
    openFiles 
  } = useAppStore()

  const getFileLanguage = (fileName) => {
    if (!fileName) return 'Plain Text'
    const ext = fileName.split('.').pop()?.toLowerCase()
    
    const languageMap = {
      'js': 'JavaScript',
      'jsx': 'React JSX',
      'ts': 'TypeScript',
      'tsx': 'React TSX',
      'vue': 'Vue',
      'py': 'Python',
      'java': 'Java',
      'cpp': 'C++',
      'c': 'C',
      'css': 'CSS',
      'scss': 'SCSS',
      'html': 'HTML',
      'json': 'JSON',
      'md': 'Markdown',
      'xml': 'XML',
      'yml': 'YAML',
      'yaml': 'YAML',
    }
    
    return languageMap[ext] || 'Plain Text'
  }

  const getSystemHealthStatus = () => {
    const services = Object.values(systemStatus)
    const healthyCount = services.filter(s => s === 'healthy').length
    const totalCount = services.length
    
    if (healthyCount === totalCount) return 'healthy'
    if (healthyCount === 0) return 'error'
    return 'warning'
  }

  const formatTime = () => {
    return new Date().toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit' 
    })
  }

  return (
    <div className="h-6 bg-ide-surface border-t border-ide-border flex items-center justify-between px-4 text-xs text-ide-text-muted">
      {/* Left Section */}
      <div className="flex items-center space-x-4">
        {/* Project Info */}
        {currentProject && (
          <div className="flex items-center space-x-2">
            <GitBranch size={12} />
            <span>{currentProject.name}</span>
          </div>
        )}
        
        {/* File Info */}
        {currentFile && (
          <div className="flex items-center space-x-2">
            <FileText size={12} />
            <span>{currentFile.name}</span>
            <span className="text-ide-accent">•</span>
            <span>{getFileLanguage(currentFile.name)}</span>
          </div>
        )}
      </div>

      {/* Center Section */}
      <div className="flex items-center space-x-4">
        {/* Open Files Count */}
        <div className="flex items-center space-x-1">
          <span>{openFiles.length} file{openFiles.length !== 1 ? 's' : ''} open</span>
        </div>
      </div>

      {/* Right Section */}
      <div className="flex items-center space-x-4">
        {/* System Health */}
        <div className="flex items-center space-x-2">
          <StatusIndicator 
            status={getSystemHealthStatus()} 
            size="xs"
          />
          <span>System</span>
        </div>
        
        {/* Theme */}
        <div className="flex items-center space-x-2">
          <div className="w-2 h-2 rounded-full bg-ide-accent"></div>
          <span className="capitalize">{theme.replace('-', ' ')}</span>
        </div>
        
        {/* Time */}
        <div className="flex items-center space-x-2">
          <Clock size={12} />
          <span>{formatTime()}</span>
        </div>
      </div>
    </div>
  )
}

export default StatusBar
