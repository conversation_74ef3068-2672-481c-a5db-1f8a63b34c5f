import React from 'react'
import { Setting<PERSON>, Palette, Code, Terminal, Maximize2, Minimize2, <PERSON>, EyeOff } from 'lucide-react'
import { useAppStore } from '../../stores/appStore'
import { getThemesByType } from '../../config/themes'

const SettingsPanel = () => {
  const { 
    theme, 
    setTheme, 
    settings, 
    updateSetting,
    toggleZenMode,
    addNotification
  } = useAppStore()
  
  const themesByType = getThemesByType()

  const handleThemeChange = (newTheme) => {
    setTheme(newTheme)
    addNotification({
      type: 'success',
      message: `Theme changed to ${themesByType.dark.find(t => t.key === newTheme)?.name || themesByType.light.find(t => t.key === newTheme)?.name}`,
    })
  }

  const handleSettingChange = (category, key, value) => {
    updateSetting(category, key, value)
    addNotification({
      type: 'info',
      message: 'Setting updated',
    })
  }

  return (
    <div className="sidebar-content-section">
      <h3>
        <Settings size={16} />
        <span>Settings</span>
      </h3>
      
      <div className="space-y-6">
        {/* Theme Selection */}
        <div>
          <label className="block text-sm text-ide-text-muted mb-2">
            <Palette size={14} className="inline mr-1" />
            Theme
          </label>
          <select 
            className="ide-input w-full text-sm"
            value={theme}
            onChange={(e) => handleThemeChange(e.target.value)}
          >
            <optgroup label="Dark Themes">
              {themesByType.dark.map((themeOption) => (
                <option key={themeOption.key} value={themeOption.key}>
                  {themeOption.name}
                </option>
              ))}
            </optgroup>
            {themesByType.light.length > 0 && (
              <optgroup label="Light Themes">
                {themesByType.light.map((themeOption) => (
                  <option key={themeOption.key} value={themeOption.key}>
                    {themeOption.name}
                  </option>
                ))}
              </optgroup>
            )}
          </select>
        </div>

        {/* Editor Settings */}
        <div>
          <label className="block text-sm text-ide-text-muted mb-2">
            <Code size={14} className="inline mr-1" />
            Editor Font Size
          </label>
          <select 
            className="ide-input w-full text-sm"
            value={settings.editor.fontSize}
            onChange={(e) => handleSettingChange('editor', 'fontSize', parseInt(e.target.value))}
          >
            <option value="10">10px</option>
            <option value="11">11px</option>
            <option value="12">12px</option>
            <option value="13">13px</option>
            <option value="14">14px</option>
            <option value="15">15px</option>
            <option value="16">16px</option>
            <option value="18">18px</option>
            <option value="20">20px</option>
          </select>
        </div>

        {/* Terminal Settings */}
        <div>
          <label className="block text-sm text-ide-text-muted mb-2">
            <Terminal size={14} className="inline mr-1" />
            Terminal Font Size
          </label>
          <select 
            className="ide-input w-full text-sm"
            value={settings.terminal.fontSize}
            onChange={(e) => handleSettingChange('terminal', 'fontSize', parseInt(e.target.value))}
          >
            <option value="10">10px</option>
            <option value="11">11px</option>
            <option value="12">12px</option>
            <option value="13">13px</option>
            <option value="14">14px</option>
            <option value="15">15px</option>
            <option value="16">16px</option>
            <option value="18">18px</option>
            <option value="20">20px</option>
          </select>
        </div>

        {/* Quick Toggles */}
        <div>
          <h4 className="text-sm text-ide-text-muted mb-3">Quick Toggles</h4>
          <div className="space-y-3">
            {/* Zen Mode */}
            <button
              onClick={toggleZenMode}
              className={`w-full flex items-center justify-between p-2 rounded-lg text-sm transition-all duration-200 ${
                settings.ui.zenMode 
                  ? 'bg-ide-accent text-white' 
                  : 'bg-ide-surface hover:bg-ide-surface-hover text-ide-text border border-ide-border'
              }`}
            >
              <div className="flex items-center space-x-2">
                {settings.ui.zenMode ? <Minimize2 size={14} /> : <Maximize2 size={14} />}
                <span>Zen Mode</span>
              </div>
              <span className="text-xs opacity-75">
                {settings.ui.zenMode ? 'ON' : 'OFF'}
              </span>
            </button>

            {/* Minimap Toggle */}
            <button
              onClick={() => handleSettingChange('editor', 'minimap', !settings.editor.minimap)}
              className={`w-full flex items-center justify-between p-2 rounded-lg text-sm transition-all duration-200 ${
                settings.editor.minimap 
                  ? 'bg-ide-accent/20 text-ide-accent border border-ide-accent/30' 
                  : 'bg-ide-surface hover:bg-ide-surface-hover text-ide-text border border-ide-border'
              }`}
            >
              <div className="flex items-center space-x-2">
                {settings.editor.minimap ? <Eye size={14} /> : <EyeOff size={14} />}
                <span>Minimap</span>
              </div>
              <span className="text-xs opacity-75">
                {settings.editor.minimap ? 'ON' : 'OFF'}
              </span>
            </button>

            {/* Mouse Wheel Zoom */}
            <button
              onClick={() => handleSettingChange('editor', 'mouseWheelZoom', !settings.editor.mouseWheelZoom)}
              className={`w-full flex items-center justify-between p-2 rounded-lg text-sm transition-all duration-200 ${
                settings.editor.mouseWheelZoom 
                  ? 'bg-ide-accent/20 text-ide-accent border border-ide-accent/30' 
                  : 'bg-ide-surface hover:bg-ide-surface-hover text-ide-text border border-ide-border'
              }`}
            >
              <div className="flex items-center space-x-2">
                <Code size={14} />
                <span>Ctrl+Scroll Zoom</span>
              </div>
              <span className="text-xs opacity-75">
                {settings.editor.mouseWheelZoom ? 'ON' : 'OFF'}
              </span>
            </button>

            {/* Format on Save */}
            <button
              onClick={() => handleSettingChange('editor', 'formatOnSave', !settings.editor.formatOnSave)}
              className={`w-full flex items-center justify-between p-2 rounded-lg text-sm transition-all duration-200 ${
                settings.editor.formatOnSave 
                  ? 'bg-ide-accent/20 text-ide-accent border border-ide-accent/30' 
                  : 'bg-ide-surface hover:bg-ide-surface-hover text-ide-text border border-ide-border'
              }`}
            >
              <div className="flex items-center space-x-2">
                <Code size={14} />
                <span>Format on Save</span>
              </div>
              <span className="text-xs opacity-75">
                {settings.editor.formatOnSave ? 'ON' : 'OFF'}
              </span>
            </button>
          </div>
        </div>

        {/* Tab Size Quick Setting */}
        <div>
          <label className="block text-sm text-ide-text-muted mb-2">
            Tab Size
          </label>
          <div className="flex space-x-1">
            {[2, 4, 8].map((size) => (
              <button
                key={size}
                onClick={() => handleSettingChange('editor', 'tabSize', size)}
                className={`flex-1 py-1 px-2 text-xs rounded transition-all duration-200 ${
                  settings.editor.tabSize === size
                    ? 'bg-ide-accent text-white'
                    : 'bg-ide-surface hover:bg-ide-surface-hover text-ide-text border border-ide-border'
                }`}
              >
                {size}
              </button>
            ))}
          </div>
        </div>

        {/* Word Wrap Quick Setting */}
        <div>
          <label className="block text-sm text-ide-text-muted mb-2">
            Word Wrap
          </label>
          <div className="flex space-x-1">
            {[
              { value: 'off', label: 'Off' },
              { value: 'on', label: 'On' }
            ].map((option) => (
              <button
                key={option.value}
                onClick={() => handleSettingChange('editor', 'wordWrap', option.value)}
                className={`flex-1 py-1 px-2 text-xs rounded transition-all duration-200 ${
                  settings.editor.wordWrap === option.value
                    ? 'bg-ide-accent text-white'
                    : 'bg-ide-surface hover:bg-ide-surface-hover text-ide-text border border-ide-border'
                }`}
              >
                {option.label}
              </button>
            ))}
          </div>
        </div>

        {/* Link to Full Settings */}
        <div className="pt-2 border-t border-ide-border">
          <a
            href="/settings"
            className="block w-full text-center py-2 px-3 text-sm text-ide-accent hover:text-ide-accent-hover transition-colors"
          >
            Open Full Settings →
          </a>
        </div>
      </div>
    </div>
  )
}

export default SettingsPanel
