import React from 'react'
import {
  <PERSON>u,
  <PERSON>,
  <PERSON>ting<PERSON>,
  Home,
  Terminal as TerminalIcon
} from 'lucide-react'
import { useNavigate } from 'react-router-dom'
import { useAppStore } from '../../stores/appStore'
import { But<PERSON>, StatusIndicator, ThemeSelector } from '../UI'
import RunButton from '../IDE/RunButton'
import ContainerStatus from '../IDE/ContainerStatus'

const Header = () => {
  const navigate = useNavigate()
  const {
    layout,
    updateLayout,
    currentProject,
    systemStatus,
    currentFile,
    addNotification
  } = useAppStore()

  const toggleSidebar = () => {
    updateLayout({ showSidebar: !layout.showSidebar })
  }

  const toggleTerminal = () => {
    updateLayout({ showTerminal: !layout.showTerminal })
  }

  const handleRunOutput = (output) => {
    addNotification({
      type: output.type === 'success' ? 'success' : 'info',
      message: `Execution ${output.type === 'success' ? 'completed' : 'finished'}: ${output.command}`,
    })
  }

  const handleRunError = (error) => {
    addNotification({
      type: 'error',
      message: `Execution failed: ${error.error || error.message}`,
    })
  }

  return (
    <header className="h-14 ide-header-blur ide-topbar flex items-center justify-between px-6 relative z-10">
      {/* Left Section */}
      <div className="flex items-center space-x-3">
        <button
          onClick={toggleSidebar}
          className={`ide-header-button p-2 text-ide-text-muted hover:text-ide-text ${
            layout.showSidebar ? 'active' : ''
          }`}
          title="Toggle Sidebar"
        >
          {layout.showSidebar ? <X size={18} /> : <Menu size={18} />}
        </button>

        <button
          onClick={() => navigate('/')}
          className="ide-header-button flex items-center space-x-2 px-3 py-2 text-ide-text hover:text-ide-accent"
        >
          <Home size={16} />
          <span className="text-sm font-medium">Cloud IDE</span>
        </button>

        {currentProject && (
          <div className="flex items-center space-x-2 text-sm">
            <div className="ide-header-separator"></div>
            <div className="flex items-center space-x-2 px-2 py-1 rounded-md bg-ide-surface/30 border border-ide-border/30">
              <span className="text-ide-text-muted">/</span>
              <span className="text-ide-text font-medium">{currentProject.name}</span>
            </div>
          </div>
        )}
      </div>

      {/* Center Section - Project Controls */}
      <div className="ide-header-center-panel flex items-center space-x-3 px-4 py-2 rounded-lg">
        <RunButton
          projectId={currentProject?.id}
          currentFile={currentFile?.name}
          onOutput={handleRunOutput}
          onError={handleRunError}
          variant="secondary"
        />

        <div className="ide-header-separator"></div>

        <ContainerStatus
          projectId={currentProject?.id}
          showControls={true}
        />
      </div>

      {/* Right Section */}
      <div className="flex items-center space-x-3">
        {/* System Status */}
        <div className="ide-header-status-panel flex items-center space-x-2 px-3 py-2 rounded-lg">
          <StatusIndicator
            status={systemStatus.containerManager}
            label="CM"
            size="xs"
          />
          <StatusIndicator
            status={systemStatus.dnsServer}
            label="DNS"
            size="xs"
          />
          <StatusIndicator
            status={systemStatus.redis}
            label="Redis"
            size="xs"
          />
        </div>

        <div className="ide-header-separator"></div>

        <button
          onClick={toggleTerminal}
          className={`ide-header-button flex items-center space-x-2 px-3 py-2 ${
            layout.showTerminal ? 'active' : 'text-ide-text-muted hover:text-ide-text'
          }`}
          title="Toggle Terminal"
        >
          <TerminalIcon size={16} />
          <span className="text-sm font-medium">Terminal</span>
        </button>

        <div className="ide-header-separator"></div>

        <ThemeSelector />

        <button
          onClick={() => navigate('/settings')}
          className="ide-header-button p-2 text-ide-text-muted hover:text-ide-text"
          title="Settings"
        >
          <Settings size={18} />
        </button>
      </div>
    </header>
  )
}

export default Header
