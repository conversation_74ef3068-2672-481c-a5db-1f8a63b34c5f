import React, { useState, useEffect } from 'react'
import { Play, Square, RefreshCw, Container, AlertCircle, CheckCircle } from 'lucide-react'
import { terminalService } from '../../services/terminal'

const ContainerStatus = ({ 
  projectId, 
  onStatusChange,
  className = '',
  showControls = true 
}) => {
  const [status, setStatus] = useState(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)
  const [lastUpdated, setLastUpdated] = useState(null)

  useEffect(() => {
    if (projectId) {
      fetchContainerStatus()
      
      // Set up periodic status updates
      const interval = setInterval(fetchContainerStatus, 10000) // Every 10 seconds
      
      return () => clearInterval(interval)
    }
  }, [projectId])

  const fetchContainerStatus = async () => {
    if (!projectId) return

    try {
      setError(null)
      const statusData = await terminalService.getContainerStatus(projectId)
      setStatus(statusData)
      setLastUpdated(new Date())
      
      if (onStatusChange) {
        onStatusChange(statusData)
      }
    } catch (error) {
      console.error('Failed to fetch container status:', error)
      setError(error.message)
    }
  }

  const handleStart = async () => {
    if (!projectId) return

    setLoading(true)
    setError(null)

    try {
      await terminalService.startProject(projectId)
      // Refresh status after starting
      setTimeout(fetchContainerStatus, 2000)
    } catch (error) {
      console.error('Failed to start container:', error)
      setError(error.message)
    } finally {
      setLoading(false)
    }
  }

  const handleStop = async () => {
    if (!projectId) return

    setLoading(true)
    setError(null)

    try {
      await terminalService.stopProject(projectId)
      // Refresh status after stopping
      setTimeout(fetchContainerStatus, 2000)
    } catch (error) {
      console.error('Failed to stop container:', error)
      setError(error.message)
    } finally {
      setLoading(false)
    }
  }

  const handleRefresh = () => {
    fetchContainerStatus()
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'running':
        return 'text-green-600'
      case 'stopped':
        return 'text-yellow-600'
      case 'not_deployed':
        return 'text-gray-600'
      case 'error':
        return 'text-red-600'
      default:
        return 'text-gray-600'
    }
  }

  const getStatusIcon = (status) => {
    switch (status) {
      case 'running':
        return <CheckCircle size={16} className="text-green-600" />
      case 'stopped':
        return <Square size={16} className="text-yellow-600" />
      case 'not_deployed':
        return <Container size={16} className="text-gray-600" />
      case 'error':
        return <AlertCircle size={16} className="text-red-600" />
      default:
        return <Container size={16} className="text-gray-600" />
    }
  }

  const getStatusText = (status) => {
    switch (status) {
      case 'running':
        return 'Running'
      case 'stopped':
        return 'Stopped'
      case 'not_deployed':
        return 'Not Deployed'
      case 'error':
        return 'Error'
      default:
        return 'Unknown'
    }
  }

  const formatUptime = (created) => {
    if (!created) return null
    
    const createdDate = new Date(created)
    const now = new Date()
    const diffMs = now - createdDate
    const diffMins = Math.floor(diffMs / 60000)
    const diffHours = Math.floor(diffMins / 60)
    const diffDays = Math.floor(diffHours / 24)
    
    if (diffDays > 0) {
      return `${diffDays}d ${diffHours % 24}h`
    } else if (diffHours > 0) {
      return `${diffHours}h ${diffMins % 60}m`
    } else {
      return `${diffMins}m`
    }
  }

  if (!projectId) {
    return (
      <div className={`flex items-center space-x-2 text-ide-text-muted ${className}`}>
        <Container size={16} />
        <span className="text-sm">No project selected</span>
      </div>
    )
  }

  return (
    <div className={`flex items-center space-x-3 ${className}`}>
      {/* Status Indicator */}
      <div className="flex items-center space-x-2">
        {status ? getStatusIcon(status.status) : <Container size={16} className="text-gray-600" />}
        <div className="flex flex-col">
          <span className={`text-sm font-medium ${status ? getStatusColor(status.status) : 'text-gray-600'}`}>
            {status ? getStatusText(status.status) : 'Loading...'}
          </span>
          {status?.containerInfo && status.status === 'running' && (
            <span className="text-xs text-ide-text-muted">
              Uptime: {formatUptime(status.containerInfo.created)}
            </span>
          )}
        </div>
      </div>

      {/* Container Info */}
      {status?.containerId && (
        <div className="text-xs text-ide-text-muted">
          <span title={status.containerId}>
            ID: {status.containerId.substring(0, 12)}...
          </span>
        </div>
      )}

      {/* Controls */}
      {showControls && (
        <div className="flex items-center space-x-1">
          {status?.status === 'running' ? (
            <button
              onClick={handleStop}
              disabled={loading}
              className="p-1 hover:bg-ide-border rounded text-ide-text-muted hover:text-ide-text transition-colors"
              title="Stop container"
            >
              <Square size={14} />
            </button>
          ) : (
            <button
              onClick={handleStart}
              disabled={loading}
              className="p-1 hover:bg-ide-border rounded text-ide-text-muted hover:text-ide-text transition-colors"
              title="Start container"
            >
              <Play size={14} />
            </button>
          )}
          
          <button
            onClick={handleRefresh}
            disabled={loading}
            className="p-1 hover:bg-ide-border rounded text-ide-text-muted hover:text-ide-text transition-colors"
            title="Refresh status"
          >
            <RefreshCw size={14} className={loading ? 'animate-spin' : ''} />
          </button>
        </div>
      )}

      {/* Error Display */}
      {error && (
        <div className="flex items-center space-x-1 text-red-600">
          <AlertCircle size={14} />
          <span className="text-xs" title={error}>Error</span>
        </div>
      )}

      {/* Last Updated */}
      {lastUpdated && (
        <span className="text-xs text-ide-text-muted" title={lastUpdated.toLocaleString()}>
          Updated {Math.floor((new Date() - lastUpdated) / 1000)}s ago
        </span>
      )}
    </div>
  )
}

export default ContainerStatus
