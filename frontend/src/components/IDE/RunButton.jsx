import React, { useState } from 'react'
import { Play, Square, Loader2, Terminal as TerminalIcon } from 'lucide-react'
import { terminalService } from '../../services/terminal'

const RunButton = ({ 
  projectId, 
  currentFile, 
  onOutput, 
  onError,
  className = '',
  variant = 'primary' // 'primary' | 'secondary' | 'ghost'
}) => {
  const [isRunning, setIsRunning] = useState(false)
  const [lastOutput, setLastOutput] = useState(null)

  const getLanguageFromFile = (fileName) => {
    if (!fileName) return null
    
    const extension = fileName.split('.').pop()?.toLowerCase()
    const languageMap = {
      'js': 'javascript',
      'jsx': 'javascript',
      'ts': 'javascript',
      'tsx': 'javascript',
      'py': 'python',
      'java': 'java',
      'vue': 'vue',
      'html': 'web',
      'css': 'web',
      'scss': 'web',
      'sass': 'web'
    }
    
    return languageMap[extension] || null
  }

  const getRunCommand = (language, fileName) => {
    const commands = {
      javascript: fileName ? `node ${fileName}` : 'npm start',
      python: fileName ? `python ${fileName}` : 'python main.py',
      java: 'mvn compile exec:java -Dexec.mainClass="com.cloudide.App"',
      vue: 'npm run serve',
      web: fileName && fileName.endsWith('.html') ? `python -m http.server 8000` : 'npm start'
    }
    
    return commands[language] || `echo "No run command defined for ${language}"`
  }

  const handleRun = async () => {
    if (!projectId) {
      onError?.('No project selected')
      return
    }

    setIsRunning(true)
    setLastOutput(null)

    try {
      // Determine if we should run the current file or the whole project
      const language = getLanguageFromFile(currentFile)
      let result

      if (currentFile && language && ['python', 'javascript'].includes(language)) {
        // Run specific file
        const command = getRunCommand(language, currentFile)
        result = await terminalService.executeCommand(projectId, command, {
          timeout: 30000
        })
      } else {
        // Run project using language-specific command
        result = await terminalService.runProject(projectId, currentFile, {
          timeout: 60000
        })
      }

      setLastOutput(result)

      if (result.success) {
        // If using terminal session integration, show different message
        if (result.sessionId) {
          onOutput?.({
            type: 'success',
            output: result.message || 'Command executed in terminal',
            command: result.command,
            sessionId: result.sessionId
          })
        } else {
          onOutput?.({
            type: 'success',
            output: result.output,
            command: result.runCommand || result.command,
            exitCode: result.exitCode
          })
        }
      } else {
        onError?.({
          type: 'execution_error',
          output: result.output,
          command: result.runCommand || result.command,
          exitCode: result.exitCode,
          error: result.error
        })
      }
    } catch (error) {
      console.error('Failed to run code:', error)
      onError?.({
        type: 'system_error',
        error: error.message
      })
    } finally {
      setIsRunning(false)
    }
  }

  const handleStop = async () => {
    // For now, we don't have a direct stop mechanism for individual commands
    // This would require more advanced process management
    setIsRunning(false)
    onOutput?.({
      type: 'info',
      output: 'Execution stopped by user',
      command: 'stop'
    })
  }

  const getButtonStyles = () => {
    const baseStyles = 'inline-flex items-center space-x-2 px-3 py-2 rounded-lg font-medium transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed'
    
    const variants = {
      primary: 'bg-green-600 hover:bg-green-700 text-white shadow-sm',
      secondary: 'bg-ide-surface hover:bg-ide-border text-ide-text border border-ide-border',
      ghost: 'hover:bg-ide-border text-ide-text'
    }
    
    return `${baseStyles} ${variants[variant]}`
  }

  const getFileInfo = () => {
    if (!currentFile) return 'Run Project'
    
    const language = getLanguageFromFile(currentFile)
    if (language) {
      return `Run ${currentFile}`
    }
    
    return 'Run Project'
  }

  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      <button
        onClick={isRunning ? handleStop : handleRun}
        disabled={!projectId}
        className={getButtonStyles()}
        title={isRunning ? 'Stop execution' : getFileInfo()}
      >
        {isRunning ? (
          <>
            <Square size={16} />
            <span>Stop</span>
          </>
        ) : (
          <>
            <Play size={16} />
            <span>Run</span>
          </>
        )}
      </button>

      {isRunning && (
        <div className="flex items-center space-x-2 text-ide-text-muted">
          <Loader2 size={14} className="animate-spin" />
          <span className="text-sm">Running...</span>
        </div>
      )}

      {lastOutput && !isRunning && (
        <div className="flex items-center space-x-2">
          {lastOutput.success ? (
            <div className="flex items-center space-x-1 text-green-600">
              <div className="w-2 h-2 bg-green-600 rounded-full" />
              <span className="text-sm">Success</span>
            </div>
          ) : (
            <div className="flex items-center space-x-1 text-red-600">
              <div className="w-2 h-2 bg-red-600 rounded-full" />
              <span className="text-sm">Error</span>
            </div>
          )}
        </div>
      )}
    </div>
  )
}

export default RunButton
