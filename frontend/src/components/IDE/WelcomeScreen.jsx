import React, { useEffect, useRef } from 'react'
import {
  FileText,
  FolderOpen,
  Terminal,
  Code,
  Zap,
  Palette,
  Keyboard,
  GitBranch,
  Settings
} from 'lucide-react'
import { useAppStore } from '../../stores/appStore'
import { ThemeSelector } from '../UI'
import { initializeCursorEffects } from '../../utils/cursorEffects'

const WelcomeScreen = () => {
  const { currentProject } = useAppStore()
  const containerRef = useRef(null)

  // Initialize cursor effects
  useEffect(() => {
    if (containerRef.current) {
      const cleanup = initializeCursorEffects(containerRef.current)
      return cleanup
    }
  }, [])

  const quickActions = [
    {
      icon: FileText,
      title: 'New File',
      description: 'Create a new file',
      shortcut: 'Ctrl+N',
      action: () => console.log('New file')
    },
    {
      icon: FolderOpen,
      title: 'Open Folder',
      description: 'Open an existing folder',
      shortcut: 'Ctrl+O',
      action: () => console.log('Open folder')
    },
    {
      icon: Terminal,
      title: 'New Terminal',
      description: 'Open a new terminal session',
      shortcut: 'Ctrl+`',
      action: () => console.log('New terminal')
    },
    {
      icon: GitBranch,
      title: 'Clone Repository',
      description: 'Clone from Git repository',
      shortcut: 'Ctrl+Shift+P',
      action: () => console.log('Clone repo')
    }
  ]

  const recentFiles = [
    { name: 'index.js', path: '/src/index.js', modified: '2 hours ago' },
    { name: 'App.jsx', path: '/src/App.jsx', modified: '3 hours ago' },
    { name: 'package.json', path: '/package.json', modified: '1 day ago' },
    { name: 'README.md', path: '/README.md', modified: '2 days ago' }
  ]

  const tips = [
    {
      icon: Keyboard,
      title: 'Keyboard Shortcuts',
      description: 'Press Ctrl+Shift+P to open the command palette'
    },
    {
      icon: Zap,
      title: 'Quick Actions',
      description: 'Use Ctrl+P to quickly open files'
    },
    {
      icon: Palette,
      title: 'Themes',
      description: 'Customize your IDE with beautiful themes'
    }
  ]

  return (
    <div ref={containerRef} className="h-full bg-ide-bg overflow-auto ide-scrollbar">
      <div className="max-w-7xl mx-auto p-8">
        {/* Header */}
        <div className="text-center mb-12 animate-fade-in-up">
          <div className="inline-flex items-center justify-center w-24 h-24 interactive-card rounded-2xl mb-6 animate-bounce-in border border-ide-accent/20" data-color="accent">
            <Code className="text-ide-accent" size={36} />
          </div>
          <h1 className="text-5xl font-bold text-ide-text mb-4 bg-gradient-to-r from-ide-text to-ide-accent bg-clip-text text-transparent">
            Welcome to Cloud IDE
          </h1>
          <p className="text-xl text-ide-text-muted max-w-3xl mx-auto leading-relaxed">
            {currentProject
              ? `Working on ${currentProject.name}`
              : 'Start coding in the cloud with our powerful development environment'
            }
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Quick Actions */}
          <div className="lg:col-span-2">
            <h2 className="text-2xl font-bold text-ide-text mb-6 flex items-center">
              <Zap className="mr-3 text-ide-accent" size={24} />
              Quick Actions
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
              {quickActions.map((action, index) => (
                <button
                  key={action.title}
                  onClick={action.action}
                  className="interactive-card group relative overflow-hidden rounded-xl p-6 text-left animate-fade-in-up border border-ide-border/30 hover:border-ide-accent/20"
                  style={{ animationDelay: `${index * 100}ms` }}
                  data-color="accent"
                >
                  <div className="relative z-10">
                    <div className="flex items-start space-x-4">
                      <div className="w-12 h-12 bg-gradient-to-br from-ide-accent/20 to-ide-accent/10 rounded-xl flex items-center justify-center border border-ide-accent/20 group-hover:border-ide-accent/30 transition-all duration-300">
                        <action.icon className="text-ide-accent" size={20} />
                      </div>
                      <div className="flex-1">
                        <h3 className="font-semibold text-ide-text mb-2 group-hover:text-ide-accent transition-colors duration-300">
                          {action.title}
                        </h3>
                        <p className="text-sm text-ide-text-muted mb-3 leading-relaxed">{action.description}</p>
                        <span className="text-xs text-ide-accent bg-ide-accent/10 px-3 py-1.5 rounded-lg font-medium border border-ide-accent/20">
                          {action.shortcut}
                        </span>
                      </div>
                    </div>
                  </div>
                </button>
              ))}
            </div>

            {/* Recent Files */}
            <h2 className="text-2xl font-bold text-ide-text mb-6 flex items-center">
              <FileText className="mr-3 text-ide-accent" size={24} />
              Recent Files
            </h2>
            <div className="interactive-card rounded-xl p-6 animate-fade-in-up border border-ide-border/30" style={{ animationDelay: '400ms' }}>
              {recentFiles.length > 0 ? (
                <div className="space-y-2">
                  {recentFiles.map((file, index) => (
                    <button
                      key={file.path}
                      className="w-full flex items-center justify-between p-4 hover:bg-ide-surface-hover/50 rounded-xl transition-all duration-200 group border border-transparent hover:border-ide-border/30"
                    >
                      <div className="flex items-center space-x-4">
                        <div className="w-8 h-8 bg-ide-accent/10 rounded-lg flex items-center justify-center border border-ide-accent/20 group-hover:border-ide-accent/30 transition-all duration-200">
                          <FileText className="text-ide-accent" size={14} />
                        </div>
                        <div className="text-left">
                          <div className="font-semibold text-ide-text group-hover:text-ide-accent transition-colors duration-200">
                            {file.name}
                          </div>
                          <div className="text-sm text-ide-text-muted">{file.path}</div>
                        </div>
                      </div>
                      <span className="text-xs text-ide-text-muted bg-ide-bg/50 px-2 py-1 rounded-md">
                        {file.modified}
                      </span>
                    </button>
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <div className="w-16 h-16 bg-ide-accent/10 rounded-2xl flex items-center justify-center mx-auto mb-4 border border-ide-accent/20">
                    <FileText className="text-ide-accent" size={24} />
                  </div>
                  <p className="text-ide-text-muted font-medium">No recent files</p>
                  <p className="text-sm text-ide-text-muted/70 mt-1">Start coding to see your recent files here</p>
                </div>
              )}
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-8">
            {/* Theme Selector */}
            <div className="animate-fade-in-up" style={{ animationDelay: '200ms' }}>
              <h3 className="text-xl font-bold text-ide-text mb-4 flex items-center">
                <Palette className="mr-3 text-ide-accent" size={20} />
                Appearance
              </h3>
              <div className="interactive-card rounded-xl p-6 border border-ide-border/30">
                <ThemeSelector className="w-full" />
              </div>
            </div>

            {/* Tips */}
            <div className="animate-fade-in-up" style={{ animationDelay: '300ms' }}>
              <h3 className="text-xl font-bold text-ide-text mb-4 flex items-center">
                <Settings className="mr-3 text-ide-accent" size={20} />
                Tips & Tricks
              </h3>
              <div className="space-y-3">
                {tips.map((tip, index) => (
                  <div
                    key={tip.title}
                    className="interactive-card rounded-xl p-5 border border-ide-border/30 hover:border-ide-accent/20"
                    style={{ animationDelay: `${(index + 1) * 100}ms` }}
                    data-color="accent"
                  >
                    <div className="relative z-10">
                      <div className="flex items-start space-x-4">
                        <div className="w-10 h-10 bg-gradient-to-br from-ide-accent/20 to-ide-accent/10 rounded-xl flex items-center justify-center border border-ide-accent/20">
                          <tip.icon className="text-ide-accent" size={16} />
                        </div>
                        <div className="flex-1">
                          <h4 className="font-semibold text-ide-text mb-2">{tip.title}</h4>
                          <p className="text-sm text-ide-text-muted leading-relaxed">{tip.description}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default WelcomeScreen
