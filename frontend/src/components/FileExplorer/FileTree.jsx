import React, { useState, useEffect } from 'react'
import {
  ChevronRight,
  ChevronDown,
  Plus,
  MoreHorizontal
} from 'lucide-react'
import { useAppStore } from '../../stores/appStore'
import { fileService } from '../../services/api'
import { clsx } from 'clsx'
import FileIcon from '../UI/FileIcon'

const FileTreeNode = ({
  node,
  level = 0,
  onFileSelect,
  onContextMenu,
  projectId = 'default'
}) => {
  const [isExpanded, setIsExpanded] = useState(false)
  const [children, setChildren] = useState([])
  const [loading, setLoading] = useState(false)
  const { currentFile } = useAppStore()

  const isDirectory = node.type === 'directory'
  const isSelected = currentFile?.path === node.path

  const handleToggle = async () => {
    if (!isDirectory) return

    if (!isExpanded && children.length === 0) {
      setLoading(true)
      try {
        const response = await fileService.listDirectory(node.path, projectId)
        setChildren(response.files || [])
      } catch (error) {
        console.error('Failed to load directory:', error)
      } finally {
        setLoading(false)
      }
    }
    
    setIsExpanded(!isExpanded)
  }

  const handleClick = () => {
    if (isDirectory) {
      handleToggle()
    } else {
      onFileSelect?.(node)
    }
  }

  const handleContextMenu = (e) => {
    e.preventDefault()
    onContextMenu?.(e, node)
  }

  const getFileIcon = () => {
    return (
      <FileIcon
        fileName={node.name}
        isDirectory={isDirectory}
        isExpanded={isExpanded}
        size={16}
      />
    )
  }

  return (
    <div>
      <div
        className={clsx(
          'flex items-center space-x-2 py-2 px-2 hover:bg-ide-surface-hover cursor-pointer text-sm group rounded-md mx-1',
          isSelected && 'bg-ide-accent/20 text-ide-accent border-l-2 border-ide-accent',
          'transition-all duration-200'
        )}
        style={{ paddingLeft: `${level * 16 + 8}px` }}
        onClick={handleClick}
        onContextMenu={handleContextMenu}
      >
        <div className="flex items-center space-x-2 min-w-0 flex-1">
          {isDirectory && (
            <div className="flex-shrink-0 text-ide-text-muted">
              {loading ? (
                <div className="animate-spin w-4 h-4">
                  <div className="w-2 h-2 bg-current rounded-full"></div>
                </div>
              ) : isExpanded ? (
                <ChevronDown size={14} />
              ) : (
                <ChevronRight size={14} />
              )}
            </div>
          )}
          <div className="flex-shrink-0">
            {getFileIcon()}
          </div>
          <span className="truncate text-ide-text">{node.name}</span>
        </div>
        
        <button
          className="opacity-0 group-hover:opacity-100 p-1 hover:bg-ide-surface rounded transition-opacity"
          onClick={(e) => {
            e.stopPropagation()
            onContextMenu?.(e, node)
          }}
        >
          <MoreHorizontal size={12} />
        </button>
      </div>

      {isDirectory && isExpanded && children.length > 0 && (
        <div className="animate-fade-in">
          {children.map((child, index) => (
            <div
              key={child.path}
              className="animate-slide-in-right"
              style={{ animationDelay: `${index * 50}ms` }}
            >
              <FileTreeNode
                node={child}
                level={level + 1}
                onFileSelect={onFileSelect}
                onContextMenu={onContextMenu}
                projectId={projectId}
              />
            </div>
          ))}
        </div>
      )}
    </div>
  )
}

const FileTree = ({ rootPath = '/', onFileSelect }) => {
  const [rootNodes, setRootNodes] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const { currentProject } = useAppStore()

  useEffect(() => {
    loadRootDirectory()
  }, [rootPath])

  const loadRootDirectory = async () => {
    setLoading(true)
    setError(null)

    const projectId = currentProject?.id || 'default'

    try {
      const response = await fileService.listDirectory(rootPath, projectId)
      setRootNodes(response.files || [])
      setError(null)
    } catch (err) {
      console.error('Failed to load root directory:', err)
      setError('Failed to load files')

      // Try to initialize project if it's empty
      try {
        await fileService.initializeProject(projectId)
        const retryResponse = await fileService.listDirectory(rootPath, projectId)
        setRootNodes(retryResponse.files || [])
        setError(null)
      } catch (initError) {
        console.error('Failed to initialize project:', initError)
        // Fallback to empty state
        setRootNodes([])
      }
    } finally {
      setLoading(false)
    }
  }

  const handleContextMenu = (e, node) => {
    // TODO: Implement context menu
    console.log('Context menu for:', node)
  }

  if (loading) {
    return (
      <div className="p-4 text-center text-ide-text-muted">
        <div className="animate-spin w-6 h-6 border-2 border-ide-accent border-t-transparent rounded-full mx-auto mb-2"></div>
        Loading files...
      </div>
    )
  }

  if (error) {
    return (
      <div className="p-4 text-center">
        <p className="text-ide-error text-sm mb-2">{error}</p>
        <button
          onClick={loadRootDirectory}
          className="text-ide-accent hover:text-ide-accent-hover text-sm"
        >
          Retry
        </button>
      </div>
    )
  }

  return (
    <div className="h-full overflow-auto">
      <div className="p-2">
        <div className="flex items-center justify-between mb-2">
          <h3 className="text-sm font-medium text-ide-text">Files</h3>
          <button
            className="p-1 hover:bg-ide-border rounded text-ide-text-muted hover:text-ide-text"
            title="New File"
          >
            <Plus size={16} />
          </button>
        </div>
        
        {rootNodes.length === 0 ? (
          <p className="text-ide-text-muted text-sm">No files found</p>
        ) : (
          <div>
            {rootNodes.map((node) => (
              <FileTreeNode
                key={node.path}
                node={node}
                onFileSelect={onFileSelect}
                onContextMenu={handleContextMenu}
                projectId={currentProject?.id || 'default'}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  )
}

export default FileTree
