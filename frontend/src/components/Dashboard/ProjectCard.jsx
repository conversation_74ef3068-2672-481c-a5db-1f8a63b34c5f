import React, { useState, useRef, useEffect } from 'react'
import { gsap } from 'gsap'
import {
  Play,
  Square,
  Settings,
  Trash2,
  Clock,
  Calendar,
  Activity,
  ExternalLink
} from 'lucide-react'
import { <PERSON><PERSON>, StatusIndicator } from '../UI'
import { clsx } from 'clsx'
import {
  getLanguageConfig,
  formatRelativeTime,
  formatCreationDate,
  JavaScriptIcon,
  PythonIcon,
  JavaIcon,
  ReactIcon,
  VueIcon,
  TypeScriptIcon,
  HTMLIcon,
  CSSIcon,
  NodeJSIcon,
  GeneralIcon
} from '../../utils/languageIcons.jsx'

// Enhanced language icon function using React components
const getLanguageIcon = (language, size = 24) => {
  const iconProps = { size, className: 'w-full h-full' }

  switch (language?.toLowerCase()) {
    case 'javascript':
      return <JavaScriptIcon {...iconProps} />
    case 'python':
      return <PythonIcon {...iconProps} />
    case 'java':
      return <JavaIcon {...iconProps} />
    case 'react':
      return <ReactIcon {...iconProps} />
    case 'typescript':
      return <TypeScriptIcon {...iconProps} />
    case 'html':
      return <HTMLIcon {...iconProps} />
    case 'css':
      return <CSSIcon {...iconProps} />
    case 'nodejs':
    case 'node':
      return <NodeJSIcon {...iconProps} />
    case 'vue':
      return <VueIcon {...iconProps} />
    default:
      return <GeneralIcon {...iconProps} />
  }
}



const ProjectCard = ({
  project,
  onStart,
  onSettings,
  onDelete,
  viewMode = 'grid',
  className = ''
}) => {
  const [isLoading, setIsLoading] = useState(false)
  const [actionType, setActionType] = useState(null)
  const [isDeleting, setIsDeleting] = useState(false)
  const cardRef = useRef(null)

  const isRunning = project.status === 'running'
  const isStarting = project.status === 'starting'
  const isStopping = project.status === 'stopping'
  const isTransitioning = isStarting || isStopping
  const projectLanguage = project.settings?.language || project.language || 'general'
  const languageConfig = getLanguageConfig(projectLanguage)

  // Handle delete - call delete dialog directly without animation
  const handleDeleteClick = () => {
    if (isDeleting) return
    onDelete?.(project)
  }

  const handleAction = async (action, type, ...args) => {
    setIsLoading(true)
    setActionType(type)
    try {
      await action(...args)
    } finally {
      setIsLoading(false)
      setActionType(null)
    }
  }

  const getStatusColor = (status) => {
    const colors = {
      running: 'text-green-400',
      stopped: 'text-gray-400',
      starting: 'text-yellow-400',
      error: 'text-red-400'
    }
    return colors[status] || 'text-gray-400'
  }

  const cardClasses = viewMode === 'list'
    ? 'interactive-card group relative rounded-xl p-4 cursor-pointer overflow-hidden'
    : 'interactive-card group relative rounded-xl p-5 cursor-pointer overflow-hidden'

  return (
    <div
      ref={cardRef}
      className={clsx(
        cardClasses,
        'animate-fade-in-up',
        className
      )}
      data-color={isRunning ? 'success' : 'accent'}
    >


      {/* Status Indicator */}
      <div className="absolute top-3 right-3 z-20">
        <StatusIndicator
          status={
            isRunning ? 'healthy' :
            isStarting ? 'warning' :
            isStopping ? 'warning' :
            'stopped'
          }
          size="sm"
          showPulse={isRunning || isTransitioning}
        />
      </div>

      {/* Project Header */}
      <div className={clsx(
        'relative z-10',
        viewMode === 'list' ? 'mb-0' : 'mb-4'
      )}>
        <div className={clsx(
          'flex justify-between',
          viewMode === 'list' ? 'items-center' : 'items-start mb-2'
        )}>
          <div className={clsx(
            'flex items-center',
            viewMode === 'list' ? 'space-x-3 flex-1' : 'space-x-3'
          )}>
            {/* Language Icon */}
            <div className={clsx(
              'rounded-xl border backdrop-blur-sm transition-all duration-300 flex items-center justify-center',
              languageConfig.bg,
              languageConfig.border,
              languageConfig.hoverBg,
              languageConfig.hoverBorder,
              'group-hover:scale-110 group-hover:shadow-lg',
              viewMode === 'list' ? 'p-2' : 'p-3'
            )}>
              {getLanguageIcon(projectLanguage, viewMode === 'list' ? 20 : 24)}
            </div>
            <div className="flex-1">
              <h3 className={clsx(
                'font-semibold text-ide-text transition-colors duration-300',
                viewMode === 'list' ? 'text-base mb-0' : 'text-lg mb-1'
              )}>
                {project.name}
              </h3>
              <div className="flex items-center space-x-2">
                <span className={clsx('text-xs font-medium', languageConfig.color)}>
                  {languageConfig.name}
                </span>
                <span className="text-ide-text-muted">•</span>
                <span className="text-xs text-ide-text-muted capitalize">
                  {project.template}
                </span>
                {viewMode === 'list' && project.description && (
                  <>
                    <span className="text-ide-text-muted">•</span>
                    <span className="text-xs text-ide-text-muted truncate max-w-xs">
                      {project.description}
                    </span>
                  </>
                )}
              </div>
            </div>
          </div>
        </div>

        {viewMode === 'grid' && project.description && (
          <p className="text-xs text-ide-text-muted line-clamp-2 mb-3 leading-relaxed">
            {project.description}
          </p>
        )}
      </div>

      {/* Project Stats - Only show in grid view */}
      {viewMode === 'grid' && (
        <div className="relative mb-4 z-10">
          <div className="grid grid-cols-2 gap-3 text-xs">
            <div className="flex items-center space-x-1.5 text-ide-text-muted transition-colors">
              <Calendar size={10} className="text-ide-accent" />
              <span className="font-medium">
                {formatCreationDate(project.createdAt || project.created || project.modified)}
              </span>
            </div>
            <div className="flex items-center space-x-1.5 text-ide-text-muted transition-colors">
              <Clock size={10} className="text-ide-warning" />
              <span className="font-medium">
                {formatRelativeTime(project.lastAccessed || project.modified || project.created)}
              </span>
            </div>
          </div>
        </div>
      )}

      {/* Resource Usage (if running) - Only show in grid view */}
      {viewMode === 'grid' && isRunning && project.stats && (
        <div className="relative mb-4 p-3 bg-ide-surface/20 rounded-lg border border-ide-border/20 z-10">
          <div className="flex items-center justify-between text-xs mb-2">
            <span className="text-ide-text-muted font-medium">Resource Usage</span>
            <Activity size={10} className="text-ide-accent animate-pulse" />
          </div>
          <div className="grid grid-cols-3 gap-2 text-xs">
            <div className="text-center">
              <div className="text-ide-accent font-mono text-sm font-bold">{project.stats.cpu}%</div>
              <div className="text-ide-text-muted">CPU</div>
            </div>
            <div className="text-center">
              <div className="text-ide-success font-mono text-sm font-bold">{project.stats.memory}%</div>
              <div className="text-ide-text-muted">RAM</div>
            </div>
            <div className="text-center">
              <div className="text-ide-warning font-mono text-sm font-bold">{project.stats.uptime}</div>
              <div className="text-ide-text-muted">Uptime</div>
            </div>
          </div>
        </div>
      )}

      {/* Quick Actions */}
      <div className="relative flex items-center justify-between z-10">
        <div className="flex items-center space-x-2">
          {isRunning ? (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleAction(() => onStart(project, 'stop'), 'stop', project)}
              disabled={isTransitioning || (isLoading && actionType === 'stop')}
              loading={isStopping || (isLoading && actionType === 'stop')}
              className="text-red-400 hover:text-red-300 hover:bg-red-400/10 border border-red-400/20 hover:border-red-400/40 transition-all duration-200"
            >
              <Square size={12} />
              <span>{isStopping ? 'Stopping...' : 'Stop'}</span>
            </Button>
          ) : (
            <Button
              variant="primary"
              size="sm"
              onClick={() => handleAction(() => onStart(project, 'start'), 'start', project)}
              disabled={isTransitioning || (isLoading && actionType === 'start')}
              loading={isStarting || (isLoading && actionType === 'start')}
              className="bg-ide-accent hover:bg-ide-accent-hover text-white transition-all duration-200"
            >
              <Play size={12} />
              <span>{isStarting ? 'Starting...' : 'Start'}</span>
            </Button>
          )}

          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleAction(() => onStart(project, 'open'), 'open', project)}
            disabled={!isRunning || isTransitioning || (isLoading && actionType === 'open')}
            className="text-ide-accent hover:text-ide-accent-hover border border-ide-accent/20 hover:border-ide-accent/40 hover:bg-ide-accent/10 transition-all duration-200 disabled:opacity-50"
          >
            <ExternalLink size={12} />
            <span>Open</span>
          </Button>
        </div>

        {/* Modern Delete Button */}
        <div className="flex items-center space-x-2">
          {/* Settings Button */}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onSettings?.(project)}
            className={clsx(
              'transition-all duration-300 border border-ide-border/20 hover:border-ide-accent/40',
              'opacity-0 group-hover:opacity-100 hover:bg-ide-accent/5',
              'text-ide-text-muted hover:text-ide-accent'
            )}
            title="Project Settings"
          >
            <Settings size={12} />
          </Button>

          {/* Delete Button */}
          <Button
            variant="ghost"
            size="sm"
            onClick={handleDeleteClick}
            disabled={isDeleting}
            className={clsx(
              'transition-all duration-300 border border-red-400/20 hover:border-red-400/60',
              'opacity-0 group-hover:opacity-100 hover:bg-red-400/10',
              'text-red-400/70 hover:text-red-400 hover:scale-110 hover:shadow-lg',
              'hover:shadow-red-400/20 active:scale-95',
              isDeleting && 'opacity-50 cursor-not-allowed scale-95'
            )}
            title="Delete Project"
          >
            <Trash2 size={12} className={clsx(
              'transition-all duration-200',
              isDeleting ? 'animate-pulse opacity-50' : 'group-hover:rotate-12'
            )} />
          </Button>
        </div>
      </div>
    </div>
  )
}

export default ProjectCard
