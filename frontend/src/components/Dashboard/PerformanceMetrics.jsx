import React, { useState, useEffect } from 'react'
import { 
  Activity, 
  Cpu, 
  HardDrive, 
  Wifi, 
  TrendingUp,
  TrendingDown,
  Zap,
  Server
} from 'lucide-react'
import { clsx } from 'clsx'

const PerformanceMetrics = ({ projects, systemHealth }) => {
  const [metrics, setMetrics] = useState({
    cpu: 0,
    memory: 0,
    network: 0,
    uptime: 0
  })

  useEffect(() => {
    // Simulate real-time metrics based on running projects
    const runningProjects = projects.filter(p => p.status === 'running').length
    
    setMetrics({
      cpu: Math.min(runningProjects * 15 + Math.random() * 10, 100),
      memory: Math.min(runningProjects * 25 + Math.random() * 15, 100),
      network: Math.random() * 50 + 10,
      uptime: 99.9 - Math.random() * 0.5
    })
  }, [projects])

  const MetricCard = ({ icon, title, value, unit, trend, color, description }) => (
    <div className="group relative overflow-hidden modern-stats-card rounded-xl p-4 transition-all duration-500 hover:scale-105">
      {/* Animated Background */}
      <div className={`absolute inset-0 bg-gradient-to-br from-${color}-500/8 via-${color}-500/4 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500`}></div>
      
      <div className="relative z-10">
        <div className="flex items-center justify-between mb-3">
          <div className={`p-2 bg-gradient-to-br from-${color}-500/20 to-${color}-500/10 rounded-lg border border-${color}-500/30`}>
            {icon}
          </div>
          {trend && (
            <div className={clsx(
              'flex items-center space-x-1 text-xs font-medium',
              trend === 'up' ? 'text-red-400' : 'text-green-400'
            )}>
              {trend === 'up' ? <TrendingUp size={12} /> : <TrendingDown size={12} />}
              <span>{trend === 'up' ? '+2.1%' : '-1.3%'}</span>
            </div>
          )}
        </div>
        
        <div className="space-y-2">
          <div className="flex items-baseline space-x-1">
            <span className="text-2xl font-bold text-ide-text">{value}</span>
            <span className="text-sm text-ide-text-muted">{unit}</span>
          </div>
          <p className="text-xs text-ide-text-muted font-medium">{title}</p>
          {description && (
            <p className="text-xs text-ide-text-muted opacity-75">{description}</p>
          )}
        </div>
        
        {/* Progress Bar */}
        <div className="mt-3 space-y-1">
          <div className="h-1.5 bg-ide-bg/50 rounded-full overflow-hidden">
            <div
              className={`h-full bg-gradient-to-r from-${color}-500 to-${color}-400 rounded-full transition-all duration-1000 ease-out`}
              style={{ width: `${Math.min(parseFloat(value), 100)}%` }}
            ></div>
          </div>
        </div>
      </div>
    </div>
  )

  return (
    <div className="modern-stats-card rounded-2xl p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-gradient-to-br from-purple-500/20 to-purple-500/10 rounded-lg border border-purple-500/30">
            <Activity className="text-purple-400" size={20} />
          </div>
          <div>
            <h3 className="text-lg font-bold text-ide-text">Performance Metrics</h3>
            <p className="text-ide-text-muted text-sm">Real-time system monitoring</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <div className="w-2 h-2 bg-ide-success rounded-full animate-ping"></div>
          <span className="text-xs text-ide-success font-medium">Live</span>
        </div>
      </div>
      
      <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
        <MetricCard
          icon={<Cpu className="text-blue-400" size={16} />}
          title="CPU Usage"
          value={metrics.cpu.toFixed(1)}
          unit="%"
          trend={metrics.cpu > 50 ? 'up' : 'down'}
          color="blue"
          description="Processing load"
        />
        
        <MetricCard
          icon={<HardDrive className="text-green-400" size={16} />}
          title="Memory"
          value={metrics.memory.toFixed(1)}
          unit="%"
          trend={metrics.memory > 70 ? 'up' : 'down'}
          color="green"
          description="RAM utilization"
        />
        
        <MetricCard
          icon={<Wifi className="text-purple-400" size={16} />}
          title="Network"
          value={metrics.network.toFixed(1)}
          unit="MB/s"
          color="purple"
          description="Data transfer"
        />
        
        <MetricCard
          icon={<Server className="text-yellow-400" size={16} />}
          title="Uptime"
          value={metrics.uptime.toFixed(1)}
          unit="%"
          color="yellow"
          description="System availability"
        />
      </div>
      
      {/* System Health Summary */}
      <div className="mt-6 p-4 rounded-xl bg-gradient-to-r from-ide-surface/40 to-ide-surface/20 border border-ide-border/30">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className={`w-3 h-3 rounded-full ${
              Object.values(systemHealth).filter(s => s === 'healthy').length === 3
                ? 'bg-ide-success animate-pulse'
                : Object.values(systemHealth).filter(s => s === 'healthy').length >= 2
                ? 'bg-ide-warning'
                : 'bg-ide-error animate-pulse'
            }`}></div>
            <span className="text-sm font-medium text-ide-text">
              System Status: {
                Object.values(systemHealth).filter(s => s === 'healthy').length === 3
                  ? 'Optimal'
                  : Object.values(systemHealth).filter(s => s === 'healthy').length >= 2
                  ? 'Good'
                  : 'Needs Attention'
              }
            </span>
          </div>
          <div className="text-xs text-ide-text-muted">
            {projects.filter(p => p.status === 'running').length} active containers
          </div>
        </div>
      </div>
    </div>
  )
}

export default PerformanceMetrics
