import axios from 'axios'

// Create axios instances for different services
const containerAPI = axios.create({
  baseURL: '/api',
  timeout: 10000,
})

const dnsAPI = axios.create({
  baseURL: '/dns-api',
  timeout: 10000,
})

// Request interceptors
containerAPI.interceptors.request.use(
  (config) => {
    console.log(`[Container API] ${config.method?.toUpperCase()} ${config.url}`)
    return config
  },
  (error) => Promise.reject(error)
)

dnsAPI.interceptors.request.use(
  (config) => {
    console.log(`[DNS API] ${config.method?.toUpperCase()} ${config.url}`)
    return config
  },
  (error) => Promise.reject(error)
)

// Response interceptors
const handleResponse = (response) => {
  console.log(`[API Response] ${response.status} ${response.config.url}`)
  console.log(`[API Response Data]`, response.data)
  return response.data
}

const handleError = (error) => {
  console.error(`[API Error] ${error.response?.status} ${error.config?.url}:`, error.response?.data || error.message)
  console.error(`[API Error Details]`, {
    status: error.response?.status,
    statusText: error.response?.statusText,
    data: error.response?.data,
    headers: error.response?.headers,
    config: {
      method: error.config?.method,
      url: error.config?.url,
      baseURL: error.config?.baseURL
    }
  })
  throw error
}

containerAPI.interceptors.response.use(handleResponse, handleError)
dnsAPI.interceptors.response.use(handleResponse, handleError)

// Container Management API
export const containerService = {
  // List all containers
  listContainers: () => containerAPI.get('/containers'),
  
  // Create a new container
  createContainer: (config) => containerAPI.post('/containers', config),
  
  // Stop a container
  stopContainer: (id) => containerAPI.post(`/containers/${id}/stop`),
  
  // Remove a container
  removeContainer: (id) => containerAPI.delete(`/containers/${id}`),
  
  // Get container stats
  getContainerStats: (id) => containerAPI.get(`/containers/${id}/stats`),

  // Get system monitoring stats
  getSystemStats: () => containerAPI.get('/monitoring/system'),
  
  // Health check
  healthCheck: async () => {
    try {
      console.log('🔍 Checking Container Manager health...')
      const data = await containerAPI.get('/health')
      console.log('✅ Container Manager health response:', data)
      return data // This is already the response.data due to interceptor
    } catch (error) {
      console.error('❌ Container Manager health check failed:', error.message)
      throw error
    }
  },
}

// DNS Management API
export const dnsService = {
  // List all subdomains
  listSubdomains: () => dnsAPI.get('/dns/subdomains'),
  
  // Create a new subdomain
  createSubdomain: (config) => dnsAPI.post('/dns/subdomains', config),
  
  // Remove a subdomain
  removeSubdomain: (subdomain, domain) => dnsAPI.delete(`/dns/subdomains/${subdomain}`, { data: { domain } }),
  
  // Get all DNS records
  getRecords: () => dnsAPI.get('/dns/records'),
  
  // Health check
  healthCheck: async () => {
    try {
      console.log('🔍 Checking DNS Server health...')
      const data = await dnsAPI.get('/health')
      console.log('✅ DNS Server health response:', data)
      return data // This is already the response.data due to interceptor
    } catch (error) {
      console.error('❌ DNS Server health check failed:', error.message)
      throw error
    }
  },
}

// File System API
export const fileService = {
  // List directory contents
  listDirectory: (path = '/', projectId = 'default') =>
    containerAPI.get(`/files?path=${encodeURIComponent(path)}&projectId=${encodeURIComponent(projectId)}`),

  // Read file content
  readFile: (path, projectId = 'default') =>
    containerAPI.get(`/files/content?path=${encodeURIComponent(path)}&projectId=${encodeURIComponent(projectId)}`),

  // Write file content
  writeFile: (path, content, projectId = 'default') =>
    containerAPI.post('/files/content', { path, content, projectId }),

  // Create directory
  createDirectory: (path, projectId = 'default') =>
    containerAPI.post('/files/directory', { path, projectId }),

  // Delete file or directory
  deleteFile: (path, projectId = 'default') =>
    containerAPI.delete(`/files?path=${encodeURIComponent(path)}&projectId=${encodeURIComponent(projectId)}`),

  // Rename file or directory
  renameFile: (oldPath, newPath, projectId = 'default') =>
    containerAPI.put('/files/rename', { oldPath, newPath, projectId }),

  // Initialize project with sample files
  initializeProject: (projectId = 'default') =>
    containerAPI.post('/files/init-project', { projectId }),
}

// Project Management API
export const projectService = {
  // List all projects
  listProjects: () => containerAPI.get('/projects'),

  // Create a new project
  createProject: (config) => containerAPI.post('/projects', config),

  // Get project details
  getProject: (id) => containerAPI.get(`/projects/${id}`),

  // Update project
  updateProject: (id, updates) => containerAPI.put(`/projects/${id}`, updates),

  // Delete project
  deleteProject: (id) => containerAPI.delete(`/projects/${id}`),

  // Start project container
  startProject: async (id) => {
    console.log(`[ProjectService] Starting project: ${id}`)
    try {
      const result = await containerAPI.post(`/projects/${id}/start`)
      console.log(`[ProjectService] Start project success:`, result)
      return result
    } catch (error) {
      console.error(`[ProjectService] Start project failed:`, error)
      throw error
    }
  },

  // Stop project container
  stopProject: async (id) => {
    console.log(`[ProjectService] Stopping project: ${id}`)
    try {
      const result = await containerAPI.post(`/projects/${id}/stop`)
      console.log(`[ProjectService] Stop project success:`, result)
      return result
    } catch (error) {
      console.error(`[ProjectService] Stop project failed:`, error)
      throw error
    }
  },

  // Get project templates
  getTemplates: () => containerAPI.get('/projects/templates'),

  // Get project statistics
  getStats: () => containerAPI.get('/projects/stats'),

  // Initialize sample projects
  initializeSamples: () => containerAPI.post('/projects/init-samples'),
}

// Terminal API
export const terminalService = {
  // Create new terminal session
  createSession: (options = {}) => containerAPI.post('/terminal/sessions', options),

  // List terminal sessions
  listSessions: (projectId = null) => {
    const params = projectId ? `?projectId=${encodeURIComponent(projectId)}` : ''
    return containerAPI.get(`/terminal/sessions${params}`)
  },

  // Get terminal session info
  getSession: (sessionId) => containerAPI.get(`/terminal/sessions/${sessionId}`),

  // Write data to terminal session
  writeToSession: (sessionId, data) =>
    containerAPI.post(`/terminal/sessions/${sessionId}/write`, { data }),

  // Resize terminal session
  resizeSession: (sessionId, cols, rows) =>
    containerAPI.post(`/terminal/sessions/${sessionId}/resize`, { cols, rows }),

  // Kill terminal session
  killSession: (sessionId) => containerAPI.delete(`/terminal/sessions/${sessionId}`),

  // Get terminal session buffer
  getSessionBuffer: (sessionId) => containerAPI.get(`/terminal/sessions/${sessionId}/buffer`),

  // Terminal health check
  getHealth: () => containerAPI.get('/terminal/health'),

  // Start project container
  startProject: async (projectId) => {
    try {
      const response = await containerAPI.post(`/projects/${projectId}/start`)
      return response
    } catch (error) {
      console.error('Failed to start project:', error)
      throw error
    }
  },

  // Execute command in project container
  executeCommand: async (projectId, command, options = {}) => {
    try {
      const response = await containerAPI.post('/terminal/execute', {
        projectId,
        command,
        shell: options.shell || 'sh',
        timeout: options.timeout || 30000
      })
      return response
    } catch (error) {
      console.error('Failed to execute command:', error)
      throw error
    }
  },

  // Run project (execute main file)
  runProject: async (projectId, fileName = null, options = {}) => {
    try {
      const response = await containerAPI.post('/terminal/run', {
        projectId,
        fileName,
        timeout: options.timeout || 60000
      })
      return response
    } catch (error) {
      console.error('Failed to run project:', error)
      throw error
    }
  }
}

// System Status API
export const systemService = {
  // Get overall system health
  getSystemHealth: async () => {
    console.log('🔍 Checking system health...')

    try {
      const [containerHealth, dnsHealth] = await Promise.allSettled([
        containerService.healthCheck(),
        dnsService.healthCheck(),
      ])

      console.log('📊 Health check results:', {
        containerManager: {
          promiseStatus: containerHealth.status,
          responseData: containerHealth.value,
          healthStatus: containerHealth.value?.status,
          services: containerHealth.value?.services,
          error: containerHealth.reason?.message
        },
        dnsServer: {
          promiseStatus: dnsHealth.status,
          responseData: dnsHealth.value,
          healthStatus: dnsHealth.value?.status,
          error: dnsHealth.reason?.message
        }
      })

      // Determine health status based on Promise.allSettled results
      const containerHealthy = containerHealth.status === 'fulfilled' && containerHealth.value?.status === 'healthy'
      const dnsHealthy = dnsHealth.status === 'fulfilled' && dnsHealth.value?.status === 'healthy'
      const redisHealthy = containerHealthy && containerHealth.value?.services?.integration?.status === 'healthy'

      const health = {
        containerManager: containerHealthy ? 'healthy' : 'unhealthy',
        dnsServer: dnsHealthy ? 'healthy' : 'unhealthy',
        redis: redisHealthy ? 'healthy' : 'unknown',
      }

      console.log('✅ Final health status:', health)
      return health
    } catch (error) {
      console.error('❌ Failed to get system health:', error)
      return {
        containerManager: 'unhealthy',
        dnsServer: 'unhealthy',
        redis: 'unknown',
      }
    }
  },
}
