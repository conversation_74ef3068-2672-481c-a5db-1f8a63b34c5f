import { containerService } from './api'

class MonitoringService {
  constructor() {
    this.statsCache = new Map()
    this.subscribers = new Map()
    this.intervals = new Map()
  }

  /**
   * Get container statistics for a specific project
   */
  async getContainerStats(projectId) {
    try {
      // First get the project to find its container ID
      const response = await fetch(`http://localhost:3000/api/projects/${projectId}`)
      if (!response.ok) {
        throw new Error('Project not found')
      }

      const project = await response.json()
      if (!project.containerId) {
        throw new Error('Project container not deployed')
      }

      if (project.status !== 'running') {
        throw new Error(`Project is ${project.status}. Start the project to view stats.`)
      }

      // Get container stats
      const stats = await containerService.getContainerStats(project.containerId)

      // Process and format the stats
      return this.processContainerStats(stats, project)
    } catch (error) {
      console.error('Failed to get container stats:', error)
      throw error
    }
  }

  /**
   * Get system-wide monitoring data
   */
  async getSystemStats() {
    try {
      const response = await fetch('http://localhost:3000/api/monitoring/system')
      if (!response.ok) {
        throw new Error('Failed to fetch system stats')
      }
      
      return await response.json()
    } catch (error) {
      console.error('Failed to get system stats:', error)
      throw error
    }
  }

  /**
   * Process raw container stats into a more usable format
   */
  processContainerStats(rawStats, project) {
    const stats = {
      projectId: project.id,
      projectName: project.name,
      containerId: project.containerId,
      timestamp: new Date().toISOString(),
      
      // CPU Stats
      cpu: {
        usage: 0,
        cores: 0,
        throttling: 0
      },
      
      // Memory Stats
      memory: {
        usage: 0,
        limit: 0,
        percentage: 0,
        cache: 0,
        swap: 0
      },
      
      // Network Stats
      network: {
        rxBytes: 0,
        txBytes: 0,
        rxPackets: 0,
        txPackets: 0
      },
      
      // Container Info
      container: {
        status: project.status,
        uptime: 0,
        restartCount: 0
      }
    }

    if (rawStats) {
      // Process CPU stats
      if (rawStats.cpu && rawStats.cpu.cpu_usage) {
        const cpuDelta = rawStats.cpu.cpu_usage.total_usage - (rawStats.cpu.precpu_stats?.cpu_usage?.total_usage || 0)
        const systemDelta = rawStats.cpu.system_cpu_usage - (rawStats.cpu.precpu_stats?.system_cpu_usage || 0)
        const cpuCount = rawStats.cpu.online_cpus || 1
        
        if (systemDelta > 0) {
          stats.cpu.usage = (cpuDelta / systemDelta) * cpuCount * 100
        }
        stats.cpu.cores = cpuCount
        stats.cpu.throttling = rawStats.cpu.throttling_data?.throttled_time || 0
      }

      // Process Memory stats
      if (rawStats.memory) {
        stats.memory.usage = rawStats.memory.usage || 0
        stats.memory.limit = rawStats.memory.limit || 0
        stats.memory.percentage = stats.memory.limit > 0 ? (stats.memory.usage / stats.memory.limit) * 100 : 0
        stats.memory.cache = rawStats.memory.stats?.cache || 0
        stats.memory.swap = rawStats.memory.stats?.swap || 0
      }

      // Process Network stats
      if (rawStats.network) {
        const networks = Object.values(rawStats.network)
        stats.network.rxBytes = networks.reduce((sum, net) => sum + (net.rx_bytes || 0), 0)
        stats.network.txBytes = networks.reduce((sum, net) => sum + (net.tx_bytes || 0), 0)
        stats.network.rxPackets = networks.reduce((sum, net) => sum + (net.rx_packets || 0), 0)
        stats.network.txPackets = networks.reduce((sum, net) => sum + (net.tx_packets || 0), 0)
      }
    }

    return stats
  }

  /**
   * Start real-time monitoring for a project
   */
  startMonitoring(projectId, callback, interval = 2000) {
    // Stop existing monitoring if any
    this.stopMonitoring(projectId)

    // Store callback
    this.subscribers.set(projectId, callback)

    // Start polling
    const intervalId = setInterval(async () => {
      try {
        const stats = await this.getContainerStats(projectId)
        
        // Cache the stats
        this.statsCache.set(projectId, stats)
        
        // Notify subscriber
        const subscriber = this.subscribers.get(projectId)
        if (subscriber) {
          subscriber(stats)
        }
      } catch (error) {
        console.error(`Monitoring error for project ${projectId}:`, error)
        const subscriber = this.subscribers.get(projectId)
        if (subscriber) {
          subscriber(null, error)
        }
      }
    }, interval)

    this.intervals.set(projectId, intervalId)
    
    // Get initial stats immediately
    this.getContainerStats(projectId)
      .then(stats => {
        this.statsCache.set(projectId, stats)
        const subscriber = this.subscribers.get(projectId)
        if (subscriber) {
          subscriber(stats)
        }
      })
      .catch(error => {
        const subscriber = this.subscribers.get(projectId)
        if (subscriber) {
          subscriber(null, error)
        }
      })

    return intervalId
  }

  /**
   * Stop monitoring for a project
   */
  stopMonitoring(projectId) {
    const intervalId = this.intervals.get(projectId)
    if (intervalId) {
      clearInterval(intervalId)
      this.intervals.delete(projectId)
    }
    
    this.subscribers.delete(projectId)
    this.statsCache.delete(projectId)
  }

  /**
   * Get cached stats for a project
   */
  getCachedStats(projectId) {
    return this.statsCache.get(projectId)
  }

  /**
   * Format bytes to human readable format
   */
  formatBytes(bytes, decimals = 2) {
    if (bytes === 0) return '0 Bytes'
    
    const k = 1024
    const dm = decimals < 0 ? 0 : decimals
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']
    
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i]
  }

  /**
   * Format percentage
   */
  formatPercentage(value, decimals = 1) {
    return parseFloat(value.toFixed(decimals)) + '%'
  }

  /**
   * Format uptime
   */
  formatUptime(startTime) {
    const now = new Date()
    const start = new Date(startTime)
    const diffMs = now - start
    
    const days = Math.floor(diffMs / (1000 * 60 * 60 * 24))
    const hours = Math.floor((diffMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
    const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60))
    
    if (days > 0) {
      return `${days}d ${hours}h ${minutes}m`
    } else if (hours > 0) {
      return `${hours}h ${minutes}m`
    } else {
      return `${minutes}m`
    }
  }

  /**
   * Clean up all monitoring
   */
  cleanup() {
    for (const [projectId] of this.intervals) {
      this.stopMonitoring(projectId)
    }
  }
}

export const monitoringService = new MonitoringService()
export default monitoringService
