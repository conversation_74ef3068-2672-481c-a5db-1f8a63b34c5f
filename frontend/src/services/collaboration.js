import { io } from 'socket.io-client';

class CollaborationService {
  constructor() {
    this.socket = null;
    this.isConnected = false;
    this.currentRoom = null;
    this.currentUser = null;
    this.collaborators = new Map();
    this.eventHandlers = new Map();
    
    // Throttle cursor updates to prevent performance issues
    this.cursorUpdateThrottle = null;
    this.cursorUpdateDelay = 100; // ms
  }

  /**
   * Connect to collaboration server
   */
  connect() {
    if (this.socket && this.isConnected) {
      return Promise.resolve();
    }

    return new Promise((resolve, reject) => {
      try {
        this.socket = io('http://localhost:3000', {
          transports: ['websocket', 'polling'],
          timeout: 5000
        });

        this.socket.on('connect', () => {
          console.log('Connected to collaboration server');
          this.isConnected = true;
          resolve();
        });

        this.socket.on('disconnect', () => {
          console.log('Disconnected from collaboration server');
          this.isConnected = false;
          this.currentRoom = null;
          this.currentUser = null;
          this.collaborators.clear();
          this.emit('disconnected');
        });

        this.socket.on('connect_error', (error) => {
          console.error('Collaboration connection error:', error);
          this.isConnected = false;
          reject(error);
        });

        // Set up collaboration event listeners
        this.setupEventListeners();

      } catch (error) {
        console.error('Failed to create collaboration socket:', error);
        reject(error);
      }
    });
  }

  /**
   * Disconnect from collaboration server
   */
  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
    this.isConnected = false;
    this.currentRoom = null;
    this.currentUser = null;
    this.collaborators.clear();
  }

  /**
   * Join a collaboration room
   */
  async joinRoom(projectId, filePath, nickname = null) {
    if (!this.isConnected) {
      await this.connect();
    }

    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('Join room timeout'));
      }, 10000);

      this.socket.once('collab:joined', (data) => {
        clearTimeout(timeout);
        this.currentRoom = data.roomId;
        this.currentUser = data.userInfo;
        
        // Update collaborators
        this.collaborators.clear();
        data.collaborators.forEach(user => {
          this.collaborators.set(user.userId, user);
        });

        console.log(`Joined collaboration room: ${data.roomId}`);
        this.emit('room-joined', data);
        resolve(data);
      });

      this.socket.once('collab:error', (data) => {
        clearTimeout(timeout);
        console.error('Failed to join collaboration room:', data.error);
        reject(new Error(data.error));
      });

      this.socket.emit('collab:join', { projectId, filePath, nickname });
    });
  }

  /**
   * Leave current collaboration room
   */
  async leaveRoom() {
    if (!this.currentRoom) {
      return;
    }

    return new Promise((resolve) => {
      this.socket.once('collab:left', (data) => {
        this.currentRoom = null;
        this.currentUser = null;
        this.collaborators.clear();
        console.log('Left collaboration room');
        this.emit('room-left');
        resolve(data);
      });

      this.socket.emit('collab:leave');
    });
  }

  /**
   * Send document changes
   */
  sendDocumentChange(content, changes = null) {
    if (!this.currentRoom || !this.socket) {
      console.warn('Cannot send document change: not in room or not connected');
      return;
    }

    console.log('📤 Sending document change to room:', this.currentRoom);
    this.socket.emit('collab:document-change', { content, changes });
  }

  /**
   * Send cursor position update (throttled)
   */
  sendCursorUpdate(cursor, selection = null) {
    if (!this.currentRoom || !this.socket) {
      return;
    }

    // Throttle cursor updates
    if (this.cursorUpdateThrottle) {
      clearTimeout(this.cursorUpdateThrottle);
    }

    this.cursorUpdateThrottle = setTimeout(() => {
      console.log('🖱️ Sending cursor update to room:', this.currentRoom);
      this.socket.emit('collab:cursor-change', { cursor, selection });
    }, this.cursorUpdateDelay);
  }

  /**
   * Update nickname
   */
  async updateNickname(nickname) {
    if (!this.currentRoom || !this.socket) {
      throw new Error('Not in a collaboration room');
    }

    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('Update nickname timeout'));
      }, 5000);

      this.socket.once('collab:nickname-updated', (data) => {
        clearTimeout(timeout);
        if (data.userId === this.currentUser?.userId) {
          this.currentUser.nickname = data.nickname;
        }
        resolve(data);
      });

      this.socket.once('collab:error', (data) => {
        clearTimeout(timeout);
        reject(new Error(data.error));
      });

      this.socket.emit('collab:update-nickname', { nickname });
    });
  }

  /**
   * Get current room info
   */
  async getRoomInfo() {
    if (!this.currentRoom || !this.socket) {
      return null;
    }

    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('Get room info timeout'));
      }, 5000);

      this.socket.once('collab:room-info', (data) => {
        clearTimeout(timeout);
        resolve(data);
      });

      this.socket.once('collab:error', (data) => {
        clearTimeout(timeout);
        reject(new Error(data.error));
      });

      this.socket.emit('collab:get-room-info');
    });
  }

  /**
   * Set up event listeners for collaboration events
   */
  setupEventListeners() {
    // User joined
    this.socket.on('collab:user-joined', (data) => {
      this.collaborators.set(data.user.userId, data.user);
      console.log(`User ${data.user.nickname} joined the room`);
      this.emit('user-joined', data);
    });

    // User left
    this.socket.on('collab:user-left', (data) => {
      this.collaborators.delete(data.userId);
      console.log(`User left the room`);
      this.emit('user-left', data);
    });

    // Document changed
    this.socket.on('collab:document-changed', (data) => {
      console.log('📝 Document changed by user:', data.userId);
      this.emit('document-changed', data);
    });

    // Cursor changed
    this.socket.on('collab:cursor-changed', (data) => {
      console.log('🖱️ Cursor changed by user:', data.userId);
      this.emit('cursor-changed', data);
    });

    // Nickname updated
    this.socket.on('collab:nickname-updated', (data) => {
      if (data.userId !== this.currentUser?.userId) {
        const user = this.collaborators.get(data.userId);
        if (user) {
          user.nickname = data.nickname;
        }
      }
      this.emit('nickname-updated', data);
    });

    // Error handling
    this.socket.on('collab:error', (data) => {
      console.error('Collaboration error:', data.error);
      this.emit('error', data);
    });
  }

  /**
   * Add event listener
   */
  on(event, handler) {
    if (!this.eventHandlers.has(event)) {
      this.eventHandlers.set(event, []);
    }
    this.eventHandlers.get(event).push(handler);
  }

  /**
   * Remove event listener
   */
  off(event, handler) {
    if (this.eventHandlers.has(event)) {
      const handlers = this.eventHandlers.get(event);
      const index = handlers.indexOf(handler);
      if (index > -1) {
        handlers.splice(index, 1);
      }
    }
  }

  /**
   * Emit event to handlers
   */
  emit(event, data) {
    if (this.eventHandlers.has(event)) {
      this.eventHandlers.get(event).forEach(handler => {
        try {
          handler(data);
        } catch (error) {
          console.error(`Error in collaboration event handler for ${event}:`, error);
        }
      });
    }
  }

  /**
   * Get current collaboration state
   */
  getState() {
    return {
      isConnected: this.isConnected,
      currentRoom: this.currentRoom,
      currentUser: this.currentUser,
      collaborators: Array.from(this.collaborators.values())
    };
  }

  /**
   * Check if currently in a collaboration room
   */
  isInRoom() {
    return !!this.currentRoom;
  }

  /**
   * Safe method to check if service is initialized
   */
  isInitialized() {
    return this.socket !== null;
  }

  /**
   * Get collaborator by user ID
   */
  getCollaborator(userId) {
    return this.collaborators.get(userId);
  }

  /**
   * Get all collaborators
   */
  getCollaborators() {
    return Array.from(this.collaborators.values());
  }
}

// Create singleton instance
export const collaborationService = new CollaborationService();
export default collaborationService;
