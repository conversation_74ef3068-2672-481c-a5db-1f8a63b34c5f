const API_BASE_URL = 'http://localhost:3000/api'

class TerminalService {
  constructor() {
    this.sessions = new Map()
  }

  /**
   * Create a new terminal session
   */
  async createSession(options = {}) {
    try {
      const response = await fetch(`${API_BASE_URL}/terminal/sessions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(options)
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to create terminal session')
      }

      const session = await response.json()
      this.sessions.set(session.sessionId, session)
      return session
    } catch (error) {
      console.error('Failed to create terminal session:', error)
      throw error
    }
  }

  /**
   * Get terminal session info
   */
  async getSession(sessionId) {
    try {
      const response = await fetch(`${API_BASE_URL}/terminal/sessions/${sessionId}`)

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to get terminal session')
      }

      const session = await response.json()
      this.sessions.set(sessionId, session)
      return session
    } catch (error) {
      console.error('Failed to get terminal session:', error)
      throw error
    }
  }

  /**
   * List terminal sessions
   */
  async listSessions(projectId = null) {
    try {
      const url = new URL(`${API_BASE_URL}/terminal/sessions`)
      if (projectId) {
        url.searchParams.set('projectId', projectId)
      }

      const response = await fetch(url)

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to list terminal sessions')
      }

      const sessions = await response.json()
      return sessions
    } catch (error) {
      console.error('Failed to list terminal sessions:', error)
      throw error
    }
  }

  /**
   * Write data to terminal session
   */
  async writeToSession(sessionId, data) {
    try {
      const response = await fetch(`${API_BASE_URL}/terminal/sessions/${sessionId}/write`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ data })
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to write to terminal session')
      }

      return await response.json()
    } catch (error) {
      console.error('Failed to write to terminal session:', error)
      throw error
    }
  }

  /**
   * Resize terminal session
   */
  async resizeSession(sessionId, cols, rows) {
    try {
      const response = await fetch(`${API_BASE_URL}/terminal/sessions/${sessionId}/resize`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ cols, rows })
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to resize terminal session')
      }

      return await response.json()
    } catch (error) {
      console.error('Failed to resize terminal session:', error)
      throw error
    }
  }

  /**
   * Kill terminal session
   */
  async killSession(sessionId) {
    try {
      const response = await fetch(`${API_BASE_URL}/terminal/sessions/${sessionId}`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to kill terminal session')
      }

      this.sessions.delete(sessionId)
      return await response.json()
    } catch (error) {
      console.error('Failed to kill terminal session:', error)
      throw error
    }
  }

  /**
   * Execute a command in a project container
   */
  async executeCommand(projectId, command, options = {}) {
    try {
      // Use terminal session integration by default for better UX
      const useTerminalSession = options.useTerminalSession !== false;

      const response = await fetch(`${API_BASE_URL}/terminal/execute`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          projectId,
          command,
          useTerminalSession,
          ...options
        })
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to execute command')
      }

      return await response.json()
    } catch (error) {
      console.error('Failed to execute command:', error)
      throw error
    }
  }

  /**
   * Run project using language-specific command
   */
  async runProject(projectId, fileName = null, options = {}) {
    try {
      // Use terminal session integration by default for better UX
      const useTerminalSession = options.useTerminalSession !== false;

      const response = await fetch(`${API_BASE_URL}/terminal/run`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          projectId,
          fileName,
          useTerminalSession,
          ...options
        })
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to run project')
      }

      return await response.json()
    } catch (error) {
      console.error('Failed to run project:', error)
      throw error
    }
  }

  /**
   * Get project container status
   */
  async getContainerStatus(projectId) {
    try {
      const response = await fetch(`${API_BASE_URL}/projects/${projectId}/container/status`)

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to get container status')
      }

      return await response.json()
    } catch (error) {
      console.error('Failed to get container status:', error)
      throw error
    }
  }

  /**
   * Start project container
   */
  async startProject(projectId) {
    try {
      const response = await fetch(`${API_BASE_URL}/projects/${projectId}/start`, {
        method: 'POST'
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to start project')
      }

      return await response.json()
    } catch (error) {
      console.error('Failed to start project:', error)
      throw error
    }
  }

  /**
   * Stop project container
   */
  async stopProject(projectId) {
    try {
      const response = await fetch(`${API_BASE_URL}/projects/${projectId}/stop`, {
        method: 'POST'
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to stop project')
      }

      return await response.json()
    } catch (error) {
      console.error('Failed to stop project:', error)
      throw error
    }
  }

  /**
   * Create Socket.IO connection for terminal
   */
  createSocketConnection(sessionId, options = {}) {
    // Import Socket.IO client dynamically to avoid issues
    const { io } = require('socket.io-client')

    const socket = io('http://localhost:3000', {
      transports: ['websocket', 'polling']
    })

    socket.on('connect', () => {
      // Join terminal session
      socket.emit('terminal:join', { sessionId })

      if (options.onOpen) {
        options.onOpen()
      }
    })

    socket.on('terminal:joined', (data) => {
      if (options.onJoined) {
        options.onJoined(data)
      }
    })

    socket.on('terminal:output', (data) => {
      if (options.onMessage) {
        options.onMessage({ type: 'output', data: data.data })
      }
    })

    socket.on('terminal:ended', (data) => {
      if (options.onMessage) {
        options.onMessage({ type: 'exit', exitCode: data.exitCode })
      }
    })

    socket.on('terminal:error', (data) => {
      if (options.onMessage) {
        options.onMessage({ type: 'error', error: data.error })
      }
    })

    socket.on('disconnect', () => {
      if (options.onClose) {
        options.onClose()
      }
    })

    socket.on('connect_error', (error) => {
      console.error('Socket.IO connection error:', error)
      if (options.onError) {
        options.onError(error)
      }
    })

    return socket
  }

  /**
   * Get cached session
   */
  getCachedSession(sessionId) {
    return this.sessions.get(sessionId)
  }

  /**
   * Clear cached sessions
   */
  clearCache() {
    this.sessions.clear()
  }
}

export const terminalService = new TerminalService()
export default terminalService
