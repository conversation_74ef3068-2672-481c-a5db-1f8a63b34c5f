import React, { useEffect, useRef, useState } from 'react'
import { useNavigate } from 'react-router-dom'
import {
  Code,
  Terminal,
  GitBranch,
  Settings,
  Play,
  ArrowRight,
  Cpu,
  Database,
  Cloud,
  Zap,
  Globe,
  Shield,
  Activity,
  Layers,
  Minimize2,
  FolderOpen,
  FileText,
  Keyboard
} from 'lucide-react'
import { pageTransition, cleanupAnimations, createAdvancedAnimations, createFloatingElements, createScrollAnimations, createPremiumCursor, initSmoothScroll } from '../utils/gsapAnimations'
import TechStrip from '../components/Landing/TechStrip'
import UserAvatar from '../components/UI/UserAvatar'

const Landing = () => {
  const navigate = useNavigate()
  const pageRef = useRef(null)
  const heroRef = useRef(null)
  const floatingElementsRef = useRef([])
  const featuresRef = useRef(null)
  const [scrollProgress, setScrollProgress] = useState(0)

  const scrollToFeatures = () => {
    featuresRef.current?.scrollIntoView({
      behavior: 'smooth',
      block: 'start'
    })
  }

  useEffect(() => {
    // Initialize smooth scrolling first
    initSmoothScroll()

    // Fast page entrance
    if (pageRef.current) {
      pageTransition.enter(pageRef.current)
    }

    // Immediate hero animations (no delay)
    if (heroRef.current) {
      createAdvancedAnimations(heroRef.current)
    }

    // Initialize cursor after hero loads
    const cursorCleanup = createPremiumCursor()

    // Scroll progress tracking
    const handleScroll = () => {
      const scrollTop = window.scrollY
      const docHeight = document.documentElement.scrollHeight - window.innerHeight
      const progress = Math.min((scrollTop / docHeight) * 100, 100)
      setScrollProgress(progress)
    }

    // Delayed initialization for non-critical animations
    setTimeout(() => {
      // Floating elements
      if (floatingElementsRef.current.length > 0) {
        createFloatingElements(floatingElementsRef.current)
      }

      // Scroll animations
      const sections = document.querySelectorAll('.scroll-animate')
      if (sections.length > 0) {
        createScrollAnimations(Array.from(sections))
      }
    }, 50)

    window.addEventListener('scroll', handleScroll, { passive: true })

    // Cleanup animations on unmount
    return () => {
      cleanupAnimations()
      cursorCleanup()
      window.removeEventListener('scroll', handleScroll)
    }
  }, [])

  return (
    <div ref={pageRef} className="landing-page relative min-h-screen bg-gradient-to-br from-black via-gray-900 to-black">

      {/* Subtle Background Pattern */}
      <div className="fixed inset-0 opacity-5 pointer-events-none">
        <div className="absolute inset-0" style={{
          backgroundImage: `radial-gradient(circle at 1px 1px, white 1px, transparent 0)`,
          backgroundSize: '50px 50px'
        }} />
      </div>

      {/* Minimal Navigation Lines */}
      <div className="fixed right-6 top-1/2 transform -translate-y-1/2 z-40">
        <div className="flex flex-col space-y-6">
          <div className="group relative">
            <button
              onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
              className={`w-8 h-0.5 transition-all duration-500 ${scrollProgress < 25 ? 'bg-gradient-to-r from-blue-400 to-purple-400 shadow-lg shadow-blue-400/30' : 'bg-white/20 hover:bg-white/40'}`}
              title="Hero Section"
            />
            <div className="absolute right-10 top-1/2 transform -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-all duration-300 pointer-events-none">
              <div className="bg-black/90 backdrop-blur-sm px-3 py-1.5 rounded-lg text-xs text-white whitespace-nowrap border border-white/10">
                Hero
              </div>
            </div>
          </div>

          <div className="group relative">
            <button
              onClick={scrollToFeatures}
              className={`w-8 h-0.5 transition-all duration-500 ${scrollProgress >= 25 && scrollProgress < 75 ? 'bg-gradient-to-r from-green-400 to-cyan-400 shadow-lg shadow-green-400/30' : 'bg-white/20 hover:bg-white/40'}`}
              title="Features"
            />
            <div className="absolute right-10 top-1/2 transform -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-all duration-300 pointer-events-none">
              <div className="bg-black/90 backdrop-blur-sm px-3 py-1.5 rounded-lg text-xs text-white whitespace-nowrap border border-white/10">
                Features
              </div>
            </div>
          </div>

          <div className="group relative">
            <button
              onClick={() => window.scrollTo({ top: document.documentElement.scrollHeight, behavior: 'smooth' })}
              className={`w-8 h-0.5 transition-all duration-500 ${scrollProgress >= 75 ? 'bg-gradient-to-r from-purple-400 to-pink-400 shadow-lg shadow-purple-400/30' : 'bg-white/20 hover:bg-white/40'}`}
              title="Architecture"
            />
            <div className="absolute right-10 top-1/2 transform -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-all duration-300 pointer-events-none">
              <div className="bg-black/90 backdrop-blur-sm px-3 py-1.5 rounded-lg text-xs text-white whitespace-nowrap border border-white/10">
                Architecture
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Floating Quick Access Button */}
      <div className="fixed bottom-20 right-8 z-40">
        <button
          onClick={() => navigate('/dashboard')}
          className="group relative w-14 h-14 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full shadow-lg shadow-blue-500/25 hover:shadow-xl hover:shadow-blue-500/40 transition-all duration-500 hover:scale-110 flex items-center justify-center"
        >
          <Play className="w-6 h-6 text-white group-hover:scale-110 transition-transform" />
          <div className="absolute -top-12 left-1/2 transform -translate-x-1/2 opacity-0 group-hover:opacity-100 transition-all duration-300 pointer-events-none">
            <div className="bg-black/80 backdrop-blur-sm px-3 py-2 rounded-lg text-sm text-white whitespace-nowrap">
              Launch IDE
            </div>
          </div>
          <div className="absolute inset-0 rounded-full bg-gradient-to-r from-blue-400 to-purple-400 opacity-0 group-hover:opacity-20 transition-opacity duration-300 animate-pulse" />
        </button>
      </div>
      {/* Advanced Animated Background */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        {/* Main gradient overlay */}
        <div className="absolute inset-0 bg-gradient-to-br from-black/90 via-gray-900/80 to-black/90" />

        {/* Animated gradient orbs */}
        <div className="absolute top-1/4 right-1/4 w-96 h-96 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-full blur-3xl animate-pulse" />
        <div className="absolute bottom-1/3 left-1/5 w-80 h-80 bg-gradient-to-r from-purple-500/15 to-pink-500/15 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '2s' }} />
        <div className="absolute top-1/2 left-1/2 w-64 h-64 bg-gradient-to-r from-cyan-500/10 to-blue-500/10 rounded-full blur-2xl animate-pulse" style={{ animationDelay: '4s' }} />

        {/* Floating particles */}
        <div className="absolute inset-0">
          {[...Array(40)].map((_, i) => (
            <div
              key={i}
              className="absolute w-1 h-1 bg-white/20 rounded-full animate-pulse"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                animationDelay: `${Math.random() * 5}s`,
                animationDuration: `${3 + Math.random() * 4}s`
              }}
            />
          ))}
        </div>
      </div>

      {/* Modern Floating Action Button */}
      <div className="fixed top-6 right-6 z-50">
        <button
          onClick={() => navigate('/dashboard')}
          className="group relative px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full text-white font-semibold shadow-lg hover:shadow-2xl hover:shadow-blue-500/25 transition-all duration-500 hover:scale-110"
        >
          <span className="flex items-center space-x-2">
            <span>Launch IDE</span>
            <ArrowRight className="w-4 h-4 transition-transform group-hover:translate-x-1" />
          </span>
          <div className="absolute inset-0 rounded-full bg-gradient-to-r from-blue-400 to-purple-400 opacity-0 group-hover:opacity-20 transition-opacity duration-500"></div>
        </button>
      </div>

      {/* Modern Logo Badge with Personal Branding */}
      <div className="fixed top-6 left-6 z-50">
        <div className="flex items-center space-x-3 px-4 py-2 bg-black/40 backdrop-blur-xl rounded-full border border-white/10 group hover:bg-black/60 transition-all duration-300">
          <div className="w-8 h-8 rounded-lg bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center group-hover:scale-110 transition-transform">
            <Code className="w-5 h-5 text-white" />
          </div>
          <div className="flex items-center space-x-2">
            <span className="text-lg font-bold text-white">CloudIDE</span>
            <span className="text-xs text-gray-400 font-light">by</span>
            <span className="text-sm text-blue-400 font-semibold">owais</span>
          </div>
        </div>
      </div>

      {/* User Avatar and Name - Right Side */}
      <div className="fixed top-6 right-52 z-50">
        <div className="relative group">
          <button className="flex items-center space-x-3 px-4 py-3 bg-black/40 backdrop-blur-xl rounded-full border border-white/10 hover:bg-black/60 transition-all duration-300 h-12">
            <UserAvatar name="Owaiss" size="md" />
            <div className="flex flex-col">
              <span className="text-sm font-semibold text-white">Owaiss</span>
              <span className="text-xs text-gray-400">Developer</span>
            </div>
            <div className="w-4 h-4 text-gray-400 transition-transform group-hover:rotate-180">
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <polyline points="6,9 12,15 18,9"></polyline>
              </svg>
            </div>
          </button>

          {/* Modern Dropdown Menu */}
          <div className="absolute top-full right-0 mt-2 w-48 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 transform translate-y-2 group-hover:translate-y-0">
            <div className="bg-black/80 backdrop-blur-xl rounded-2xl border border-white/10 shadow-2xl shadow-black/50 overflow-hidden">
              {/* Account Option */}
              <button className="w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-white/5 transition-all duration-200 group/item">
                <div className="w-8 h-8 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-lg flex items-center justify-center">
                  <svg className="w-4 h-4 text-blue-400" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                    <circle cx="12" cy="7" r="4"></circle>
                  </svg>
                </div>
                <div className="flex flex-col">
                  <span className="text-sm font-medium text-white group-hover/item:text-blue-400 transition-colors">Account</span>
                  <span className="text-xs text-gray-400">Manage profile</span>
                </div>
              </button>

              {/* Divider */}
              <div className="h-px bg-gradient-to-r from-transparent via-white/10 to-transparent mx-4"></div>

              {/* Logout Option */}
              <button className="w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-red-500/10 transition-all duration-200 group/item">
                <div className="w-8 h-8 bg-gradient-to-r from-red-500/20 to-orange-500/20 rounded-lg flex items-center justify-center">
                  <svg className="w-4 h-4 text-red-400" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path>
                    <polyline points="16,17 21,12 16,7"></polyline>
                    <line x1="21" y1="12" x2="9" y2="12"></line>
                  </svg>
                </div>
                <div className="flex flex-col">
                  <span className="text-sm font-medium text-white group-hover/item:text-red-400 transition-colors">Logout</span>
                  <span className="text-xs text-gray-400">Sign out</span>
                </div>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Hero Section */}
      <main className="relative z-10 min-h-screen flex items-center justify-center px-6">
        <div ref={heroRef} className="max-w-6xl mx-auto text-center relative">

          {/* Floating IDE Technical Components */}
          {/* Code Editor Stats - Top Left */}
          <div
            ref={el => floatingElementsRef.current[0] = el}
            className="absolute -top-20 -left-32 p-5 rounded-2xl bg-black/40 backdrop-blur-xl border border-white/10 shadow-2xl hover:shadow-blue-500/20 transition-all duration-300"
          >
            <div className="flex items-center space-x-3 mb-3">
              <div className="w-8 h-8 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-lg flex items-center justify-center">
                <Code className="w-4 h-4 text-blue-400" />
              </div>
              <span className="text-sm text-gray-300 font-semibold">Code Editor</span>
            </div>
            <div className="space-y-1">
              <div className="text-xs text-gray-400">Languages Supported</div>
              <div className="text-lg font-bold text-blue-400">Multiple</div>
              <div className="text-xs text-gray-500">Syntax highlighting, IntelliSense</div>
            </div>
          </div>

          {/* Performance Metrics - Top Right */}
          <div
            ref={el => floatingElementsRef.current[1] = el}
            className="absolute -top-16 -right-40 p-5 rounded-2xl bg-black/40 backdrop-blur-xl border border-white/10 shadow-2xl hover:shadow-green-500/20 transition-all duration-300"
          >
            <div className="flex items-center space-x-3 mb-3">
              <div className="w-8 h-8 bg-gradient-to-r from-green-500/20 to-emerald-500/20 rounded-lg flex items-center justify-center">
                <Activity className="w-4 h-4 text-green-400" />
              </div>
              <span className="text-sm text-gray-300 font-semibold">Performance</span>
            </div>
            <div className="space-y-1">
              <div className="text-xs text-gray-400">Build Time</div>
              <div className="text-lg font-bold text-green-400">&lt;2s</div>
              <div className="text-xs text-gray-500">Lightning fast compilation</div>
            </div>
          </div>

          {/* Cloud Infrastructure - Bottom Left */}
          <div
            ref={el => floatingElementsRef.current[2] = el}
            className="absolute -bottom-24 -left-40 p-5 rounded-2xl bg-black/40 backdrop-blur-xl border border-white/10 shadow-2xl hover:shadow-purple-500/20 transition-all duration-300"
          >
            <div className="flex items-center space-x-3 mb-3">
              <div className="w-8 h-8 bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-lg flex items-center justify-center">
                <Cloud className="w-4 h-4 text-purple-400" />
              </div>
              <span className="text-sm text-gray-300 font-semibold">Cloud Native</span>
            </div>
            <div className="space-y-1">
              <div className="text-xs text-gray-400">Container Orchestration</div>
              <div className="text-lg font-bold text-purple-400">Docker</div>
              <div className="text-xs text-gray-500">Scalable infrastructure</div>
            </div>
          </div>

          {/* Security & Collaboration - Bottom Right */}
          <div
            ref={el => floatingElementsRef.current[3] = el}
            className="absolute -bottom-20 -right-32 p-5 rounded-2xl bg-black/40 backdrop-blur-xl border border-white/10 shadow-2xl hover:shadow-orange-500/20 transition-all duration-300"
          >
            <div className="flex items-center space-x-3 mb-3">
              <div className="w-8 h-8 bg-gradient-to-r from-orange-500/20 to-red-500/20 rounded-lg flex items-center justify-center">
                <Shield className="w-4 h-4 text-orange-400" />
              </div>
              <span className="text-sm text-gray-300 font-semibold">Security</span>
            </div>
            <div className="space-y-1">
              <div className="text-xs text-gray-400">Enterprise Grade</div>
              <div className="text-lg font-bold text-orange-400">99.9%</div>
              <div className="text-xs text-gray-500">Uptime guarantee</div>
            </div>
          </div>

          {/* Additional floating elements for more visual interest */}
          {/* Git Integration - Middle Left */}
          <div
            ref={el => floatingElementsRef.current[4] = el}
            className="absolute top-1/2 -left-48 p-4 rounded-xl bg-black/30 backdrop-blur-xl border border-white/5 shadow-xl hover:shadow-cyan-500/10 transition-all duration-300"
          >
            <div className="flex items-center space-x-2 mb-2">
              <GitBranch className="w-4 h-4 text-cyan-400" />
              <span className="text-xs text-gray-400 font-medium">Git</span>
            </div>
            <div className="text-xs text-cyan-400 font-bold">Integrated</div>
          </div>

          {/* Terminal Access - Middle Right */}
          <div
            ref={el => floatingElementsRef.current[5] = el}
            className="absolute top-1/2 -right-48 p-4 rounded-xl bg-black/30 backdrop-blur-xl border border-white/5 shadow-xl hover:shadow-yellow-500/10 transition-all duration-300"
          >
            <div className="flex items-center space-x-2 mb-2">
              <Terminal className="w-4 h-4 text-yellow-400" />
              <span className="text-xs text-gray-400 font-medium">Terminal</span>
            </div>
            <div className="text-xs text-yellow-400 font-bold">Full Access</div>
          </div>

          {/* Database Integration - Top Center */}
          <div
            ref={el => floatingElementsRef.current[6] = el}
            className="absolute -top-32 left-1/2 transform -translate-x-1/2 p-3 rounded-lg bg-black/25 backdrop-blur-xl border border-white/5 shadow-lg hover:shadow-indigo-500/10 transition-all duration-300"
          >
            <div className="flex items-center space-x-2">
              <Database className="w-3 h-3 text-indigo-400" />
              <span className="text-xs text-indigo-400 font-bold">DB</span>
            </div>
          </div>

          {/* CPU Performance - Bottom Center */}
          <div
            ref={el => floatingElementsRef.current[7] = el}
            className="absolute -bottom-32 left-1/2 transform -translate-x-1/2 p-3 rounded-lg bg-black/25 backdrop-blur-xl border border-white/5 shadow-lg hover:shadow-red-500/10 transition-all duration-300"
          >
            <div className="flex items-center space-x-2">
              <Cpu className="w-3 h-3 text-red-400" />
              <span className="text-xs text-red-400 font-bold">CPU</span>
            </div>
          </div>

          {/* Play Button Positioned Over Card */}
          <div className="mb-12 flex justify-start">
            <div className="relative ml-16">
              <button className="w-20 h-20 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 flex items-center justify-center hover:bg-white/20 transition-all duration-300 hover:scale-110 shadow-2xl">
                <Play className="w-8 h-8 text-white ml-1" />
              </button>
            </div>
          </div>

          {/* Main Heading */}
          <div className="space-y-8">
            <div className="space-y-4">
              <div className="inline-flex items-center space-x-2 px-4 py-2 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 text-sm text-gray-300 mb-8">
                <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
                <span>Unlock Your Development Potential</span>
                <ArrowRight className="w-4 h-4" />
              </div>

              <h1 className="text-6xl md:text-8xl font-bold leading-tight">
                <span className="block text-white">One-click for</span>
                <span className="block bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 bg-clip-text text-transparent">
                  Cloud <span className="text-gray-400">Development</span>
                </span>
              </h1>
            </div>

            <p className="text-xl md:text-2xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
              Dive into the art of development, where innovative cloud technology meets coding expertise
            </p>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-6 justify-center items-center pt-8">
              <button
                onClick={() => navigate('/dashboard')}
                className="group px-8 py-4 bg-white text-black rounded-full font-semibold text-lg transition-all duration-300 hover:shadow-2xl hover:shadow-white/25 hover:scale-105 relative overflow-hidden"
              >
                <span className="flex items-center gap-3 relative z-10">
                  Open App
                  <ArrowRight className="w-5 h-5 transition-transform group-hover:translate-x-1" />
                </span>
                <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              </button>

              <button className="px-8 py-4 rounded-full border border-white/20 bg-white/10 backdrop-blur-sm text-white font-semibold text-lg transition-all duration-300 hover:bg-white/20 hover:border-white/30 hover:shadow-lg hover:shadow-white/10">
                Discover More
              </button>
            </div>
          </div>

          {/* Enhanced Minimal Scroll Indicator - Bottom left */}
          <div className="absolute bottom-12 left-12 z-20">
            <button
              onClick={scrollToFeatures}
              className="group flex flex-col items-center space-y-3 p-5 rounded-2xl bg-black/20 backdrop-blur-xl border border-white/10 hover:bg-black/30 transition-all duration-500 hover:scale-105 cursor-pointer"
            >
              <div className="flex flex-col space-y-1">
                <div className="w-6 h-0.5 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full transition-all duration-300 group-hover:w-8" />
                <div className="w-4 h-0.5 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full transition-all duration-300 group-hover:w-6" style={{animationDelay: '150ms'}} />
                <div className="w-2 h-0.5 bg-gradient-to-r from-pink-400 to-blue-400 rounded-full transition-all duration-300 group-hover:w-4" style={{animationDelay: '300ms'}} />
              </div>
              <span className="text-xs text-white/60 group-hover:text-white/90 transition-colors font-medium">scroll</span>
            </button>
          </div>
        </div>
      </main>

      {/* IDE Features Showcase Section */}
      <section ref={featuresRef} className="relative z-10 py-20 px-6 scroll-animate" data-delay="200">
        <div className="max-w-7xl mx-auto">
          {/* Section Header */}
          <div className="text-center mb-20">
            <div className="inline-flex items-center space-x-2 px-4 py-2 rounded-full bg-blue-500/10 backdrop-blur-sm border border-blue-500/20 text-sm text-blue-400 mb-6">
              <Code className="w-4 h-4" />
              <span>Full-Featured Cloud IDE</span>
            </div>
            <h2 className="text-4xl md:text-6xl font-bold text-white mb-6">
              Everything You Need to <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">Code</span>
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              A complete development environment in your browser with advanced features, containerized workspaces, and seamless collaboration
            </p>
          </div>

          {/* Core IDE Features Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-20 scroll-animate" data-delay="300">
            {/* Monaco Editor */}
            <div className="group p-8 rounded-2xl bg-black/40 backdrop-blur-xl border border-white/10 hover:border-blue-500/30 transition-all duration-500 hover:scale-105 hover:shadow-2xl hover:shadow-blue-500/10">
              <div className="w-14 h-14 bg-gradient-to-r from-blue-500/20 to-cyan-500/20 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform">
                <Code className="w-7 h-7 text-blue-400" />
              </div>
              <h3 className="text-xl font-semibold text-white mb-3">Monaco Editor</h3>
              <p className="text-gray-400 mb-4">VS Code-powered editor with intelligent IntelliSense, syntax highlighting, and multi-language support</p>
              <div className="flex flex-wrap gap-2">
                <span className="px-2 py-1 text-xs bg-blue-500/20 text-blue-400 rounded">JavaScript</span>
                <span className="px-2 py-1 text-xs bg-green-500/20 text-green-400 rounded">Python</span>
                <span className="px-2 py-1 text-xs bg-orange-500/20 text-orange-400 rounded">Rust</span>
                <span className="px-2 py-1 text-xs bg-purple-500/20 text-purple-400 rounded">Go</span>
              </div>
            </div>

            {/* Split Editor */}
            <div className="group p-8 rounded-2xl bg-black/40 backdrop-blur-xl border border-white/10 hover:border-green-500/30 transition-all duration-500 hover:scale-105 hover:shadow-2xl hover:shadow-green-500/10">
              <div className="w-14 h-14 bg-gradient-to-r from-green-500/20 to-emerald-500/20 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform">
                <div className="grid grid-cols-2 gap-1 w-6 h-6">
                  <div className="bg-green-400 rounded-sm"></div>
                  <div className="bg-green-400 rounded-sm"></div>
                  <div className="bg-green-400 rounded-sm"></div>
                  <div className="bg-green-400 rounded-sm"></div>
                </div>
              </div>
              <h3 className="text-xl font-semibold text-white mb-3">Split Editor</h3>
              <p className="text-gray-400 mb-4">Work with multiple files simultaneously using resizable split panels and editor groups</p>
              <div className="flex items-center space-x-2 text-sm text-gray-500">
                <Keyboard className="w-4 h-4" />
                <span>Ctrl+\ to split</span>
              </div>
            </div>

            {/* Zen Mode */}
            <div className="group p-8 rounded-2xl bg-black/40 backdrop-blur-xl border border-white/10 hover:border-purple-500/30 transition-all duration-500 hover:scale-105 hover:shadow-2xl hover:shadow-purple-500/10">
              <div className="w-14 h-14 bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform">
                <Minimize2 className="w-7 h-7 text-purple-400" />
              </div>
              <h3 className="text-xl font-semibold text-white mb-3">Zen Mode</h3>
              <p className="text-gray-400 mb-4">Distraction-free coding with immersive full-screen editor and smooth GSAP animations</p>
              <div className="flex items-center space-x-2 text-sm text-gray-500">
                <span className="w-2 h-2 bg-purple-400 rounded-full animate-pulse"></span>
                <span>Focus mode enabled</span>
              </div>
            </div>
          </div>

          {/* Terminal & Development Tools */}
          <div className="mb-20 scroll-animate" data-delay="400">
            <h3 className="text-3xl font-bold text-white mb-12 text-center">
              Integrated <span className="bg-gradient-to-r from-green-400 to-cyan-400 bg-clip-text text-transparent">Development Tools</span>
            </h3>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* XTerm Terminal */}
              <div className="group p-8 rounded-2xl bg-black/40 backdrop-blur-xl border border-white/10 hover:border-green-500/30 transition-all duration-500">
                <div className="flex items-center space-x-4 mb-6">
                  <div className="w-12 h-12 bg-gradient-to-r from-green-500/20 to-cyan-500/20 rounded-xl flex items-center justify-center">
                    <Terminal className="w-6 h-6 text-green-400" />
                  </div>
                  <div>
                    <h4 className="text-xl font-semibold text-white">XTerm.js Terminal</h4>
                    <p className="text-gray-400">Full-featured terminal with theme support</p>
                  </div>
                </div>
                <div className="bg-black/60 rounded-lg p-4 font-mono text-sm">
                  <div className="flex items-center space-x-2 mb-2">
                    <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                    <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                    <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  </div>
                  <div className="text-green-400">
                    <span className="text-blue-400">user@cloudide</span>
                    <span className="text-white">:</span>
                    <span className="text-cyan-400">~/project</span>
                    <span className="text-white">$ </span>
                    <span className="animate-pulse">|</span>
                  </div>
                </div>
                <div className="mt-4 flex flex-wrap gap-2">
                  <span className="px-2 py-1 text-xs bg-green-500/20 text-green-400 rounded">Multi-session</span>
                  <span className="px-2 py-1 text-xs bg-cyan-500/20 text-cyan-400 rounded">Theme aware</span>
                  <span className="px-2 py-1 text-xs bg-blue-500/20 text-blue-400 rounded">Resizable</span>
                </div>
              </div>

              {/* File Explorer */}
              <div className="group p-8 rounded-2xl bg-black/40 backdrop-blur-xl border border-white/10 hover:border-orange-500/30 transition-all duration-500">
                <div className="flex items-center space-x-4 mb-6">
                  <div className="w-12 h-12 bg-gradient-to-r from-orange-500/20 to-red-500/20 rounded-xl flex items-center justify-center">
                    <FolderOpen className="w-6 h-6 text-orange-400" />
                  </div>
                  <div>
                    <h4 className="text-xl font-semibold text-white">File Explorer</h4>
                    <p className="text-gray-400">Tree-based file navigation with context menus</p>
                  </div>
                </div>
                <div className="bg-black/60 rounded-lg p-4">
                  <div className="space-y-2 text-sm">
                    <div className="flex items-center space-x-2">
                      <FolderOpen className="w-4 h-4 text-blue-400" />
                      <span className="text-white">src/</span>
                    </div>
                    <div className="flex items-center space-x-2 ml-4">
                      <FileText className="w-4 h-4 text-green-400" />
                      <span className="text-gray-300">index.js</span>
                    </div>
                    <div className="flex items-center space-x-2 ml-4">
                      <FileText className="w-4 h-4 text-blue-400" />
                      <span className="text-gray-300">App.jsx</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <FileText className="w-4 h-4 text-yellow-400" />
                      <span className="text-gray-300">package.json</span>
                    </div>
                  </div>
                </div>
                <div className="mt-4 flex flex-wrap gap-2">
                  <span className="px-2 py-1 text-xs bg-orange-500/20 text-orange-400 rounded">Context menus</span>
                  <span className="px-2 py-1 text-xs bg-red-500/20 text-red-400 rounded">File operations</span>
                </div>
              </div>
            </div>
          </div>

          {/* Infrastructure Features */}
          <div className="mb-20">
            <h3 className="text-3xl font-bold text-white mb-12 text-center">
              Cloud <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">Infrastructure</span>
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {/* Docker Containers */}
              <div className="group p-6 rounded-2xl bg-black/40 backdrop-blur-xl border border-white/10 hover:border-blue-500/30 transition-all duration-500 hover:scale-105">
                <div className="w-12 h-12 bg-gradient-to-r from-blue-500/20 to-cyan-500/20 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform">
                  <div className="w-6 h-6 bg-blue-400 rounded-sm"></div>
                </div>
                <h4 className="text-lg font-semibold text-white mb-2">Docker Containers</h4>
                <p className="text-gray-400 text-sm mb-3">Isolated development environments with custom images</p>
                <div className="text-xs text-blue-400">Per-user isolation</div>
              </div>

              {/* Custom DNS */}
              <div className="group p-6 rounded-2xl bg-black/40 backdrop-blur-xl border border-white/10 hover:border-green-500/30 transition-all duration-500 hover:scale-105">
                <div className="w-12 h-12 bg-gradient-to-r from-green-500/20 to-emerald-500/20 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform">
                  <Globe className="w-6 h-6 text-green-400" />
                </div>
                <h4 className="text-lg font-semibold text-white mb-2">Custom DNS</h4>
                <p className="text-gray-400 text-sm mb-3">Dynamic subdomain routing with Redis persistence</p>
                <div className="text-xs text-green-400">user.ide.local</div>
              </div>

              {/* Reverse Proxy */}
              <div className="group p-6 rounded-2xl bg-black/40 backdrop-blur-xl border border-white/10 hover:border-purple-500/30 transition-all duration-500 hover:scale-105">
                <div className="w-12 h-12 bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform">
                  <ArrowRight className="w-6 h-6 text-purple-400" />
                </div>
                <h4 className="text-lg font-semibold text-white mb-2">Nginx Proxy</h4>
                <p className="text-gray-400 text-sm mb-3">Automatic routing and load balancing</p>
                <div className="text-xs text-purple-400">Auto-configured</div>
              </div>

              {/* Monitoring */}
              <div className="group p-6 rounded-2xl bg-black/40 backdrop-blur-xl border border-white/10 hover:border-orange-500/30 transition-all duration-500 hover:scale-105">
                <div className="w-12 h-12 bg-gradient-to-r from-orange-500/20 to-red-500/20 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform">
                  <Activity className="w-6 h-6 text-orange-400" />
                </div>
                <h4 className="text-lg font-semibold text-white mb-2">System Monitor</h4>
                <p className="text-gray-400 text-sm mb-3">Real-time resource monitoring and health checks</p>
                <div className="text-xs text-orange-400">Live metrics</div>
              </div>
            </div>
          </div>
          {/* Interactive Demo Section */}
          <div className="mb-20">
            <h3 className="text-3xl font-bold text-white mb-12 text-center">
              Try It <span className="bg-gradient-to-r from-cyan-400 to-blue-400 bg-clip-text text-transparent">Live</span>
            </h3>
            <div className="max-w-4xl mx-auto">
              <div className="group p-8 rounded-2xl bg-black/40 backdrop-blur-xl border border-white/10 hover:border-cyan-500/30 transition-all duration-500">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 bg-gradient-to-r from-cyan-500/20 to-blue-500/20 rounded-xl flex items-center justify-center">
                      <Play className="w-6 h-6 text-cyan-400" />
                    </div>
                    <div>
                      <h4 className="text-xl font-semibold text-white">Interactive IDE Demo</h4>
                      <p className="text-gray-400">Experience the full CloudIDE in action</p>
                    </div>
                  </div>
                  <button
                    onClick={() => navigate('/dashboard')}
                    className="px-6 py-3 bg-gradient-to-r from-cyan-500 to-blue-500 rounded-lg text-white font-semibold hover:shadow-lg hover:shadow-cyan-500/25 transition-all duration-300 hover:scale-105"
                  >
                    Launch Demo
                  </button>
                </div>

                {/* Mock IDE Interface */}
                <div className="bg-black/60 rounded-lg overflow-hidden border border-white/10">
                  {/* IDE Header */}
                  <div className="flex items-center justify-between px-4 py-2 bg-black/40 border-b border-white/10">
                    <div className="flex items-center space-x-2">
                      <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                      <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                      <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                    </div>
                    <div className="text-sm text-gray-400">CloudIDE - main.js</div>
                    <div className="flex items-center space-x-2">
                      <Minimize2 className="w-4 h-4 text-gray-400" />
                      <Settings className="w-4 h-4 text-gray-400" />
                    </div>
                  </div>

                  {/* IDE Content */}
                  <div className="flex">
                    {/* File Explorer */}
                    <div className="w-48 bg-black/20 border-r border-white/10 p-3">
                      <div className="text-xs text-gray-400 mb-2">EXPLORER</div>
                      <div className="space-y-1 text-sm">
                        <div className="flex items-center space-x-2 text-blue-400">
                          <FolderOpen className="w-4 h-4" />
                          <span>src</span>
                        </div>
                        <div className="flex items-center space-x-2 ml-4 text-green-400">
                          <FileText className="w-4 h-4" />
                          <span>main.js</span>
                        </div>
                        <div className="flex items-center space-x-2 ml-4 text-gray-400">
                          <FileText className="w-4 h-4" />
                          <span>utils.js</span>
                        </div>
                      </div>
                    </div>

                    {/* Code Editor */}
                    <div className="flex-1 p-4 font-mono text-sm">
                      <div className="space-y-1">
                        <div><span className="text-purple-400">const</span> <span className="text-blue-400">express</span> <span className="text-white">=</span> <span className="text-green-400">require</span><span className="text-white">(</span><span className="text-orange-400">'express'</span><span className="text-white">)</span></div>
                        <div><span className="text-purple-400">const</span> <span className="text-blue-400">app</span> <span className="text-white">=</span> <span className="text-blue-400">express</span><span className="text-white">()</span></div>
                        <div className="text-gray-500"></div>
                        <div><span className="text-blue-400">app</span><span className="text-white">.</span><span className="text-yellow-400">get</span><span className="text-white">(</span><span className="text-orange-400">'/'</span><span className="text-white">, (</span><span className="text-blue-400">req</span><span className="text-white">, </span><span className="text-blue-400">res</span><span className="text-white">) =&gt; {'{'}</span></div>
                        <div className="ml-4"><span className="text-blue-400">res</span><span className="text-white">.</span><span className="text-yellow-400">send</span><span className="text-white">(</span><span className="text-orange-400">'Hello CloudIDE!'</span><span className="text-white">)</span></div>
                        <div><span className="text-white">{'}'}</span></div>
                        <div className="text-gray-500"></div>
                        <div><span className="text-blue-400">app</span><span className="text-white">.</span><span className="text-yellow-400">listen</span><span className="text-white">(</span><span className="text-cyan-400">3000</span><span className="text-white">, () =&gt; {'{'}</span></div>
                        <div className="ml-4"><span className="text-blue-400">console</span><span className="text-white">.</span><span className="text-yellow-400">log</span><span className="text-white">(</span><span className="text-orange-400">'Server running!'</span><span className="text-white">)</span></div>
                        <div><span className="text-white">{'}'}</span></div>
                      </div>
                    </div>
                  </div>

                  {/* Terminal */}
                  <div className="border-t border-white/10 bg-black/40 p-3">
                    <div className="text-xs text-gray-400 mb-2">TERMINAL</div>
                    <div className="font-mono text-sm">
                      <div className="text-green-400">
                        <span className="text-blue-400">user@cloudide</span>
                        <span className="text-white">:</span>
                        <span className="text-cyan-400">~/project</span>
                        <span className="text-white">$ npm start</span>
                      </div>
                      <div className="text-gray-300">Server running on port 3000</div>
                      <div className="text-green-400">
                        <span className="text-blue-400">user@cloudide</span>
                        <span className="text-white">:</span>
                        <span className="text-cyan-400">~/project</span>
                        <span className="text-white">$ </span>
                        <span className="animate-pulse">|</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="mt-6 flex flex-wrap gap-3">
                  <span className="px-3 py-1 text-sm bg-cyan-500/20 text-cyan-400 rounded-full">Monaco Editor</span>
                  <span className="px-3 py-1 text-sm bg-green-500/20 text-green-400 rounded-full">XTerm Terminal</span>
                  <span className="px-3 py-1 text-sm bg-blue-500/20 text-blue-400 rounded-full">File Explorer</span>
                  <span className="px-3 py-1 text-sm bg-purple-500/20 text-purple-400 rounded-full">Split View</span>
                </div>
              </div>
            </div>
          </div>

          {/* Rotating Tech Strip */}
          <div className="my-20">
            <TechStrip />
          </div>

          {/* Technical Specifications */}
          <div className="mb-20 scroll-animate" data-delay="500">
            <h3 className="text-3xl font-bold text-white mb-12 text-center">
              Technical <span className="bg-gradient-to-r from-yellow-400 to-orange-400 bg-clip-text text-transparent">Specifications</span>
            </h3>

            {/* Performance Metrics */}
            <div className="max-w-4xl mx-auto mb-16">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div className="text-center p-6 rounded-2xl bg-black/30 backdrop-blur-sm border border-white/10">
                  <div className="text-3xl font-bold text-blue-400 mb-2">&lt; 100ms</div>
                  <div className="text-sm text-gray-400">Editor Response Time</div>
                </div>
                <div className="text-center p-6 rounded-2xl bg-black/30 backdrop-blur-sm border border-white/10">
                  <div className="text-3xl font-bold text-green-400 mb-2">99.9%</div>
                  <div className="text-sm text-gray-400">Container Uptime</div>
                </div>
                <div className="text-center p-6 rounded-2xl bg-black/30 backdrop-blur-sm border border-white/10">
                  <div className="text-3xl font-bold text-purple-400 mb-2">∞</div>
                  <div className="text-sm text-gray-400">Concurrent Users</div>
                </div>
                <div className="text-center p-6 rounded-2xl bg-black/30 backdrop-blur-sm border border-white/10">
                  <div className="text-3xl font-bold text-orange-400 mb-2">50+</div>
                  <div className="text-sm text-gray-400">Language Support</div>
                </div>
              </div>
            </div>

            {/* Detailed Feature Breakdown */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-16">
              {/* Development Environment */}
              <div className="space-y-6">
                <div className="flex items-center space-x-3 mb-6">
                  <div className="w-10 h-10 bg-gradient-to-r from-blue-500/20 to-cyan-500/20 rounded-lg flex items-center justify-center">
                    <Code className="w-5 h-5 text-blue-400" />
                  </div>
                  <h4 className="text-2xl font-semibold text-white">Development Environment</h4>
                </div>

                <div className="space-y-4">
                  <div className="p-4 rounded-xl bg-black/30 backdrop-blur-sm border border-white/10">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-white font-medium">Monaco Editor Integration</span>
                      <span className="text-green-400 text-sm">✓ Active</span>
                    </div>
                    <p className="text-gray-400 text-sm">Full VS Code editor experience with IntelliSense, syntax highlighting, and multi-cursor support</p>
                  </div>

                  <div className="p-4 rounded-xl bg-black/30 backdrop-blur-sm border border-white/10">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-white font-medium">XTerm.js Terminal</span>
                      <span className="text-green-400 text-sm">✓ Active</span>
                    </div>
                    <p className="text-gray-400 text-sm">Full-featured terminal with theme support, multi-session management, and real-time command execution</p>
                  </div>

                  <div className="p-4 rounded-xl bg-black/30 backdrop-blur-sm border border-white/10">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-white font-medium">File System Operations</span>
                      <span className="text-green-400 text-sm">✓ Active</span>
                    </div>
                    <p className="text-gray-400 text-sm">Complete file management with tree navigation, context menus, and real-time file operations</p>
                  </div>
                </div>
              </div>

              {/* Infrastructure & Deployment */}
              <div className="space-y-6">
                <div className="flex items-center space-x-3 mb-6">
                  <div className="w-10 h-10 bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-lg flex items-center justify-center">
                    <Cloud className="w-5 h-5 text-purple-400" />
                  </div>
                  <h4 className="text-2xl font-semibold text-white">Cloud Infrastructure</h4>
                </div>

                <div className="space-y-4">
                  <div className="p-4 rounded-xl bg-black/30 backdrop-blur-sm border border-white/10">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-white font-medium">Docker Containerization</span>
                      <span className="text-blue-400 text-sm">Per-User</span>
                    </div>
                    <p className="text-gray-400 text-sm">Isolated development environments with custom Docker images and volume persistence</p>
                  </div>

                  <div className="p-4 rounded-xl bg-black/30 backdrop-blur-sm border border-white/10">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-white font-medium">Custom DNS Server</span>
                      <span className="text-green-400 text-sm">Redis-Backed</span>
                    </div>
                    <p className="text-gray-400 text-sm">Dynamic subdomain routing with Redis persistence for seamless project access</p>
                  </div>

                  <div className="p-4 rounded-xl bg-black/30 backdrop-blur-sm border border-white/10">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-white font-medium">Nginx Reverse Proxy</span>
                      <span className="text-purple-400 text-sm">Auto-Config</span>
                    </div>
                    <p className="text-gray-400 text-sm">Automatic routing, load balancing, and SSL termination for all containerized applications</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Project Architecture */}
          <div className="mb-20 scroll-animate" data-delay="600">
            <h3 className="text-3xl font-bold text-white mb-12 text-center">
              Project <span className="bg-gradient-to-r from-cyan-400 to-blue-400 bg-clip-text text-transparent">Architecture</span>
            </h3>

            {/* Architecture Components */}
            <div className="max-w-6xl mx-auto mb-16">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                    {/* Frontend */}
                    <div className="group p-6 rounded-2xl bg-black/40 backdrop-blur-xl border border-white/10 hover:border-blue-500/30 transition-all duration-500">
                      <div className="text-center">
                    <div className="w-16 h-16 bg-gradient-to-r from-blue-500/20 to-cyan-500/20 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform">
                      <Code className="w-8 h-8 text-blue-400" />
                    </div>
                    <h4 className="text-xl font-semibold text-white mb-3">Frontend</h4>
                    <p className="text-gray-400 mb-4">React-based IDE interface with Monaco Editor and XTerm.js</p>
                    <div className="space-y-2 text-sm">
                      <div className="flex items-center justify-between">
                        <span className="text-gray-400">React</span>
                        <span className="text-blue-400">18.x</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-gray-400">Monaco Editor</span>
                        <span className="text-green-400">Latest</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-gray-400">XTerm.js</span>
                        <span className="text-cyan-400">5.x</span>
                      </div>
                    </div>
                      </div>
                    </div>

                    {/* Backend */}
                    <div className="group p-6 rounded-2xl bg-black/40 backdrop-blur-xl border border-white/10 hover:border-green-500/30 transition-all duration-500">
                      <div className="text-center">
                    <div className="w-16 h-16 bg-gradient-to-r from-green-500/20 to-emerald-500/20 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform">
                      <Database className="w-8 h-8 text-green-400" />
                    </div>
                    <h4 className="text-xl font-semibold text-white mb-3">Backend</h4>
                    <p className="text-gray-400 mb-4">Node.js API with container management and real-time features</p>
                    <div className="space-y-2 text-sm">
                      <div className="flex items-center justify-between">
                        <span className="text-gray-400">Node.js</span>
                        <span className="text-green-400">18.x</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-gray-400">Express</span>
                        <span className="text-blue-400">4.x</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-gray-400">Socket.IO</span>
                        <span className="text-purple-400">4.x</span>
                      </div>
                    </div>
                      </div>
                    </div>

                    {/* Infrastructure */}
                    <div className="group p-6 rounded-2xl bg-black/40 backdrop-blur-xl border border-white/10 hover:border-purple-500/30 transition-all duration-500">
                      <div className="text-center">
                    <div className="w-16 h-16 bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform">
                      <Cloud className="w-8 h-8 text-purple-400" />
                    </div>
                    <h4 className="text-xl font-semibold text-white mb-3">Infrastructure</h4>
                    <p className="text-gray-400 mb-4">Docker containers with custom DNS and reverse proxy</p>
                    <div className="space-y-2 text-sm">
                      <div className="flex items-center justify-between">
                        <span className="text-gray-400">Docker</span>
                        <span className="text-blue-400">Latest</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-gray-400">Nginx</span>
                        <span className="text-green-400">1.24</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-gray-400">Redis</span>
                        <span className="text-red-400">7.x</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>



      {/* Modern Footer */}
      <footer className="relative z-10 py-16 px-6 bg-black/40 backdrop-blur-xl border-t border-white/10">
        <div className="max-w-7xl mx-auto">
          {/* Main Footer Content */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-12 mb-12">
            {/* Brand Section */}
            <div className="space-y-4">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 rounded-xl bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center">
                  <Code className="w-7 h-7 text-white" />
                </div>
                <div>
                  <h3 className="text-2xl font-bold text-white">CloudIDE</h3>
                  <p className="text-sm text-blue-400 font-medium">by owais</p>
                </div>
              </div>
              <p className="text-gray-400 leading-relaxed">
                Next-generation cloud development platform with containerized environments,
                real-time collaboration, and enterprise-grade infrastructure.
              </p>
            </div>

            {/* Tech Stack */}
            <div className="space-y-4">
              <h4 className="text-lg font-semibold text-white">Built With</h4>
              <div className="grid grid-cols-2 gap-2">
                {['React', 'Node.js', 'Docker', 'Nginx', 'Redis', 'MongoDB', 'GSAP', 'Monaco'].map((tech) => (
                  <div key={tech} className="flex items-center space-x-2 group">
                    <div className="w-2 h-2 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full group-hover:scale-125 transition-transform" />
                    <span className="text-sm text-gray-400 group-hover:text-white transition-colors">{tech}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Quick Actions */}
            <div className="space-y-4">
              <h4 className="text-lg font-semibold text-white">Get Started</h4>
              <div className="space-y-3">
                <button
                  onClick={() => navigate('/dashboard')}
                  className="w-full px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl text-white font-semibold hover:shadow-lg hover:shadow-blue-500/25 transition-all duration-300 hover:scale-105"
                >
                  Launch CloudIDE
                </button>
                <button
                  onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
                  className="w-full px-6 py-3 border border-white/20 rounded-xl text-gray-300 hover:text-white hover:border-white/40 transition-all duration-300"
                >
                  Back to Top
                </button>
              </div>
            </div>
          </div>

          {/* Bottom Section */}
          <div className="pt-8 border-t border-white/10">
            <div className="flex flex-col md:flex-row items-center justify-between space-y-4 md:space-y-0">
              {/* Copyright */}
              <p className="text-sm text-gray-500">
                © 2024 CloudIDE. Empowering developers with cloud-native tools.
              </p>

              {/* Personal Branding */}
              <div className="flex items-center space-x-2 text-gray-400">
                <span className="text-sm">crafted with</span>
                <span className="text-red-400 text-lg animate-pulse">❤️</span>
                <span className="text-sm">by</span>
                <span className="text-white font-semibold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">owais</span>
              </div>

              {/* Version */}
              <div className="text-xs text-gray-600">
                v1.0.0
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}

export default Landing
