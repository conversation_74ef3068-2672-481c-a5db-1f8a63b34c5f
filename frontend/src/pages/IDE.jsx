import React, { useState, useEffect, useRef } from 'react'
import { useParams } from 'react-router-dom'
import { Panel, PanelGroup, PanelResizeHandle } from 'react-resizable-panels'
import { Header, Sidebar, StatusBar } from '../components/Layout'
import { MonacoEditor, EditorTabs, SplitEditor } from '../components/Editor'
import { XTerminal, TerminalTabs } from '../components/Terminal'
import { WelcomeScreen } from '../components/IDE'
import { ZenMode } from '../components/ZenMode'
import { useAppStore } from '../stores/appStore'
import { fileService, systemService, projectService } from '../services/api'
import { applyTheme } from '../config/themes'
import { clsx } from 'clsx'

const IDE = () => {
  const { projectId } = useParams()
  const terminalRef = useRef(null)
  const {
    layout,
    updateLayout,
    currentFile,
    setCurrentFile,
    addOpenFile,
    openFiles,
    terminalSessions,
    addTerminalSession,
    activeTerminalId,
    updateSystemStatus,
    addNotification,
    currentProject,
    setCurrentProject,
    theme,
    setTheme,
    settings
  } = useAppStore()

  const [editorContent, setEditorContent] = useState('')
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    initializeIDE()
  }, [projectId])

  useEffect(() => {
    // Apply theme on component mount
    applyTheme(theme)
  }, [theme])

  // Load file content when currentFile changes (for tab switching)
  useEffect(() => {
    const loadFileContent = async () => {
      if (currentFile && currentFile.type === 'file') {
        try {
          const projectId = currentProject?.id || 'default'
          const response = await fileService.readFile(currentFile.path, projectId)
          setEditorContent(response.content || '')
        } catch (error) {
          console.error('Failed to load file content:', error)
          // Mock content for development
          setEditorContent(`// ${currentFile.name}\n// File content will be loaded here\n\nconsole.log('Hello from ${currentFile.name}');`)
        }
      }
    }

    loadFileContent()
  }, [currentFile?.path, currentProject?.id])

  useEffect(() => {
    // Load system status periodically
    const interval = setInterval(async () => {
      try {
        const health = await systemService.getSystemHealth()
        updateSystemStatus(health)
      } catch (error) {
        console.error('Failed to update system status:', error)
      }
    }, 30000) // Every 30 seconds

    return () => clearInterval(interval)
  }, [])

  const initializeIDE = async () => {
    setLoading(true)
    try {
      // Load project data if projectId is provided
      if (projectId) {
        try {
          const project = await projectService.getProject(projectId)
          setCurrentProject(project)
          console.log('Loaded project:', project)
        } catch (error) {
          console.error('Failed to load project:', error)
          // Set a default project for development
          setCurrentProject({ id: projectId, name: `Project ${projectId}` })
        }
      } else {
        // Set default project
        setCurrentProject({ id: 'default', name: 'Default Project' })
      }

      // Load initial system status
      const health = await systemService.getSystemHealth()
      updateSystemStatus(health)

      // Create initial terminal session if none exists
      if (terminalSessions.length === 0) {
        createNewTerminal()
      }

      addNotification({
        type: 'success',
        message: 'IDE initialized successfully',
      })
    } catch (error) {
      console.error('Failed to initialize IDE:', error)
      addNotification({
        type: 'error',
        message: 'Failed to initialize IDE',
      })
    } finally {
      setLoading(false)
    }
  }

  const handleFileSelect = async (file) => {
    try {
      // Add to current editor group
      const { addFileToGroup } = useAppStore.getState()
      addFileToGroup(file)
    } catch (error) {
      console.error('Failed to select file:', error)
      addNotification({
        type: 'error',
        message: `Failed to open ${file.name}`,
      })
    }
  }

  const handleEditorChange = (newContent) => {
    setEditorContent(newContent)
    // TODO: Implement auto-save or mark file as modified
  }

  const handleSaveFile = async () => {
    if (!currentFile || currentFile.type !== 'file') return

    try {
      const projectId = currentProject?.id || 'default'
      await fileService.writeFile(currentFile.path, editorContent, projectId)

      addNotification({
        type: 'success',
        message: `Saved ${currentFile.name}`,
      })
    } catch (error) {
      console.error('Failed to save file:', error)
      addNotification({
        type: 'error',
        message: `Failed to save ${currentFile.name}`,
      })
    }
  }

  const createNewTerminal = () => {
    const newSession = {
      id: Date.now().toString(),
      name: `Terminal ${terminalSessions.length + 1}`,
      created: new Date(),
    }
    addTerminalSession(newSession)
  }

  const handleTerminalData = (data) => {
    // TODO: Send data to backend terminal service
    console.log('Terminal data:', data)
    
    // Mock terminal response for development
    if (terminalRef.current) {
      setTimeout(() => {
        if (data.includes('\r')) {
          terminalRef.current.writeData('\r\n$ ')
        }
      }, 100)
    }
  }

  const getLanguageFromFileName = (fileName) => {
    if (!fileName) return 'plaintext'
    
    const ext = fileName.split('.').pop()?.toLowerCase()
    const languageMap = {
      js: 'javascript',
      jsx: 'javascript',
      ts: 'typescript',
      tsx: 'typescript',
      py: 'python',
      java: 'java',
      cpp: 'cpp',
      c: 'c',
      cs: 'csharp',
      php: 'php',
      rb: 'ruby',
      go: 'go',
      rs: 'rust',
      html: 'html',
      css: 'css',
      scss: 'scss',
      sass: 'sass',
      json: 'json',
      xml: 'xml',
      yaml: 'yaml',
      yml: 'yaml',
      md: 'markdown',
      sql: 'sql',
      sh: 'shell',
      bash: 'shell',
    }
    
    return languageMap[ext] || 'plaintext'
  }

  if (loading) {
    return (
      <div className="h-screen bg-ide-bg flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin w-8 h-8 border-2 border-ide-accent border-t-transparent rounded-full mx-auto mb-4"></div>
          <p className="text-ide-text-muted">Loading IDE...</p>
        </div>
      </div>
    )
  }

  return (
    <ZenMode>
      <div className={clsx(
        "h-screen bg-ide-bg flex flex-col",
        settings.ui.zenMode && "zen-mode-active"
      )}>
        <Header />

        <div className="flex-1 overflow-hidden">
          <PanelGroup direction="horizontal" className="panel-group">
          {/* Sidebar Panel */}
          {layout.showSidebar && layout.showSidebarContent && (
            <>
              <Panel
                defaultSize={20}
                minSize={15}
                maxSize={40}
                onResize={(size) => {
                  // Convert percentage to pixels (approximate)
                  const newWidth = Math.round((size / 100) * window.innerWidth)
                  updateLayout({ sidebarWidth: Math.max(200, Math.min(600, newWidth)) })
                }}
              >
                <Sidebar onFileSelect={handleFileSelect} />
              </Panel>
              <PanelResizeHandle className="modern-resize-handle" />
            </>
          )}

          {/* Sidebar Navigation Only (when content is hidden) */}
          {layout.showSidebar && !layout.showSidebarContent && (
            <>
              <Panel defaultSize={4} minSize={4} maxSize={4}>
                <Sidebar onFileSelect={handleFileSelect} />
              </Panel>
              <PanelResizeHandle className="modern-resize-handle" />
            </>
          )}

          {/* Main Content Panel */}
          <Panel minSize={40}>
            <div className="flex flex-col h-full">
              <PanelGroup direction="vertical" className="panel-group">
                {/* Editor Panel */}
                <Panel defaultSize={layout.showTerminal ? 70 : 100} minSize={30}>
                  <div className="h-full m-2">
                    <SplitEditor
                      onFileSelect={handleFileSelect}
                      getLanguageFromFileName={getLanguageFromFileName}
                      currentProject={currentProject}
                    />
                  </div>
                </Panel>

                {/* Terminal Panel */}
                {layout.showTerminal && (
                  <>
                    <PanelResizeHandle className="modern-resize-handle-vertical" />
                    <Panel defaultSize={30} minSize={15}>
                      <div className="flex flex-col h-full ide-terminal-panel m-2 mt-1">
                        <TerminalTabs onNewTerminal={createNewTerminal} />
                        <div className="flex-1 overflow-hidden rounded-b-lg">
                          {activeTerminalId && (
                            <XTerminal
                              ref={terminalRef}
                              projectId={currentProject?.id || projectId}
                              onData={handleTerminalData}
                              onSessionCreated={(session) => {
                                console.log('Terminal session created in IDE:', session)
                              }}
                              className="h-full rounded-b-lg"
                            />
                          )}
                        </div>
                      </div>
                    </Panel>
                  </>
                )}
              </PanelGroup>
            </div>
          </Panel>
        </PanelGroup>
      </div>

        <StatusBar />
      </div>
    </ZenMode>
  )
}

export default IDE
