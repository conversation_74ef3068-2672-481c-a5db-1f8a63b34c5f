import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  server: {
    port: 5173,
    host: true,
    proxy: {
      '/api': {
        target: 'http://localhost:3000',
        changeOrigin: true,
        secure: false,
        rewrite: (path) => {
          // Handle health check endpoint specifically
          if (path === '/api/health') {
            console.log('Rewriting /api/health -> /health')
            return '/health'
          }
          // Keep other API endpoints as-is
          console.log(`Keeping API path: ${path}`)
          return path
        },
        configure: (proxy, options) => {
          proxy.on('error', (err, req, res) => {
            console.log('Proxy error for /api:', err.message)
          })
          proxy.on('proxyReq', (proxyReq, req, res) => {
            console.log('Proxying /api request:', req.method, req.url, '-> http://localhost:3000' + proxyReq.path)
          })
          proxy.on('proxyRes', (proxyRes, req, res) => {
            console.log('Proxy response for /api:', proxyRes.statusCode, req.url)
          })
        }
      },
      '/dns-api': {
        target: 'http://localhost:8053',
        changeOrigin: true,
        rewrite: (path) => {
          // Handle health check endpoint specifically
          if (path === '/dns-api/health') {
            console.log('Rewriting /dns-api/health -> /health')
            return '/health'
          }
          // Handle other API endpoints
          const rewritten = path.replace(/^\/dns-api/, '/api')
          console.log(`Rewriting ${path} -> ${rewritten}`)
          return rewritten
        },
        configure: (proxy, options) => {
          proxy.on('error', (err, req, res) => {
            console.log('Proxy error for /dns-api:', err.message)
          })
          proxy.on('proxyReq', (proxyReq, req, res) => {
            console.log('Proxying /dns-api request:', req.method, req.url, '-> http://localhost:8053' + proxyReq.path)
          })
          proxy.on('proxyRes', (proxyRes, req, res) => {
            console.log('Proxy response for /dns-api:', proxyRes.statusCode, req.url)
          })
        }
      }
    }
  },
  build: {
    outDir: 'dist',
    sourcemap: true,
  },
  optimizeDeps: {
    include: ['monaco-editor']
  }
})
