const dgram = require("dgram");
const { parseQuery, createResponse } = require("../lib/dns-parser");
const { getRecordsForDomain } = require("../lib/dns-resolver");
const { eventBus, EventTypes, errorHandler } = require("../shared");

function startDnsUdpServer(port = 53) {
  const server = dgram.createSocket("udp4");

  server.on("error", async (err) => {
    console.error(`DNS server error: ${err.message}`);

    // Publish error event
    await eventBus.publish(EventTypes.SYSTEM_ERROR, {
      component: 'dns-server',
      operation: 'udp_server',
      error: err.message
    });
  });

  //rinfo : info about the client who sent it(ip & port of client)
  //msg : the message sent by the client(raw query packet)
  server.on("message", async (msg, rinfo) => {
    let query;
    try {
      // Parse query with error handling
      query = await errorHandler.executeWithProtection('dns.parseQuery', async () => {
        return parseQuery(msg);
      }, {
        component: 'dns-server',
        operation: 'parseQuery',
        retry: { maxRetries: 1, baseDelay: 100 },
        fallback: async () => null
      });

      if (!query || !query.questions || query.questions.length === 0) {
        console.warn("Received DNS Query with no questions — ignoring.");
        return;
      }

      const question = query.questions[0];// Extracting the first question.

      console.log(
        `DNS Query received: ${question.name} (Type ${question.type}) from ${rinfo.address}:${rinfo.port}`
      );

      // Publish DNS query event
      await eventBus.publish(EventTypes.DNS_QUERY_RECEIVED, {
        domain: question.name,
        type: question.type,
        clientIp: rinfo.address,
        clientPort: rinfo.port
      });

      // Get records with error handling
      const answers = await errorHandler.executeWithProtection('dns.getRecords', async () => {
        return getRecordsForDomain(question.name, question.type);
      }, {
        component: 'dns-server',
        operation: 'getRecords',
        retry: { maxRetries: 2, baseDelay: 100 },
        fallback: async () => []
      });

      console.log(`Query result for ${question.name}: ${answers.length} answers found`);

      // Create and send response with error handling
      await errorHandler.executeWithProtection('dns.sendResponse', async () => {
        const response = createResponse(query, answers);
        server.send(response, rinfo.port, rinfo.address);
      }, {
        component: 'dns-server',
        operation: 'sendResponse',
        retry: { maxRetries: 1, baseDelay: 100 }
      });

      console.log(`Response sent for ${question.name} with ${answers.length} answers`);

    } catch (err) {
      console.error("Error processing DNS query:", err);

      // Publish error event
      await eventBus.publish(EventTypes.SYSTEM_ERROR, {
        component: 'dns-server',
        operation: 'process_query',
        error: err.message,
        domain: query?.questions?.[0]?.name || 'unknown'
      });

      // Only send error response if we have a valid query
      if (query) {
        try {
          const errorResponse = createResponse(query, []);
          server.send(errorResponse, rinfo.port, rinfo.address);
        } catch (responseError) {
          console.error("Failed to send error response:", responseError);
        }
      }
    }
  });

  server.bind(port, async () => {
    console.log(`DNS server running on port ${port}`);

    // Publish startup event
    await eventBus.publish(EventTypes.SYSTEM_RECOVERY, {
      component: 'dns-server',
      action: 'udp_server_started',
      port,
      message: 'DNS UDP server started successfully'
    });
  });

  return server;
}

module.exports = { startDnsUdpServer };
