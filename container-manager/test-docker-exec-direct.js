#!/usr/bin/env node

/**
 * Direct Docker Exec Test
 * This script tests Docker exec functionality directly to isolate the terminal issue
 */

const Docker = require('dockerode');

async function testDockerExecDirect() {
  console.log('🔍 Testing Docker Exec Directly');
  console.log('================================\n');

  try {
    const docker = new Docker();

    // Step 1: Find project container
    console.log('📋 Step 1: Finding project container');
    const containers = await docker.listContainers();
    const projectContainer = containers.find(c => 
      c.Names[0].includes('project-') && c.State === 'running'
    );

    if (!projectContainer) {
      console.log('❌ No running project containers found!');
      return;
    }

    const containerId = projectContainer.Id;
    const containerName = projectContainer.Names[0];
    console.log(`✅ Found container: ${containerName} (${containerId.substring(0, 12)})`);

    // Step 2: Get container object
    const container = docker.getContainer(containerId);

    // Step 3: Test TTY exec (like our terminal)
    console.log('\n📋 Step 2: Testing TTY exec (like terminal)');
    try {
      const exec2 = await container.exec({
        Cmd: ['sh', '-i'],
        AttachStdin: true,
        AttachStdout: true,
        AttachStderr: true,
        Tty: true,
        WorkingDir: '/workspace'
      });

      const stream2 = await exec2.start({
        hijack: true,
        stdin: true
      });

      let ttyOutput = '';
      let outputReceived = false;

      stream2.on('data', (data) => {
        const output = data.toString();
        ttyOutput += output;
        outputReceived = true;
        console.log(`   📤 TTY Output: "${output.replace(/\r/g, '\\r').replace(/\n/g, '\\n')}"`);
      });

      stream2.on('error', (error) => {
        console.log(`   ❌ TTY Stream error: ${error.message}`);
      });

      stream2.on('end', () => {
        console.log('   📋 TTY Stream ended');
      });

      // Wait a bit for initial output
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Send some commands
      console.log('\n   📋 Sending commands to TTY:');
      
      const commands = ['pwd\n', 'ls\n', 'echo "test"\n'];
      
      for (const cmd of commands) {
        console.log(`   📤 Sending: "${cmd.replace(/\n/g, '\\n')}"`);
        stream2.write(cmd);
        
        // Wait for response
        await new Promise(resolve => setTimeout(resolve, 1500));
      }

      // Final wait
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Close the stream
      stream2.destroy();

      if (outputReceived) {
        console.log('\n   ✅ TTY exec is working!');
        console.log('   📋 This means the Docker exec functionality is fine');
        console.log('   📋 The issue is likely in our terminal service event handling');
      } else {
        console.log('\n   ⚠️  TTY exec not receiving output');
        console.log('   📋 This indicates a Docker exec configuration issue');
      }

    } catch (error) {
      console.log(`   ❌ TTY exec failed: ${error.message}`);
    }

    console.log('\n🎉 Docker Exec Direct Test Completed!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test
testDockerExecDirect().catch(console.error);
