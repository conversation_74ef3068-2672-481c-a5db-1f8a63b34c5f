{"name": "cloud-ide-container-manager", "version": "1.0.0", "description": "Container management service for Cloud IDE", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "test": "jest", "test:manual": "node test/test-container-manager.js", "test:integration": "node test/test-integration.js"}, "dependencies": {"@cloud-ide/shared": "file:../shared", "axios": "^1.9.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dockerode": "^4.0.2", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.5.0", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "socket.io": "^4.7.4", "sqlite": "^5.1.1", "sqlite3": "^5.1.6", "uuid": "^9.0.1", "winston": "^3.11.0"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.2"}}