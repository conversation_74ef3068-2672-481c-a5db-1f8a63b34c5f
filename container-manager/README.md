# Cloud IDE Container Manager

The Container Manager is a crucial component of the Cloud IDE project, responsible for managing Docker containers for development environments.

## Features

- Container lifecycle management (create, start, stop, remove)
- Resource allocation and monitoring
- Container health checks
- System-wide resource monitoring
- Container resource usage history
- Secure container isolation

## API Endpoints

### Container Management

- `POST /api/containers` - Create a new container
- `GET /api/containers` - List all containers
- `POST /api/containers/:id/stop` - Stop a container
- `DELETE /api/containers/:id` - Remove a container
- `GET /api/containers/:id/stats` - Get container stats

### Monitoring

- `GET /api/monitoring/system` - Get system-wide container stats
- `GET /api/monitoring/containers/:id/history` - Get container resource usage history

## Setup

1. Install dependencies:
```bash
npm install
```

2. Create a `.env` file:
```env
PORT=3000
DOCKER_SOCKET=/var/run/docker.sock
LOG_LEVEL=info
NODE_ENV=development
```

3. Start the service:
```bash
npm start
```

For development:
```bash
npm run dev
```

## Container Configuration

When creating a container, provide the following configuration:

```json
{
  "name": "container-name",
  "image": "base-image:tag",
  "ports": {
    "8080/tcp": {}
  },
  "portBindings": {
    "8080/tcp": [
      {
        "HostPort": "8080",
        "HostIp": "0.0.0.0"
      }
    ]
  },
  "memory": *********,  // 512MB
  "cpuShares": 1024,
  "environment": ["KEY=value"],
  "workingDir": "/workspace",
  "command": ["/bin/bash"]
}
```

## Security Considerations

- All containers are created with resource limits
- Network isolation is enforced
- Container health is monitored
- Resource usage is tracked and limited
- Secure logging of all operations

## Integration with Other Components

The Container Manager works in conjunction with:
- DNS Server: For container name resolution
- Reverse Proxy: For routing traffic to containers
- Frontend: For user interface and container management
- Backend API: For user authentication and container orchestration

## Development

To contribute to the Container Manager:

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests: `npm test`
5. Submit a pull request

## License

MIT 