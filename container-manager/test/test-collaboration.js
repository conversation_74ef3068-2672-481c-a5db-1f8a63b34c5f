const CollaborationService = require('../src/services/collaboration.service');

async function testCollaborationService() {
  console.log('🧪 Testing Collaboration Service...\n');

  const service = new CollaborationService();
  
  try {
    // Test 1: Join room
    console.log('Test 1: Join collaboration room');
    const result1 = service.joinRoom('socket1', 'project1', '/test.js', 'Alice');
    console.log('✅ User joined:', result1.success);
    console.log('   Room ID:', result1.roomId);
    console.log('   User ID:', result1.userId);
    console.log('   Nickname:', result1.userInfo.nickname);
    console.log('   Color:', result1.userInfo.color);
    console.log('');

    // Test 2: Second user joins same room
    console.log('Test 2: Second user joins same room');
    const result2 = service.joinRoom('socket2', 'project1', '/test.js', 'Bob');
    console.log('✅ Second user joined:', result2.success);
    console.log('   Collaborators count:', result2.collaborators.length);
    console.log('   Collaborators:', result2.collaborators.map(u => u.nickname));
    console.log('');

    // Test 3: Update document
    console.log('Test 3: Update document content');
    const result3 = service.updateDocument('socket1', 'console.log("Hello World");');
    console.log('✅ Document updated:', result3.success);
    console.log('   Content length:', result3.content.length);
    console.log('');

    // Test 4: Update cursor
    console.log('Test 4: Update cursor position');
    const result4 = service.updateCursor('socket1', { line: 0, column: 10 }, null);
    console.log('✅ Cursor updated:', result4.success);
    console.log('   Cursor position:', result4.cursor);
    console.log('   Active cursors:', result4.cursors.length);
    console.log('');

    // Test 5: Update nickname
    console.log('Test 5: Update nickname');
    const result5 = service.updateNickname('socket1', 'Alice Smith');
    console.log('✅ Nickname updated:', result5.success);
    console.log('   New nickname:', result5.nickname);
    console.log('');

    // Test 6: Get room info
    console.log('Test 6: Get room information');
    const room = service.getRoom(result1.roomId);
    console.log('✅ Room found:', !!room);
    console.log('   Users in room:', room.users.size);
    console.log('   Document length:', room.document.length);
    console.log('   Last activity:', room.lastActivity);
    console.log('');

    // Test 7: Leave room
    console.log('Test 7: User leaves room');
    const result7 = service.leaveRoom('socket1');
    console.log('✅ User left:', result7.success);
    console.log('   Remaining collaborators:', result7.collaborators.length);
    console.log('');

    // Test 8: Second user leaves (room should be cleaned up)
    console.log('Test 8: Last user leaves room');
    const result8 = service.leaveRoom('socket2');
    console.log('✅ Last user left:', result8.success);
    console.log('   Room still exists:', !!service.getRoom(result1.roomId));
    console.log('');

    // Test 9: Error handling - invalid operations
    console.log('Test 9: Error handling');
    const result9 = service.updateDocument('invalid-socket', 'test');
    console.log('✅ Error handled correctly:', !result9.success);
    console.log('   Error message:', result9.error);
    console.log('');

    console.log('🎉 All collaboration service tests passed!\n');

  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    // Cleanup
    service.cleanup();
  }
}

// Test room ID generation
function testRoomIdGeneration() {
  console.log('🧪 Testing Room ID Generation...\n');
  
  const service = new CollaborationService();
  
  const roomId1 = service.generateRoomId('project1', '/src/index.js');
  const roomId2 = service.generateRoomId('project1', '/src/utils.js');
  const roomId3 = service.generateRoomId('project2', '/src/index.js');
  
  console.log('Room ID 1:', roomId1);
  console.log('Room ID 2:', roomId2);
  console.log('Room ID 3:', roomId3);
  
  console.log('✅ Different files generate different room IDs:', roomId1 !== roomId2);
  console.log('✅ Different projects generate different room IDs:', roomId1 !== roomId3);
  console.log('');
  
  service.cleanup();
}

// Test user color generation
function testUserColors() {
  console.log('🧪 Testing User Color Generation...\n');
  
  const service = new CollaborationService();
  
  const colors = [];
  for (let i = 0; i < 12; i++) {
    colors.push(service.generateUserColor(i));
  }
  
  console.log('Generated colors:', colors);
  console.log('✅ Colors are cycling correctly:', colors[0] === colors[10]);
  console.log('✅ Different indices generate different colors:', colors[0] !== colors[1]);
  console.log('');
  
  service.cleanup();
}

// Run all tests
async function runAllTests() {
  console.log('🚀 Starting Collaboration Service Tests\n');
  console.log('=' .repeat(50));
  
  testRoomIdGeneration();
  testUserColors();
  await testCollaborationService();
  
  console.log('=' .repeat(50));
  console.log('✨ All tests completed successfully!');
}

// Run tests if this file is executed directly
if (require.main === module) {
  runAllTests().catch(console.error);
}

module.exports = {
  testCollaborationService,
  testRoomIdGeneration,
  testUserColors,
  runAllTests
};
