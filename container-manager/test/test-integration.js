const axios = require('axios');
const dns = require('dns').promises;
const { exec } = require('child_process');
const util = require('util');
const execPromise = util.promisify(exec);

const CONTAINER_MANAGER_URL = 'http://localhost:3000/api';
const DNS_SERVER = '127.0.0.1:5354';
const DNS_API_URL = 'http://localhost:8053/api/dns'; // Updated DNS API base URL
const REVERSE_PROXY_URL = 'http://localhost:80';

// Test container configuration
const testContainer = {
  name: 'test-ide-container',
  image: 'node:18-alpine',
  ports: {
    '8080/tcp': {}
  },
  portBindings: {
    '8080/tcp': [
      {
        HostPort: '8080',
        HostIp: '0.0.0.0'
      }
    ]
  },
  memory: 512 * 1024 * 1024,
  cpuShares: 1024,
  environment: [
    'NODE_ENV=test',
    'VIRTUAL_HOST=test-ide.local'
  ],
  workingDir: '/workspace',
  command: ['/bin/sh', '-c', 'while true; do sleep 1; done']
};

async function checkDNSServer() {
  try {
    // Check DNS records endpoint instead of health
    const response = await axios.get(`${DNS_API_URL}/records`);
    console.log('✅ DNS server is running and responding:', response.data);
    return true;
  } catch (error) {
    console.error('❌ DNS server check failed:', error.message);
    return false;
  }
}

async function testDNSResolution(domain) {
  try {
    // Set DNS server for this test
    dns.setServers([DNS_SERVER]);
    
    // Try to resolve the domain
    const dnsResult = await dns.lookup(domain);
    console.log(`✅ DNS resolution for ${domain} successful:`, dnsResult);
    return true;
  } catch (error) {
    console.error(`❌ DNS resolution for ${domain} failed:`, error.message);
    return false;
  }
}

async function testIntegration() {
  try {
    console.log('🚀 Starting Integration Tests...\n');

    // Step 1: Check DNS Server Status
    console.log('🔍 Step 1: Checking DNS server status...');
    const dnsServerHealthy = await checkDNSServer();
    if (!dnsServerHealthy) {
      console.error('❌ DNS server is not healthy. Please check if it\'s running.');
      return;
    }

    // Step 2: Create Container
    console.log('\n📦 Step 2: Creating container...');
    const createResponse = await axios.post(`${CONTAINER_MANAGER_URL}/containers`, testContainer);
    console.log('✅ Container created successfully:', createResponse.data);
    const containerId = createResponse.data.containerId;

    // Step 3: Wait for container to be fully started
    console.log('\n⏳ Step 3: Waiting for container to start...');
    await new Promise(resolve => setTimeout(resolve, 5000));

    // Step 4: Test DNS Resolution
    console.log('\n🔍 Step 4: Testing DNS resolution...');
    const dnsSuccess = await testDNSResolution('test-ide.local');
    
    if (!dnsSuccess) {
      console.log('\n⚠️ DNS resolution failed. Checking DNS records...');
      try {
        // Try to query DNS records directly
        const dnsRecordsResponse = await axios.get(`${DNS_API_URL}/records`);
        console.log('Current DNS records:', dnsRecordsResponse.data);
        
        // Try to add the test domain if it doesn't exist
        console.log('\nAttempting to add test domain...');
        const addDomainResponse = await axios.post(`${DNS_API_URL}/subdomains`, {
          subdomain: 'test-ide',
          domain: 'local',
          ipAddress: '127.0.0.1',
          ttl: 3600,
          isPersistent: true
        });
        console.log('Domain addition response:', addDomainResponse.data);
      } catch (error) {
        console.error('Failed to manage DNS records:', error.message);
      }
    }

    // Step 5: Test Reverse Proxy
    console.log('\n🔄 Step 5: Testing reverse proxy...');
    try {
      const proxyResponse = await axios.get(`${REVERSE_PROXY_URL}`, {
        headers: {
          'Host': 'test-ide.local'
        }
      });
      console.log('✅ Reverse proxy test successful:', proxyResponse.status);
    } catch (error) {
      console.error('❌ Reverse proxy test failed:', error.message);
    }

    // Step 6: Clean up
    console.log('\n🧹 Step 6: Cleaning up...');
    await axios.post(`${CONTAINER_MANAGER_URL}/containers/${containerId}/stop`);
    await axios.delete(`${CONTAINER_MANAGER_URL}/containers/${containerId}`);
    console.log('✅ Cleanup completed successfully');

    console.log('\n✨ Integration tests completed!');
  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
  }
}

// Run the integration tests
testIntegration(); 