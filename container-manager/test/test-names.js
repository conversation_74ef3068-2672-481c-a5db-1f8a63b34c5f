const CollaborationService = require('../src/services/collaboration.service');

function testSimpleNames() {
  console.log('🧪 Testing Simple Name Generation...\n');

  const service = new CollaborationService();

  console.log('Generated Simple Names:');
  for (let i = 0; i < 10; i++) {
    const name = service.generateSimpleName(i);
    const color = service.generateUserColor(i);
    console.log(`${i + 1}. ${name} (${color})`);
  }

  console.log('\n✅ Simple name generation working!');

  service.cleanup();
}

// Test joining without nickname to see auto-generated names
function testAutoGeneratedNames() {
  console.log('\n🧪 Testing Auto-Generated Names in Rooms...\n');

  const service = new CollaborationService();

  // Join room without providing nickname
  const result1 = service.joinRoom('socket1', 'project1', '/test.js');
  console.log('User 1 auto-generated name:', result1.userInfo.nickname);
  console.log('User 1 color:', result1.userInfo.color);

  const result2 = service.joinRoom('socket2', 'project1', '/test.js');
  console.log('User 2 auto-generated name:', result2.userInfo.nickname);
  console.log('User 2 color:', result2.userInfo.color);

  const result3 = service.joinRoom('socket3', 'project1', '/test.js');
  console.log('User 3 auto-generated name:', result3.userInfo.nickname);
  console.log('User 3 color:', result3.userInfo.color);

  console.log('\n✅ Auto-generated names working in rooms!');

  service.cleanup();
}

testSimpleNames();
testAutoGeneratedNames();
