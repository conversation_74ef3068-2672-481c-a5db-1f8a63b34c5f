const axios = require('axios');
const { exec } = require('child_process');
const util = require('util');
const execPromise = util.promisify(exec);

const API_URL = 'http://localhost:3000/api';

// Test container configuration
const testContainer = {
  name: 'test-ide-container222',
  image: 'node:18-alpine',
  ports: {
    '8080/tcp': {}
  },
  portBindings: {
    '8080/tcp': [
      {
        HostPort: '8080',
        HostIp: '0.0.0.0'
      }
    ]
  },
  memory: 512 * 1024 * 1024, // 512MB
  cpuShares: 1024,
  environment: ['NODE_ENV=test'],
  workingDir: '/workspace',
  command: ['/bin/sh', '-c', 'while true; do sleep 1; done']
};

async function runTests() {
  try {
    console.log('🚀 Starting Container Manager Tests...\n');

    // Test 1: Create Container
    console.log('📦 Test 1: Creating container...');
    const createResponse = await axios.post(`${API_URL}/containers`, testContainer);
    console.log('✅ Container created successfully:', createResponse.data);
    const containerId = createResponse.data.containerId;

    // Test 2: List Containers
    console.log('\n📋 Test 2: Listing containers...');
    const listResponse = await axios.get(`${API_URL}/containers`);
    console.log('✅ Containers listed successfully:', listResponse.data);

    // Test 3: Get Container Stats
    console.log('\n📊 Test 3: Getting container stats...');
    const statsResponse = await axios.get(`${API_URL}/containers/${containerId}/stats`);
    console.log('✅ Container stats retrieved successfully:', statsResponse.data);

    // Test 4: Get System Stats
    console.log('\n📈 Test 4: Getting system stats...');
    const systemStatsResponse = await axios.get(`${API_URL}/monitoring/system`);
    console.log('✅ System stats retrieved successfully:', systemStatsResponse.data);

    // Test 5: Stop Container
    console.log('\n🛑 Test 5: Stopping container...');
    const stopResponse = await axios.post(`${API_URL}/containers/${containerId}/stop`);
    console.log('✅ Container stopped successfully:', stopResponse.data);

    // Test 6: Remove Container
    console.log('\n🗑️ Test 6: Removing container...');
    const removeResponse = await axios.delete(`${API_URL}/containers/${containerId}`);
    console.log('✅ Container removed successfully:', removeResponse.data);

    console.log('\n✨ All tests completed successfully!');
  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
  }
}

// Run the tests
runTests(); 