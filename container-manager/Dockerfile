FROM node:18-alpine

# Install Docker CLI and curl for container management
RUN apk add --no-cache docker-cli curl

# Set working directory
WORKDIR /app

# Copy package files
COPY container-manager/package*.json ./

# Install dependencies
RUN npm install --production

# Copy source code
COPY container-manager/src/ ./src/

# Copy shared services (temporary workaround for Windows volume mount issue)
# Copy shared package files first
COPY shared/package*.json ./shared/

# Install shared dependencies
RUN cd shared && npm install --production

# Copy shared source files
COPY shared/config ./shared/config
COPY shared/events ./shared/events
COPY shared/integration ./shared/integration
COPY shared/utils ./shared/utils
COPY shared/index.js ./shared/

# Create necessary directories
RUN mkdir -p /app/logs && \
    chmod 755 /app/logs

# Note: Running as root to access Docker socket
# In production, consider using Docker-in-Docker or rootless Docker

# Expose port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:3000/health || exit 1

# Start the application
CMD ["node", "src/index.js"]
