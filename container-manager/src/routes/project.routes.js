const Joi = require('joi');
const { ProjectService } = require('../services/project.service');
const logger = require('../utils/logger');

// Validation schemas
const createProjectSchema = Joi.object({
  name: Joi.string().min(1).max(100).required(),
  description: Joi.string().max(500).default(''),
  type: Joi.string().valid('general', 'web', 'api', 'mobile', 'desktop').default('general'),
  template: Joi.string().valid('blank', 'javascript', 'react', 'vue', 'python', 'java').default('blank'),
  language: Joi.string().valid('general', 'javascript', 'react', 'vue', 'nodejs', 'python', 'java', 'typescript', 'cpp', 'go', 'rust').default('javascript'),
  framework: Joi.string().default('none'),
  nodeVersion: Joi.string().default('18'),
  port: Joi.number().integer().min(1000).max(65535).default(3000),
  environment: Joi.string().valid('development', 'staging', 'production').default('development'),
  settings: Joi.object().default({})
});

const updateProjectSchema = Joi.object({
  name: Joi.string().min(1).max(100),
  description: Joi.string().max(500),
  settings: Joi.object()
});

const projectIdSchema = Joi.object({
  id: Joi.string().uuid().required()
});

function setupProjectRoutes(app, docker) {
  const projectService = new ProjectService(docker);

  // List all projects
  app.get('/api/projects', async (req, res) => {
    try {
      const projects = await projectService.listProjects();
      res.json(projects);
    } catch (error) {
      logger.error('Failed to list projects:', error);
      res.status(500).json({ error: error.message });
    }
  });

  // Get project templates (must be before /:id route)
  app.get('/api/projects/templates', async (req, res) => {
    try {
      const templates = [
        {
          id: 'blank',
          name: 'Blank Project',
          description: 'Start with an empty project',
          language: 'general',
          icon: '📄',
          features: ['Basic README', 'Empty workspace']
        },
        {
          id: 'javascript',
          name: 'JavaScript/Node.js',
          description: 'Node.js project with package.json and sample code',
          language: 'javascript',
          icon: '🟨',
          features: ['package.json', 'index.js', 'npm scripts']
        },
        {
          id: 'react',
          name: 'React App',
          description: 'React application with modern setup',
          language: 'react',
          icon: '⚛️',
          features: ['React 18', 'JSX components', 'CSS styling']
        },
        {
          id: 'vue',
          name: 'Vue.js App',
          description: 'Vue.js application with Vue 3',
          language: 'vue',
          icon: '💚',
          features: ['Vue 3', 'Single File Components', 'Vue CLI']
        },
        {
          id: 'python',
          name: 'Python Project',
          description: 'Python project with requirements.txt and sample code',
          language: 'python',
          icon: '🐍',
          features: ['main.py', 'requirements.txt', 'Python 3.11+']
        },
        {
          id: 'java',
          name: 'Java Project',
          description: 'Java project with Maven and JUnit testing',
          language: 'java',
          icon: '☕',
          features: ['Maven build', 'JUnit 5', 'Java 17']
        }
      ];

      res.json(templates);
    } catch (error) {
      logger.error('Failed to get templates:', error);
      res.status(500).json({ error: error.message });
    }
  });

  // Get project by ID
  app.get('/api/projects/:id', async (req, res) => {
    try {
      const { error, value } = projectIdSchema.validate({ id: req.params.id });
      if (error) {
        return res.status(400).json({ error: error.details[0].message });
      }

      const project = await projectService.getProject(value.id);
      res.json(project);
    } catch (error) {
      logger.error('Failed to get project:', error);
      if (error.message === 'Project not found') {
        res.status(404).json({ error: error.message });
      } else {
        res.status(500).json({ error: error.message });
      }
    }
  });

  // Create new project
  app.post('/api/projects', async (req, res) => {
    try {
      const { error, value } = createProjectSchema.validate(req.body);
      if (error) {
        return res.status(400).json({ error: error.details[0].message });
      }

      const project = await projectService.createProject(value);
      res.status(201).json(project);
    } catch (error) {
      logger.error('Failed to create project:', error);
      res.status(500).json({ error: error.message });
    }
  });

  // Update project
  app.put('/api/projects/:id', async (req, res) => {
    try {
      const { error: idError, value: idValue } = projectIdSchema.validate({ id: req.params.id });
      if (idError) {
        return res.status(400).json({ error: idError.details[0].message });
      }

      const { error: bodyError, value: bodyValue } = updateProjectSchema.validate(req.body);
      if (bodyError) {
        return res.status(400).json({ error: bodyError.details[0].message });
      }

      const project = await projectService.updateProject(idValue.id, bodyValue);
      res.json(project);
    } catch (error) {
      logger.error('Failed to update project:', error);
      if (error.message === 'Project not found') {
        res.status(404).json({ error: error.message });
      } else {
        res.status(500).json({ error: error.message });
      }
    }
  });

  // Delete project
  app.delete('/api/projects/:id', async (req, res) => {
    try {
      const { error, value } = projectIdSchema.validate({ id: req.params.id });
      if (error) {
        return res.status(400).json({ error: error.details[0].message });
      }

      const result = await projectService.deleteProject(value.id);
      res.json(result);
    } catch (error) {
      logger.error('Failed to delete project:', error);
      if (error.message === 'Project not found') {
        res.status(404).json({ error: error.message });
      } else {
        res.status(500).json({ error: error.message });
      }
    }
  });

  // Start project (deploy container)
  app.post('/api/projects/:id/start', async (req, res) => {
    try {
      const { error, value } = projectIdSchema.validate({ id: req.params.id });
      if (error) {
        return res.status(400).json({ error: error.details[0].message });
      }

      const result = await projectService.startProject(value.id);
      res.json(result);
    } catch (error) {
      logger.error('Failed to start project:', error);
      if (error.message === 'Project not found') {
        res.status(404).json({ error: error.message });
      } else {
        res.status(500).json({ error: error.message });
      }
    }
  });

  // Stop project
  app.post('/api/projects/:id/stop', async (req, res) => {
    try {
      const { error, value } = projectIdSchema.validate({ id: req.params.id });
      if (error) {
        return res.status(400).json({ error: error.details[0].message });
      }

      const result = await projectService.stopProject(value.id);
      res.json(result);
    } catch (error) {
      logger.error('Failed to stop project:', error);
      if (error.message === 'Project not found') {
        res.status(404).json({ error: error.message });
      } else {
        res.status(500).json({ error: error.message });
      }
    }
  });

  // Execute command in project container
  app.post('/api/projects/:id/execute', async (req, res) => {
    try {
      const { error, value } = projectIdSchema.validate({ id: req.params.id });
      if (error) {
        return res.status(400).json({ error: error.details[0].message });
      }

      const { command, shell = 'sh', timeout = 30000 } = req.body;
      if (!command) {
        return res.status(400).json({ error: 'Command is required' });
      }

      const result = await projectService.executeCommand(value.id, command, {
        shell,
        timeout
      });

      res.json({
        success: true,
        ...result
      });
    } catch (error) {
      logger.error('Failed to execute command:', error);
      if (error.message === 'Project not found') {
        res.status(404).json({ error: error.message });
      } else {
        res.status(500).json({
          success: false,
          error: error.message
        });
      }
    }
  });

  // Run project (language-specific execution)
  app.post('/api/projects/:id/run', async (req, res) => {
    try {
      const { error, value } = projectIdSchema.validate({ id: req.params.id });
      if (error) {
        return res.status(400).json({ error: error.details[0].message });
      }

      const { fileName, timeout = 60000 } = req.body;

      const result = await projectService.runProject(value.id, fileName, { timeout });

      res.json({
        success: true,
        ...result
      });
    } catch (error) {
      logger.error('Failed to run project:', error);
      if (error.message === 'Project not found') {
        res.status(404).json({ error: error.message });
      } else {
        res.status(500).json({
          success: false,
          error: error.message
        });
      }
    }
  });

  // Get project container status
  app.get('/api/projects/:id/container/status', async (req, res) => {
    try {
      const { error, value } = projectIdSchema.validate({ id: req.params.id });
      if (error) {
        return res.status(400).json({ error: error.details[0].message });
      }

      const status = await projectService.getContainerStatus(value.id);
      res.json(status);
    } catch (error) {
      logger.error('Failed to get container status:', error);
      if (error.message === 'Project not found') {
        res.status(404).json({ error: error.message });
      } else {
        res.status(500).json({ error: error.message });
      }
    }
  });

  // Copy files to container (for testing)
  app.post('/api/projects/:id/copy-to-container', async (req, res) => {
    try {
      const { error, value } = projectIdSchema.validate({ id: req.params.id });
      if (error) {
        return res.status(400).json({ error: error.details[0].message });
      }

      await projectService.copyFilesToContainer(value.id);
      res.json({ success: true, message: 'Files copied to container successfully' });
    } catch (error) {
      logger.error('Failed to copy files to container:', error);
      res.status(500).json({ error: error.message });
    }
  });

  // Initialize sample projects for development
  app.post('/api/projects/init-samples', async (req, res) => {
    try {
      const sampleProjects = [
        {
          name: 'My Web App',
          description: 'A modern web application built with React',
          type: 'web',
          template: 'react',
          language: 'javascript',
          framework: 'react'
        },
        {
          name: 'API Server',
          description: 'Node.js REST API with Express',
          type: 'api',
          template: 'javascript',
          language: 'javascript',
          framework: 'express'
        },
        {
          name: 'Data Analysis',
          description: 'Python scripts for data processing',
          type: 'general',
          template: 'python',
          language: 'python',
          framework: 'none'
        }
      ];

      const createdProjects = [];
      for (const projectData of sampleProjects) {
        try {
          const project = await projectService.createProject(projectData);
          createdProjects.push(project);
        } catch (error) {
          logger.warn(`Failed to create sample project ${projectData.name}:`, error);
        }
      }

      res.json({
        message: 'Sample projects initialized',
        projects: createdProjects
      });
    } catch (error) {
      logger.error('Failed to initialize sample projects:', error);
      res.status(500).json({ error: error.message });
    }
  });

  // Get project statistics
  app.get('/api/projects/stats', async (req, res) => {
    try {
      const projects = await projectService.listProjects();
      
      const stats = {
        total: projects.length,
        running: projects.filter(p => p.status === 'running').length,
        stopped: projects.filter(p => p.status === 'stopped').length,
        notDeployed: projects.filter(p => p.status === 'not_deployed').length,
        byLanguage: {},
        byType: {}
      };

      // Count by language
      projects.forEach(project => {
        const lang = project.settings.language || 'unknown';
        stats.byLanguage[lang] = (stats.byLanguage[lang] || 0) + 1;
      });

      // Count by type
      projects.forEach(project => {
        const type = project.type || 'unknown';
        stats.byType[type] = (stats.byType[type] || 0) + 1;
      });

      res.json(stats);
    } catch (error) {
      logger.error('Failed to get project statistics:', error);
      res.status(500).json({ error: error.message });
    }
  });
}

module.exports = {
  setupProjectRoutes
};
