const { DockerService } = require('../services/docker.service');
const Joi = require('joi');

const containerSchema = Joi.object({
  name: Joi.string().required(),
  image: Joi.string().required(),
  ports: Joi.object().pattern(
    Joi.string(),
    Joi.object()
  ).required(),
  portBindings: Joi.object().pattern(
    Joi.string(),
    Joi.array().items(
      Joi.object({
        HostPort: Joi.string().required(),
        HostIp: Joi.string()
      })
    )
  ).required(),
  memory: Joi.number(),
  cpuShares: Joi.number(),
  environment: Joi.array().items(Joi.string()),
  workingDir: Joi.string(),
  command: Joi.array().items(Joi.string())
});

function setupContainerRoutes(app, docker) {
  const dockerService = new DockerService(docker);

  // Create a new container
  app.post('/api/containers', async (req, res) => {
    try {
      const { error, value } = containerSchema.validate(req.body);
      if (error) {
        return res.status(400).json({ error: error.details[0].message });
      }

      const container = await dockerService.createContainer(value);
      res.status(201).json({
        message: 'Container created successfully',
        containerId: container.id
      });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  });

  // List all containers
  app.get('/api/containers', async (req, res) => {
    try {
      const containers = await dockerService.listContainers();
      res.json(containers);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  });

  // Stop a container
  app.post('/api/containers/:id/stop', async (req, res) => {
    try {
      await dockerService.stopContainer(req.params.id);
      res.json({ message: 'Container stopped successfully' });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  });

  // Remove a container
  app.delete('/api/containers/:id', async (req, res) => {
    try {
      await dockerService.removeContainer(req.params.id);
      res.json({ message: 'Container removed successfully' });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  });

  // Get container stats
  app.get('/api/containers/:id/stats', async (req, res) => {
    try {
      const stats = await dockerService.getContainerStats(req.params.id);
      res.json(stats);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  });
}

module.exports = {
  setupContainerRoutes
}; 