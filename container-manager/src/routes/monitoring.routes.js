const { DockerService } = require('../services/docker.service');

function setupMonitoringRoutes(app, docker) {
  const dockerService = new DockerService(docker);

  // Get system-wide container stats
  app.get('/api/monitoring/system', async (req, res) => {
    try {
      const containers = await dockerService.listContainers();
      const statsPromises = containers.map(async (container) => {
        try {
          if (container.status.includes('Up')) {
            return await dockerService.getContainerStats(container.id);
          }
          return null;
        } catch (error) {
          console.error(`Failed to get stats for container ${container.id}:`, error);
          return null;
        }
      });

      const stats = await Promise.all(statsPromises);

      const systemStats = {
        totalContainers: containers.length,
        runningContainers: containers.filter(c => c.status.includes('Up')).length,
        totalMemory: stats.reduce((acc, stat) => acc + (stat?.memory?.usage || 0), 0),
        totalCpu: 0, // Will be calculated on frontend
        containers: containers.map((container, index) => ({
          ...container,
          stats: stats[index]
        })).filter(c => c.stats !== null) // Only include containers with stats
      };

      res.json(systemStats);
    } catch (error) {
      console.error('Error getting system stats:', error);
      res.status(500).json({ error: error.message });
    }
  });

  // Get container resource usage history
  app.get('/api/monitoring/containers/:id/history', async (req, res) => {
    try {
      const container = docker.getContainer(req.params.id);
      const stats = await container.stats({ stream: true });
      
      const history = [];
      stats.on('data', (chunk) => {
        const stat = JSON.parse(chunk.toString());
        history.push({
          timestamp: new Date().toISOString(),
          cpu: stat.cpu_stats,
          memory: stat.memory_stats,
          network: stat.networks
        });
      });

      // Send initial history
      res.json(history);

      // Clean up stream after response
      stats.on('end', () => {
        stats.destroy();
      });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  });
}

module.exports = {
  setupMonitoringRoutes
}; 