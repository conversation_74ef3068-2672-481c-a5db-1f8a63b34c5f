const Joi = require('joi');
const { FileSystemService } = require('../services/filesystem.service');
const logger = require('../utils/logger');

// Validation schemas
const listDirectorySchema = Joi.object({
  projectId: Joi.string().required(),
  path: Joi.string().default('/')
});

const readFileSchema = Joi.object({
  projectId: Joi.string().required(),
  path: Joi.string().required()
});

const writeFileSchema = Joi.object({
  projectId: Joi.string().required(),
  path: Joi.string().required(),
  content: Joi.string().required(),
  encoding: Joi.string().valid('utf8', 'base64').default('utf8')
});

const createDirectorySchema = Joi.object({
  projectId: Joi.string().required(),
  path: Joi.string().required()
});

const deleteItemSchema = Joi.object({
  projectId: Joi.string().required(),
  path: Joi.string().required()
});

const renameItemSchema = Joi.object({
  projectId: Joi.string().required(),
  oldPath: Joi.string().required(),
  newPath: Joi.string().required()
});

function setupFileSystemRoutes(app, docker) {
  const fileSystemService = new FileSystemService(docker);

  // List directory contents
  app.get('/api/files', async (req, res) => {
    try {
      const { error, value } = listDirectorySchema.validate({
        projectId: req.query.projectId || 'default',
        path: req.query.path
      });
      
      if (error) {
        return res.status(400).json({ error: error.details[0].message });
      }

      const result = await fileSystemService.listDirectory(value.projectId, value.path);
      res.json(result);
    } catch (error) {
      logger.error('Failed to list directory:', error);
      res.status(500).json({ error: error.message });
    }
  });

  // Read file content
  app.get('/api/files/content', async (req, res) => {
    try {
      const { error, value } = readFileSchema.validate({
        projectId: req.query.projectId || 'default',
        path: req.query.path
      });
      
      if (error) {
        return res.status(400).json({ error: error.details[0].message });
      }

      const result = await fileSystemService.readFile(value.projectId, value.path);
      res.json(result);
    } catch (error) {
      logger.error('Failed to read file:', error);
      res.status(500).json({ error: error.message });
    }
  });

  // Write file content
  app.post('/api/files/content', async (req, res) => {
    try {
      const { error, value } = writeFileSchema.validate({
        projectId: req.body.projectId || 'default',
        path: req.body.path,
        content: req.body.content,
        encoding: req.body.encoding
      });
      
      if (error) {
        return res.status(400).json({ error: error.details[0].message });
      }

      const result = await fileSystemService.writeFile(
        value.projectId, 
        value.path, 
        value.content, 
        { encoding: value.encoding }
      );
      
      res.json(result);
    } catch (error) {
      logger.error('Failed to write file:', error);
      res.status(500).json({ error: error.message });
    }
  });

  // Create directory
  app.post('/api/files/directory', async (req, res) => {
    try {
      const { error, value } = createDirectorySchema.validate({
        projectId: req.body.projectId || 'default',
        path: req.body.path
      });
      
      if (error) {
        return res.status(400).json({ error: error.details[0].message });
      }

      const result = await fileSystemService.createDirectory(value.projectId, value.path);
      res.status(201).json(result);
    } catch (error) {
      logger.error('Failed to create directory:', error);
      res.status(500).json({ error: error.message });
    }
  });

  // Delete file or directory
  app.delete('/api/files', async (req, res) => {
    try {
      const { error, value } = deleteItemSchema.validate({
        projectId: req.query.projectId || req.body.projectId || 'default',
        path: req.query.path || req.body.path
      });
      
      if (error) {
        return res.status(400).json({ error: error.details[0].message });
      }

      const result = await fileSystemService.deleteItem(value.projectId, value.path);
      res.json(result);
    } catch (error) {
      logger.error('Failed to delete item:', error);
      res.status(500).json({ error: error.message });
    }
  });

  // Rename/move file or directory
  app.put('/api/files/rename', async (req, res) => {
    try {
      const { error, value } = renameItemSchema.validate({
        projectId: req.body.projectId || 'default',
        oldPath: req.body.oldPath,
        newPath: req.body.newPath
      });
      
      if (error) {
        return res.status(400).json({ error: error.details[0].message });
      }

      const result = await fileSystemService.renameItem(
        value.projectId, 
        value.oldPath, 
        value.newPath
      );
      
      res.json(result);
    } catch (error) {
      logger.error('Failed to rename item:', error);
      res.status(500).json({ error: error.message });
    }
  });

  // Get file/directory information
  app.get('/api/files/info', async (req, res) => {
    try {
      const { error, value } = readFileSchema.validate({
        projectId: req.query.projectId || 'default',
        path: req.query.path
      });
      
      if (error) {
        return res.status(400).json({ error: error.details[0].message });
      }

      const result = await fileSystemService.getItemInfo(value.projectId, value.path);
      res.json(result);
    } catch (error) {
      logger.error('Failed to get item info:', error);
      res.status(500).json({ error: error.message });
    }
  });

  // Initialize default project with sample files
  app.post('/api/files/init-project', async (req, res) => {
    try {
      const projectId = req.body.projectId || 'default';
      
      // Create sample files for testing
      const sampleFiles = [
        {
          path: '/README.md',
          content: `# ${projectId} Project\n\nWelcome to your Cloud IDE project!\n\n## Getting Started\n\n1. Edit files in the file explorer\n2. Use the terminal for commands\n3. Save your work automatically\n\nHappy coding! 🚀`
        },
        {
          path: '/src/index.js',
          content: `// Welcome to ${projectId}!\nconsole.log('Hello from Cloud IDE!');\n\n// Start coding here...\nfunction main() {\n    console.log('Project initialized successfully!');\n}\n\nmain();`
        },
        {
          path: '/src/styles.css',
          content: `/* Styles for ${projectId} */\nbody {\n    font-family: 'Arial', sans-serif;\n    margin: 0;\n    padding: 20px;\n    background-color: #f5f5f5;\n}\n\n.container {\n    max-width: 800px;\n    margin: 0 auto;\n    background: white;\n    padding: 20px;\n    border-radius: 8px;\n    box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n}`
        },
        {
          path: '/package.json',
          content: JSON.stringify({
            name: projectId,
            version: '1.0.0',
            description: `${projectId} project created in Cloud IDE`,
            main: 'src/index.js',
            scripts: {
              start: 'node src/index.js',
              dev: 'node src/index.js'
            },
            keywords: ['cloud-ide', 'project'],
            author: 'Cloud IDE User'
          }, null, 2)
        }
      ];

      const results = [];
      for (const file of sampleFiles) {
        try {
          const result = await fileSystemService.writeFile(projectId, file.path, file.content);
          results.push({ path: file.path, success: true });
        } catch (error) {
          results.push({ path: file.path, success: false, error: error.message });
        }
      }

      res.json({
        message: 'Project initialized successfully',
        projectId,
        files: results
      });
    } catch (error) {
      logger.error('Failed to initialize project:', error);
      res.status(500).json({ error: error.message });
    }
  });
}

module.exports = {
  setupFileSystemRoutes
};
