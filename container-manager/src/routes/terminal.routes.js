const Joi = require('joi');
const { TerminalService } = require('../services/terminal.service');
const logger = require('../utils/logger');

// Validation schemas
const createSessionSchema = Joi.object({
  projectId: Joi.string().default('default'),
  containerId: Joi.string().allow(null).default(null),
  shell: Joi.string().valid('sh', 'bash', 'zsh').default('sh'),
  cwd: Joi.string().default(process.cwd()),
  environment: Joi.object().default({})
});

const sessionIdSchema = Joi.object({
  sessionId: Joi.string().uuid().required()
});

const writeDataSchema = Joi.object({
  sessionId: Joi.string().uuid().required(),
  data: Joi.string().required()
});

const resizeSchema = Joi.object({
  sessionId: Joi.string().uuid().required(),
  cols: Joi.number().integer().min(1).max(1000).required(),
  rows: Joi.number().integer().min(1).max(1000).required()
});

function setupTerminalRoutes(app, docker, projectService = null) {
  const terminalService = new TerminalService(docker, projectService);

  // Create new terminal session
  app.post('/api/terminal/sessions', async (req, res) => {
    try {
      const { error, value } = createSessionSchema.validate(req.body);
      if (error) {
        return res.status(400).json({ error: error.details[0].message });
      }

      const session = await terminalService.createSession(value);
      res.status(201).json(session);
    } catch (error) {
      logger.error('Failed to create terminal session:', error);
      res.status(500).json({ error: error.message });
    }
  });

  // List terminal sessions
  app.get('/api/terminal/sessions', async (req, res) => {
    try {
      const projectId = req.query.projectId || null;
      const sessions = terminalService.listSessions(projectId);
      res.json(sessions);
    } catch (error) {
      logger.error('Failed to list terminal sessions:', error);
      res.status(500).json({ error: error.message });
    }
  });

  // Get terminal session info
  app.get('/api/terminal/sessions/:sessionId', async (req, res) => {
    try {
      const { error, value } = sessionIdSchema.validate({ sessionId: req.params.sessionId });
      if (error) {
        return res.status(400).json({ error: error.details[0].message });
      }

      const session = terminalService.getSession(value.sessionId);
      if (!session) {
        return res.status(404).json({ error: 'Terminal session not found' });
      }

      res.json(session);
    } catch (error) {
      logger.error('Failed to get terminal session:', error);
      res.status(500).json({ error: error.message });
    }
  });

  // Write data to terminal session
  app.post('/api/terminal/sessions/:sessionId/write', async (req, res) => {
    try {
      const { error, value } = writeDataSchema.validate({
        sessionId: req.params.sessionId,
        data: req.body.data
      });
      if (error) {
        return res.status(400).json({ error: error.details[0].message });
      }

      const result = await terminalService.writeToSession(value.sessionId, value.data);
      res.json(result);
    } catch (error) {
      logger.error('Failed to write to terminal session:', error);
      if (error.message === 'Terminal session not found') {
        res.status(404).json({ error: error.message });
      } else {
        res.status(500).json({ error: error.message });
      }
    }
  });

  // Resize terminal session
  app.post('/api/terminal/sessions/:sessionId/resize', async (req, res) => {
    try {
      const { error, value } = resizeSchema.validate({
        sessionId: req.params.sessionId,
        cols: req.body.cols,
        rows: req.body.rows
      });
      if (error) {
        return res.status(400).json({ error: error.details[0].message });
      }

      const result = await terminalService.resizeSession(value.sessionId, value.cols, value.rows);
      res.json(result);
    } catch (error) {
      logger.error('Failed to resize terminal session:', error);
      if (error.message === 'Terminal session not found') {
        res.status(404).json({ error: error.message });
      } else {
        res.status(500).json({ error: error.message });
      }
    }
  });

  // Kill terminal session
  app.delete('/api/terminal/sessions/:sessionId', async (req, res) => {
    try {
      const { error, value } = sessionIdSchema.validate({ sessionId: req.params.sessionId });
      if (error) {
        return res.status(400).json({ error: error.details[0].message });
      }

      const result = await terminalService.killSession(value.sessionId);
      res.json(result);
    } catch (error) {
      logger.error('Failed to kill terminal session:', error);
      if (error.message === 'Terminal session not found') {
        res.status(404).json({ error: error.message });
      } else {
        res.status(500).json({ error: error.message });
      }
    }
  });

  // Get terminal session buffer (for reconnection)
  app.get('/api/terminal/sessions/:sessionId/buffer', async (req, res) => {
    try {
      const { error, value } = sessionIdSchema.validate({ sessionId: req.params.sessionId });
      if (error) {
        return res.status(400).json({ error: error.details[0].message });
      }

      const session = terminalService.getSession(value.sessionId);
      if (!session) {
        return res.status(404).json({ error: 'Terminal session not found' });
      }

      res.json({
        sessionId: session.id,
        buffer: session.buffer,
        lastActivity: session.lastActivity
      });
    } catch (error) {
      logger.error('Failed to get terminal buffer:', error);
      res.status(500).json({ error: error.message });
    }
  });

  // Execute command in project container (for Run button)
  app.post('/api/terminal/execute', async (req, res) => {
    try {
      const { projectId, command, shell = 'bash', timeout = 30000, useTerminalSession = true } = req.body;

      if (!projectId || !command) {
        return res.status(400).json({ error: 'projectId and command are required' });
      }

      // If useTerminalSession is true, execute in existing terminal session
      if (useTerminalSession) {
        const result = await terminalService.executeCommandInSession(projectId, command, {
          shell,
          timeout
        });
        return res.json(result);
      }

      // Otherwise, use project service to execute command (legacy behavior)
      if (!projectService) {
        return res.status(500).json({ error: 'Project service not available' });
      }

      const result = await projectService.executeCommand(projectId, command, {
        shell,
        timeout
      });

      res.json({
        success: true,
        ...result
      });
    } catch (error) {
      logger.error('Failed to execute command:', error);
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  });

  // Run project (for Run button)
  app.post('/api/terminal/run', async (req, res) => {
    try {
      const { projectId, fileName, timeout = 60000, useTerminalSession = true } = req.body;

      if (!projectId) {
        return res.status(400).json({ error: 'projectId is required' });
      }

      // If useTerminalSession is true, execute in existing terminal session
      if (useTerminalSession) {
        // Get project info to determine run command
        if (!projectService) {
          return res.status(500).json({ error: 'Project service not available' });
        }

        const project = await projectService.getProject(projectId);
        if (!project) {
          return res.status(404).json({ error: 'Project not found' });
        }

        // Get language-specific run command
        const language = project.settings?.language || 'javascript';
        const projectType = project.type || 'general';
        const dockerService = projectService.dockerService;
        const runCommand = dockerService.getRunCommand(language, projectType, fileName);

        const result = await terminalService.executeCommandInSession(projectId, runCommand, {
          timeout
        });
        return res.json(result);
      }

      // Otherwise, use project service to run project (legacy behavior)
      if (!projectService) {
        return res.status(500).json({ error: 'Project service not available' });
      }

      const result = await projectService.runProject(projectId, fileName, { timeout });

      res.json({
        success: true,
        ...result
      });
    } catch (error) {
      logger.error('Failed to run project:', error);
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  });

  // Health check for terminal service
  app.get('/api/terminal/health', async (req, res) => {
    try {
      const sessions = terminalService.listSessions();
      const activeSessions = sessions.filter(s => s.isActive).length;
      
      res.json({
        status: 'healthy',
        totalSessions: sessions.length,
        activeSessions,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      logger.error('Terminal health check failed:', error);
      res.status(500).json({ 
        status: 'unhealthy',
        error: error.message,
        timestamp: new Date().toISOString()
      });
    }
  });

  // Clean up inactive sessions (maintenance endpoint)
  app.post('/api/terminal/cleanup', async (req, res) => {
    try {
      const maxAge = req.body.maxAge || 3600000; // 1 hour default
      terminalService.cleanupInactiveSessions(maxAge);

      res.json({
        message: 'Cleanup initiated',
        maxAge,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      logger.error('Failed to cleanup terminal sessions:', error);
      res.status(500).json({ error: error.message });
    }
  });

  // Debug endpoint: Get container info for project
  app.get('/api/terminal/debug/:projectId', async (req, res) => {
    try {
      const { projectId } = req.params;
      const containers = await docker.listContainers();

      const debugInfo = {
        projectId,
        totalContainers: containers.length,
        containers: containers.map(c => ({
          id: c.id.substring(0, 12),
          name: c.name,
          status: c.status,
          subdomain: c.subdomain,
          labels: c.labels
        })),
        matchingContainers: containers.filter(c => {
          return (c.subdomain && c.subdomain.includes(projectId.toLowerCase())) ||
                 (c.name && c.name.includes(projectId.toLowerCase())) ||
                 (c.labels && c.labels['project.id'] === projectId);
        }).map(c => ({
          id: c.id.substring(0, 12),
          name: c.name,
          status: c.status,
          subdomain: c.subdomain
        }))
      };

      res.json(debugInfo);
    } catch (error) {
      logger.error('Failed to get debug info:', error);
      res.status(500).json({ error: error.message });
    }
  });

  return terminalService;
}

function setupTerminalWebSocket(io, terminalService) {
  // Initialize collaboration service
  const CollaborationService = require('../services/collaboration.service');
  const collaborationService = new CollaborationService();

  // Handle WebSocket connections for terminal and collaboration
  io.on('connection', (socket) => {
    logger.info(`WebSocket client connected: ${socket.id}`);

    // Join terminal session
    socket.on('terminal:join', async (data) => {
      try {
        let { sessionId, projectId } = data;
        let session = terminalService.getSession(sessionId);

        // If session doesn't exist, try to create it for the project
        if (!session && projectId) {
          logger.info(`Session ${sessionId} not found, attempting to create for project ${projectId}`);
          try {
            const newSession = await terminalService.createSession({
              projectId,
              shell: 'bash',
              cwd: '/workspace'
            });

            // Update the session ID mapping if needed
            session = terminalService.getSession(newSession.sessionId);
            if (session) {
              socket.emit('terminal:session-created', {
                oldSessionId: sessionId,
                newSessionId: newSession.sessionId
              });
              sessionId = newSession.sessionId;
            }
          } catch (createError) {
            logger.error('Failed to create terminal session on join:', createError);
            socket.emit('terminal:error', {
              error: `Failed to create terminal session: ${createError.message}`
            });
            return;
          }
        }

        if (!session) {
          socket.emit('terminal:error', { error: 'Terminal session not found and could not be created' });
          return;
        }

        const room = `terminal:${sessionId}`;
        socket.join(room);

        // Verify the client joined the room
        const clientsInRoom = io.sockets.adapter.rooms.get(room);
        logger.info(`[DEBUG] Client ${socket.id} joined room ${room}. Clients in room: ${clientsInRoom ? clientsInRoom.size : 0}`);

        // Send current buffer to newly connected client
        if (session.buffer && session.buffer.length > 0) {
          logger.info(`[DEBUG] Sending buffered output to client ${socket.id}, length: ${session.buffer.length}`);
          socket.emit('terminal:output', {
            sessionId,
            data: session.buffer
          });
        } else {
          // Send initial welcome message if no buffer exists
          const welcomeMessage = '\x1b[32mTerminal connected successfully\x1b[0m\r\n/workspace # ';
          socket.emit('terminal:output', {
            sessionId,
            data: welcomeMessage
          });
        }

        socket.emit('terminal:joined', { sessionId, projectId: session.projectId });
        logger.info(`Client ${socket.id} joined terminal session ${sessionId} for project ${session.projectId}`);
      } catch (error) {
        logger.error('Failed to join terminal session:', error);
        socket.emit('terminal:error', { error: error.message });
      }
    });

    // Handle terminal input
    socket.on('terminal:input', async (data) => {
      try {
        const { sessionId, data: inputData } = data;

        // Validate input data
        if (!sessionId) {
          socket.emit('terminal:error', { error: 'Session ID is required' });
          return;
        }

        if (inputData === undefined || inputData === null) {
          logger.warn(`Received undefined/null input data for session ${sessionId}`);
          return; // Silently ignore undefined/null input
        }

        // Convert to string if not already
        const stringInput = typeof inputData === 'string' ? inputData : String(inputData);

        logger.info(`[DEBUG] Received terminal input for session ${sessionId}: ${JSON.stringify(stringInput)}`);
        const result = await terminalService.writeToSession(sessionId, stringInput);
        logger.info(`[DEBUG] Write result for session ${sessionId}:`, result);
      } catch (error) {
        logger.error('Failed to write terminal input:', error);
        socket.emit('terminal:error', { error: error.message });
      }
    });

    // Handle terminal resize
    socket.on('terminal:resize', async (data) => {
      try {
        const { sessionId, cols, rows } = data;
        await terminalService.resizeSession(sessionId, cols, rows);
      } catch (error) {
        logger.error('Failed to resize terminal:', error);
        socket.emit('terminal:error', { error: error.message });
      }
    });

    // Leave terminal session
    socket.on('terminal:leave', (data) => {
      try {
        const { sessionId } = data;
        socket.leave(`terminal:${sessionId}`);
        logger.info(`Client ${socket.id} left terminal session ${sessionId}`);
      } catch (error) {
        logger.error('Failed to leave terminal session:', error);
      }
    });

    // === COLLABORATION EVENT HANDLERS ===

    // Join collaboration room
    socket.on('collab:join', async (data) => {
      try {
        const { projectId, filePath, nickname } = data;

        if (!projectId || !filePath) {
          socket.emit('collab:error', { error: 'Project ID and file path are required' });
          return;
        }

        const result = collaborationService.joinRoom(socket.id, projectId, filePath, nickname);

        if (result.success) {
          // Join socket room for broadcasting
          socket.join(`collab:${result.roomId}`);

          // Send success response to joining user
          socket.emit('collab:joined', {
            roomId: result.roomId,
            userId: result.userId,
            userInfo: result.userInfo,
            collaborators: result.collaborators,
            document: result.document
          });

          // Notify other users in the room
          socket.to(`collab:${result.roomId}`).emit('collab:user-joined', {
            user: result.userInfo,
            collaborators: result.collaborators
          });

          logger.info(`User ${result.userId} joined collaboration room ${result.roomId}`);
        } else {
          socket.emit('collab:error', { error: result.error });
        }
      } catch (error) {
        logger.error('Failed to join collaboration room:', error);
        socket.emit('collab:error', { error: error.message });
      }
    });

    // Leave collaboration room
    socket.on('collab:leave', async (data) => {
      try {
        const result = collaborationService.leaveRoom(socket.id);

        if (result.success && result.roomId) {
          // Leave socket room
          socket.leave(`collab:${result.roomId}`);

          // Notify other users in the room
          socket.to(`collab:${result.roomId}`).emit('collab:user-left', {
            userId: result.userId,
            collaborators: result.collaborators
          });

          logger.info(`User ${result.userId} left collaboration room ${result.roomId}`);
        }

        socket.emit('collab:left', { success: result.success });
      } catch (error) {
        logger.error('Failed to leave collaboration room:', error);
        socket.emit('collab:error', { error: error.message });
      }
    });

    // Document content change
    socket.on('collab:document-change', async (data) => {
      try {
        const { content, changes } = data;

        const result = collaborationService.updateDocument(socket.id, content, changes);

        if (result.success) {
          // Broadcast to other users in the room (exclude sender)
          socket.to(`collab:${result.roomId}`).emit('collab:document-changed', {
            userId: result.userId,
            content: result.content,
            changes: result.changes
          });

          logger.debug(`Document updated in room ${result.roomId} by user ${result.userId}`);
        } else {
          socket.emit('collab:error', { error: result.error });
        }
      } catch (error) {
        logger.error('Failed to update document:', error);
        socket.emit('collab:error', { error: error.message });
      }
    });

    // Cursor position change
    socket.on('collab:cursor-change', async (data) => {
      try {
        const { cursor, selection } = data;

        const result = collaborationService.updateCursor(socket.id, cursor, selection);

        if (result.success) {
          // Broadcast to other users in the room (exclude sender)
          socket.to(`collab:${result.roomId}`).emit('collab:cursor-changed', {
            userId: result.userId,
            cursor: result.cursor,
            selection: result.selection,
            cursors: result.cursors
          });
        } else {
          socket.emit('collab:error', { error: result.error });
        }
      } catch (error) {
        logger.error('Failed to update cursor:', error);
        socket.emit('collab:error', { error: error.message });
      }
    });

    // Update nickname
    socket.on('collab:update-nickname', async (data) => {
      try {
        const { nickname } = data;

        if (!nickname || nickname.trim().length === 0) {
          socket.emit('collab:error', { error: 'Nickname cannot be empty' });
          return;
        }

        const result = collaborationService.updateNickname(socket.id, nickname.trim());

        if (result.success) {
          // Notify all users in the room (including sender)
          io.to(`collab:${result.roomId}`).emit('collab:nickname-updated', {
            userId: result.userId,
            nickname: result.nickname,
            collaborators: result.collaborators
          });

          logger.info(`User ${result.userId} updated nickname to ${result.nickname}`);
        } else {
          socket.emit('collab:error', { error: result.error });
        }
      } catch (error) {
        logger.error('Failed to update nickname:', error);
        socket.emit('collab:error', { error: error.message });
      }
    });

    // Get room info
    socket.on('collab:get-room-info', async (data) => {
      try {
        const userSession = collaborationService.getUserSession(socket.id);

        if (userSession) {
          const room = collaborationService.getRoom(userSession.roomId);
          if (room) {
            socket.emit('collab:room-info', {
              roomId: userSession.roomId,
              collaborators: collaborationService.getRoomCollaborators(userSession.roomId),
              document: room.document
            });
          } else {
            socket.emit('collab:error', { error: 'Room not found' });
          }
        } else {
          socket.emit('collab:error', { error: 'Not in any collaboration room' });
        }
      } catch (error) {
        logger.error('Failed to get room info:', error);
        socket.emit('collab:error', { error: error.message });
      }
    });

    socket.on('disconnect', () => {
      logger.info(`WebSocket client disconnected: ${socket.id}`);

      // Clean up collaboration session
      try {
        const result = collaborationService.leaveRoom(socket.id);
        if (result.success && result.roomId) {
          // Notify other users in the room
          socket.to(`collab:${result.roomId}`).emit('collab:user-left', {
            userId: result.userId,
            collaborators: result.collaborators
          });
        }
      } catch (error) {
        logger.error('Failed to cleanup collaboration session on disconnect:', error);
      }
    });
  });

  // Listen for terminal output events and broadcast to connected clients
  const { eventBus, EventTypes } = require('../../shared');

  eventBus.subscribe(EventTypes.TERMINAL_OUTPUT, (eventData) => {
    try {
      const { sessionId, data: output } = eventData.data || eventData;

      if (!sessionId || !output) {
        logger.warn('[WEBSOCKET] Invalid terminal output event data:', eventData);
        return;
      }

      logger.debug(`[WEBSOCKET] Broadcasting terminal output to room terminal:${sessionId}, data length: ${output.length}`);

      // Broadcast to all clients in the terminal session room
      const room = `terminal:${sessionId}`;
      const clientsInRoom = io.sockets.adapter.rooms.get(room);
      logger.debug(`[WEBSOCKET] Clients in room ${room}: ${clientsInRoom ? clientsInRoom.size : 0}`);

      if (clientsInRoom && clientsInRoom.size > 0) {
        io.to(room).emit('terminal:output', {
          sessionId,
          data: output
        });
        logger.debug(`[WEBSOCKET] Successfully broadcasted output to ${clientsInRoom.size} clients`);
      } else {
        logger.debug(`[WEBSOCKET] No clients in room ${room} to broadcast to`);
      }
    } catch (error) {
      logger.error('Error broadcasting terminal output:', error);
    }
  });

  eventBus.subscribe(EventTypes.TERMINAL_SESSION_ENDED, (data) => {
    const { sessionId, exitCode } = data;
    io.to(`terminal:${sessionId}`).emit('terminal:ended', {
      sessionId,
      exitCode
    });
  });
}

module.exports = {
  setupTerminalRoutes,
  setupTerminalWebSocket
};
