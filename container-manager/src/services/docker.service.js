const Docker = require('dockerode');
const logger = require('../utils/logger');
const os = require('os');
const { eventBus, EventTypes, configManager, errorHandler } = require('../../shared');

function initializeDocker() {
  // Determine the correct socket path based on the operating system
  const isWindows = os.platform() === 'win32';
  const dockerOptions = isWindows
    ? { socketPath: '//./pipe/docker_engine' }
    : { socketPath: '/var/run/docker.sock' };

  try {
    const docker = new Docker(dockerOptions);
    // Test the connection with error handling
    return errorHandler.executeWithProtection('docker.initialize', async () => {
      await docker.ping();
      logger.info('Docker connection initialized successfully');

      // Publish initialization event
      await eventBus.publish(EventTypes.SYSTEM_RECOVERY, {
        component: 'container-manager',
        action: 'docker_initialized',
        message: 'Docker connection established successfully'
      });

      return docker;
    }, {
      component: 'container-manager',
      circuitBreaker: { failureThreshold: 3, resetTimeout: 30000 },
      retry: { maxRetries: 3, baseDelay: 2000 },
      fallback: async () => {
        logger.error('Docker connection failed. Please ensure Docker is running.');
        throw new Error('Docker is not available. Please start Docker and try again.');
      }
    });
  } catch (error) {
    logger.error('Failed to initialize Docker connection:', error);
    throw error;
  }
}

class DockerService {
  constructor(docker) {
    this.docker = docker;
  }

  async createContainer(config) {
    return await errorHandler.executeWithProtection('docker.createContainer', async () => {
      // Validate that the Docker image exists
      try {
        await this.docker.getImage(config.image).inspect();
        logger.info(`Docker image ${config.image} found and ready`);
      } catch (error) {
        logger.error(`Docker image ${config.image} not found:`, error.message);
        throw new Error(`Docker image '${config.image}' not found. Please ensure the image is built and available. Run 'build-docker-images.bat' to build language-specific images.`);
      }

      // Get configuration defaults
      const defaults = configManager.get('containerManager') || {
        defaultMemory: 512 * 1024 * 1024, // 512MB
        defaultCpuShares: 1024
      };

      // Extract subdomain from environment variables or labels
      const subdomain = this.extractSubdomain(config);

      // Prepare exposed ports
      const exposedPorts = {};
      const portBindings = {};

      if (config.ports && Array.isArray(config.ports)) {
        config.ports.forEach(port => {
          const portKey = `${port}/tcp`;
          exposedPorts[portKey] = {};
          portBindings[portKey] = [{ HostPort: '0' }]; // Let Docker assign random host port
        });
      }

      // Prepare volumes
      const binds = [];
      if (config.volumes && Array.isArray(config.volumes)) {
        binds.push(...config.volumes);
      }

      // Get language-specific setup commands
      const setupCommands = this.getLanguageSetupCommands(config.language || 'general');

      // Create container with enhanced configuration
      const containerConfig = {
        Image: config.image,
        name: config.name,
        ExposedPorts: exposedPorts,
        Labels: {
          ...config.labels,
          'cloud-ide.subdomain': subdomain,
          'cloud-ide.enabled': 'true',
          'cloud-ide.project': config.projectId || 'unknown',
          'cloud-ide.language': config.language || 'general'
        },
        WorkingDir: '/workspace',
        Cmd: ['tail', '-f', '/dev/null'], // Keep container running
        HostConfig: {
          PortBindings: portBindings,
          Binds: binds,
          Memory: config.memory || defaults.defaultMemory,
          MemorySwap: -1,
          CpuShares: config.cpuShares || defaults.defaultCpuShares,
          RestartPolicy: {
            Name: 'unless-stopped'
          },
          AutoRemove: false // Keep containers for debugging
        },
        Env: this.formatEnvironmentVariables({
          ...config.environment,
          LANGUAGE: config.language || 'general'
        }),
        WorkingDir: config.workingDir || '/workspace',
        Cmd: config.command || ['tail', '-f', '/dev/null'], // Simple keep-alive command
        Tty: true,
        OpenStdin: true,
        StdinOnce: false
      };

      logger.info(`Creating container with config:`, {
        name: config.name,
        image: config.image,
        volumes: binds,
        ports: config.ports
      });

      const container = await this.docker.createContainer(containerConfig);
      await container.start();

      // Wait a moment for container to fully start
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Get container details for event
      const containerInfo = await container.inspect();
      const ipAddress = this.extractContainerIp(containerInfo);
      const port = this.extractContainerPort(containerInfo);

      logger.info(`Container ${config.name} created and started successfully`, {
        containerId: container.id,
        ipAddress,
        port
      });

      // Publish container created event
      await eventBus.publish(EventTypes.CONTAINER_CREATED, {
        containerId: container.id,
        name: config.name,
        image: config.image,
        subdomain,
        port,
        ipAddress
      });

      // Publish container started event
      await eventBus.publish(EventTypes.CONTAINER_STARTED, {
        containerId: container.id,
        name: config.name,
        subdomain,
        port,
        ipAddress
      });

      return {
        id: container.id,
        name: config.name,
        image: config.image,
        subdomain,
        port,
        ipAddress,
        container
      };
    }, {
      component: 'container-manager',
      operation: 'createContainer',
      circuitBreaker: { failureThreshold: 3, resetTimeout: 60000 },
      retry: { maxRetries: 2, baseDelay: 1000 }
    });
  }

  async stopContainer(containerId) {
    return await errorHandler.executeWithProtection('docker.stopContainer', async () => {
      const container = this.docker.getContainer(containerId);

      // Get container info before stopping
      const containerInfo = await container.inspect();
      const subdomain = containerInfo.Config.Labels[configManager.get('labels.subdomainLabel')];

      await container.stop();
      logger.info(`Container ${containerId} stopped successfully`);

      // Publish container stopped event
      await eventBus.publish(EventTypes.CONTAINER_STOPPED, {
        containerId,
        name: containerInfo.Name.replace('/', ''),
        subdomain
      });

    }, {
      component: 'container-manager',
      operation: 'stopContainer',
      retry: { maxRetries: 2, baseDelay: 1000 }
    });
  }

  async restartContainer(containerId) {
    return await errorHandler.executeWithProtection('docker.restartContainer', async () => {
      const container = this.docker.getContainer(containerId);

      // Check if container exists
      const containerInfo = await container.inspect();
      logger.info(`Restarting container ${containerId} (${containerInfo.Name})`);

      // Restart the container
      await container.restart();

      // Wait a moment for container to fully start
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Get updated container info
      const updatedInfo = await container.inspect();
      const ipAddress = this.extractContainerIp(updatedInfo);
      const port = this.extractContainerPort(updatedInfo);
      const subdomain = updatedInfo.Config.Labels['cloud-ide.subdomain'];

      logger.info(`Container ${containerId} restarted successfully`, {
        containerId,
        ipAddress,
        port,
        subdomain
      });

      // Publish container restarted event
      await eventBus.publish(EventTypes.CONTAINER_STARTED, {
        containerId,
        name: updatedInfo.Name.replace('/', ''),
        image: updatedInfo.Config.Image,
        subdomain,
        port,
        ipAddress
      });

      return {
        id: containerId,
        ipAddress,
        port,
        subdomain
      };

    }, {
      component: 'container-manager',
      operation: 'restartContainer',
      retry: { maxRetries: 2, baseDelay: 1000 }
    });
  }

  async removeContainer(containerId) {
    return await errorHandler.executeWithProtection('docker.removeContainer', async () => {
      const container = this.docker.getContainer(containerId);

      try {
        // Get container info before removing
        const containerInfo = await container.inspect();
        const subdomain = containerInfo.Config.Labels['cloud-ide.subdomain'] ||
                         containerInfo.Config.Labels[configManager.get('labels.subdomainLabel')];

        // Stop container if it's running
        if (containerInfo.State.Running) {
          logger.info(`Stopping container ${containerId} before removal`);
          await container.stop({ t: 10 }); // 10 second timeout
          logger.info(`Stopped container ${containerId} before removal`);
        }

        // Remove the container with force
        await container.remove({ force: true });
        logger.info(`Container ${containerId} removed successfully`);

        // Publish container removed event
        await eventBus.publish(EventTypes.CONTAINER_REMOVED, {
          containerId,
          name: containerInfo.Name.replace('/', ''),
          subdomain
        });

      } catch (error) {
        if (error.statusCode === 404) {
          logger.warn(`Container ${containerId} not found, already removed`);
          return;
        }
        logger.error(`Error removing container ${containerId}:`, error.message);
        throw error;
      }

    }, {
      component: 'container-manager',
      operation: 'removeContainer',
      retry: { maxRetries: 2, baseDelay: 1000 }
    });
  }

  async getContainerStats(containerId) {
    try {
      const container = this.docker.getContainer(containerId);

      // Get container info first to ensure it's running
      const containerInfo = await container.inspect();
      if (!containerInfo.State.Running) {
        throw new Error('Container is not running');
      }

      const stats = await container.stats({ stream: false });

      return {
        cpu: stats.cpu_stats,
        memory: stats.memory_stats,
        network: stats.networks,
        timestamp: stats.read
      };
    } catch (error) {
      logger.error(`Error getting stats for container ${containerId}:`, error);
      throw error;
    }
  }

  async listContainers() {
    return await errorHandler.executeWithProtection('docker.listContainers', async () => {
      const containers = await this.docker.listContainers({ all: true });
      return containers.map(container => ({
        id: container.Id,
        name: container.Names[0].replace('/', ''),
        image: container.Image,
        status: container.Status,
        created: container.Created,
        subdomain: container.Labels[configManager.get('labels.subdomainLabel')] || null
      }));
    }, {
      component: 'container-manager',
      operation: 'listContainers',
      retry: { maxRetries: 2, baseDelay: 500 }
    });
  }

  /**
   * Format environment variables for Docker container
   */
  formatEnvironmentVariables(environment) {
    if (!environment) {
      return [];
    }

    if (Array.isArray(environment)) {
      return environment;
    }

    if (typeof environment === 'object') {
      return Object.entries(environment).map(([key, value]) => `${key}=${value}`);
    }

    return [];
  }

  /**
   * Execute a command in a container
   */
  async executeCommand(containerId, command, options = {}) {
    try {
      const container = this.docker.getContainer(containerId);

      // Check if container is running
      const containerInfo = await container.inspect();
      if (!containerInfo || !containerInfo.State || !containerInfo.State.Running) {
        throw new Error('Container is not running');
      }

      const {
        shell = 'sh',
        workingDir = '/workspace',
        environment = {},
        timeout = 30000, // 30 seconds default
        input = null
      } = options;

      // Create exec instance
      const exec = await container.exec({
        Cmd: [shell, '-c', command],
        AttachStdin: !!input,
        AttachStdout: true,
        AttachStderr: true,
        WorkingDir: workingDir,
        Env: this.formatEnvironmentVariables(environment)
      });

      // Start execution with timeout
      const stream = await exec.start({
        Detach: false,
        hijack: !!input,
        stdin: !!input
      });

      return new Promise((resolve, reject) => {
        let output = '';
        let errorOutput = '';

        const timeoutId = setTimeout(() => {
          reject(new Error(`Command execution timed out after ${timeout}ms`));
        }, timeout);

        // Write input if provided
        if (input) {
          stream.write(input);
          stream.end();
        }

        stream.on('data', (chunk) => {
          const data = chunk.toString();
          // Docker multiplexes stdout/stderr, we'll combine them
          output += data;
        });

        stream.on('end', async () => {
          clearTimeout(timeoutId);

          try {
            // Get exit code
            const execInfo = await exec.inspect();
            const exitCode = execInfo.ExitCode;

            resolve({
              output: output.trim(),
              exitCode,
              success: exitCode === 0
            });
          } catch (error) {
            reject(error);
          }
        });

        stream.on('error', (error) => {
          clearTimeout(timeoutId);
          reject(error);
        });
      });
    } catch (error) {
      console.error('Failed to execute command in container:', error);
      throw error;
    }
  }

  /**
   * Check if container is running
   */
  async isContainerRunning(containerId) {
    try {
      const container = this.docker.getContainer(containerId);
      const containerInfo = await container.inspect();
      return containerInfo && containerInfo.State && containerInfo.State.Running;
    } catch (error) {
      console.error('Failed to check container status:', error);
      return false;
    }
  }

  /**
   * Get container information
   */
  async getContainerInfo(containerId) {
    try {
      const container = this.docker.getContainer(containerId);
      return await container.inspect();
    } catch (error) {
      console.error('Failed to get container info:', error);
      throw error;
    }
  }

  /**
   * Get language-specific setup commands for container initialization
   * Note: All packages are pre-installed in Docker images, so no runtime setup needed
   */
  getLanguageSetupCommands(language) {
    // Return empty array since all packages are pre-installed in Docker images
    // This prevents permission issues with apk/npm commands running as non-root user
    return [];
  }

  /**
   * Get language-specific run command
   */
  getRunCommand(language, projectType, fileName = null) {
    const commands = {
      javascript: {
        web: fileName ? `node ${fileName}` : 'npm start',
        api: fileName ? `node ${fileName}` : 'npm start',
        general: fileName ? `node ${fileName}` : 'node index.js'
      },
      python: {
        web: fileName ? `python ${fileName}` : 'python main.py',
        api: fileName ? `python ${fileName}` : 'python main.py',
        general: fileName ? `python ${fileName}` : 'python main.py'
      },
      java: {
        web: fileName ? `javac ${fileName} && java ${fileName.replace('.java', '')}` : 'mvn compile exec:java -Dexec.mainClass="Main"',
        api: fileName ? `javac ${fileName} && java ${fileName.replace('.java', '')}` : 'mvn compile exec:java -Dexec.mainClass="Main"',
        general: fileName ? `javac ${fileName} && java ${fileName.replace('.java', '')}` : 'javac *.java && java Main'
      },
      vue: {
        web: 'npm run serve',
        general: 'npm run serve'
      },
      react: {
        web: 'npm start',
        general: 'npm start'
      },
      typescript: {
        web: fileName ? `tsc ${fileName} && node ${fileName.replace('.ts', '.js')}` : 'npm start',
        api: fileName ? `tsc ${fileName} && node ${fileName.replace('.ts', '.js')}` : 'npm start',
        general: fileName ? `tsc ${fileName} && node ${fileName.replace('.ts', '.js')}` : 'npm start'
      },
      html: {
        web: fileName ? `echo "Open ${fileName} in browser"` : 'echo "Open index.html in browser"',
        general: fileName ? `echo "Open ${fileName} in browser"` : 'echo "Open index.html in browser"'
      },
      css: {
        web: 'echo "CSS files are used with HTML. Open your HTML file in browser"',
        general: 'echo "CSS files are used with HTML. Open your HTML file in browser"'
      },
      web: {
        web: 'npm start',
        general: 'npm start'
      }
    };

    return commands[language]?.[projectType] || commands[language]?.general || `echo "No run command defined for ${language}"`;
  }

  /**
   * Extract subdomain from container configuration
   */
  extractSubdomain(config) {
    // Use provided subdomain if available
    if (config.subdomain) {
      return config.subdomain;
    }

    // Check environment variables
    if (config.environment) {
      // Handle both object and array formats
      if (Array.isArray(config.environment)) {
        const virtualHost = config.environment.find(env => env.startsWith('VIRTUAL_HOST='));
        if (virtualHost) {
          const hostname = virtualHost.split('=')[1];
          return hostname.split('.')[0]; // Extract subdomain part
        }
      } else if (typeof config.environment === 'object') {
        if (config.environment.VIRTUAL_HOST) {
          const hostname = config.environment.VIRTUAL_HOST;
          return hostname.split('.')[0]; // Extract subdomain part
        }
      }
    }

    // Check labels
    if (config.labels && config.labels['cloud-ide.subdomain']) {
      return config.labels['cloud-ide.subdomain'];
    }

    // Fallback to container name
    return config.name || 'unknown';
  }

  /**
   * Extract container IP address from container info
   */
  extractContainerIp(containerInfo) {
    const networks = containerInfo.NetworkSettings.Networks;
    const networkNames = Object.keys(networks);

    // Prefer ide-network if available
    const ideNetwork = networkNames.find(name => name.includes('ide-network'));
    if (ideNetwork && networks[ideNetwork].IPAddress) {
      return networks[ideNetwork].IPAddress;
    }

    // Fallback to first available network
    for (const networkName of networkNames) {
      if (networks[networkName].IPAddress) {
        return networks[networkName].IPAddress;
      }
    }

    return '127.0.0.1'; // Fallback
  }

  /**
   * Extract container port from container info
   */
  extractContainerPort(containerInfo) {
    const exposedPorts = containerInfo.Config.ExposedPorts;
    if (exposedPorts) {
      const firstPort = Object.keys(exposedPorts)[0];
      if (firstPort) {
        return parseInt(firstPort.split('/')[0], 10);
      }
    }
    return 80; // Default port
  }

  /**
   * Create a terminal session in a container
   */
  async createTerminalSession(containerId, options = {}) {
    try {
      const container = this.docker.getContainer(containerId);

      // Check if container is running
      const containerInfo = await container.inspect();
      if (!containerInfo || !containerInfo.State || !containerInfo.State.Running) {
        throw new Error('Container is not running');
      }

      const {
        shell = '/bin/bash',
        workingDir = '/workspace',
        environment = {}
      } = options;

      // Create exec instance for terminal
      const exec = await container.exec({
        Cmd: [shell],
        AttachStdin: true,
        AttachStdout: true,
        AttachStderr: true,
        Tty: true,
        WorkingDir: workingDir,
        User: 'developer', // Use the developer user created in the container
        Env: this.formatEnvironmentVariables(environment)
      });

      return exec;
    } catch (error) {
      logger.error('Failed to create terminal session:', error);
      throw error;
    }
  }

  /**
   * Start terminal session and return stream
   */
  async startTerminalSession(exec) {
    try {
      const stream = await exec.start({
        hijack: true,
        stdin: true
      });

      return stream;
    } catch (error) {
      logger.error('Failed to start terminal session:', error);
      throw error;
    }
  }

  /**
   * Health check for container service
   */
  async healthCheck() {
    try {
      await this.docker.ping();
      return {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        dockerConnected: true
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        dockerConnected: false,
        error: error.message
      };
    }
  }
}



module.exports = {
  initializeDocker,
  DockerService
};