const logger = require('../utils/logger');
const { v4: uuidv4 } = require('uuid');

class CollaborationService {
  constructor() {
    // Store active collaboration sessions
    // Structure: { roomId: { users: Map, document: string, cursors: Map, lastActivity: Date } }
    this.rooms = new Map();
    
    // Store user sessions
    // Structure: { socketId: { userId, nickname, roomId, lastActivity } }
    this.userSessions = new Map();
    
    // Cleanup interval for inactive rooms
    this.cleanupInterval = setInterval(() => {
      this.cleanupInactiveRooms();
    }, 60000); // Check every minute
  }

  /**
   * Generate a room ID based on project and file path
   */
  generateRoomId(projectId, filePath) {
    return `${projectId}:${filePath}`;
  }

  /**
   * Generate a unique user ID for a session
   */
  generateUserId() {
    return uuidv4();
  }

  /**
   * Join a collaboration room
   */
  joinRoom(socketId, projectId, filePath, nickname = null) {
    try {
      const roomId = this.generateRoomId(projectId, filePath);
      const userId = this.generateUserId();
      
      // Initialize room if it doesn't exist
      if (!this.rooms.has(roomId)) {
        this.rooms.set(roomId, {
          users: new Map(),
          document: '', // Will be populated when first user joins
          cursors: new Map(),
          lastActivity: new Date(),
          projectId,
          filePath
        });
        logger.info(`Created new collaboration room: ${roomId}`);
      }

      const room = this.rooms.get(roomId);
      
      // Add user to room
      const userInfo = {
        userId,
        socketId,
        nickname: nickname || this.generateSimpleName(room.users.size),
        joinedAt: new Date(),
        cursor: { line: 0, column: 0 },
        selection: null,
        color: this.generateUserColor(room.users.size)
      };

      room.users.set(userId, userInfo);
      room.lastActivity = new Date();

      // Store user session
      this.userSessions.set(socketId, {
        userId,
        nickname: userInfo.nickname,
        roomId,
        lastActivity: new Date()
      });

      logger.info(`User ${userId} (${userInfo.nickname}) joined room ${roomId}`);

      return {
        success: true,
        roomId,
        userId,
        userInfo,
        collaborators: this.getRoomCollaborators(roomId),
        document: room.document
      };
    } catch (error) {
      logger.error('Failed to join collaboration room:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Leave a collaboration room
   */
  leaveRoom(socketId) {
    try {
      const userSession = this.userSessions.get(socketId);
      if (!userSession) {
        return { success: true }; // Already not in any room
      }

      const { userId, roomId } = userSession;
      const room = this.rooms.get(roomId);

      if (room) {
        room.users.delete(userId);
        room.cursors.delete(userId);
        room.lastActivity = new Date();

        logger.info(`User ${userId} left room ${roomId}`);

        // Clean up empty room
        if (room.users.size === 0) {
          this.rooms.delete(roomId);
          logger.info(`Deleted empty collaboration room: ${roomId}`);
        }
      }

      this.userSessions.delete(socketId);

      return {
        success: true,
        roomId,
        userId,
        collaborators: room ? this.getRoomCollaborators(roomId) : []
      };
    } catch (error) {
      logger.error('Failed to leave collaboration room:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Update document content
   */
  updateDocument(socketId, content, changes = null) {
    try {
      const userSession = this.userSessions.get(socketId);
      if (!userSession) {
        return { success: false, error: 'User not in any room' };
      }

      const { userId, roomId } = userSession;
      const room = this.rooms.get(roomId);

      if (!room) {
        return { success: false, error: 'Room not found' };
      }

      // Update document content
      room.document = content;
      room.lastActivity = new Date();
      userSession.lastActivity = new Date();

      logger.debug(`Document updated in room ${roomId} by user ${userId}`);

      return {
        success: true,
        roomId,
        userId,
        content,
        changes,
        collaborators: this.getRoomCollaborators(roomId)
      };
    } catch (error) {
      logger.error('Failed to update document:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Update cursor position
   */
  updateCursor(socketId, cursor, selection = null) {
    try {
      const userSession = this.userSessions.get(socketId);
      if (!userSession) {
        return { success: false, error: 'User not in any room' };
      }

      const { userId, roomId } = userSession;
      const room = this.rooms.get(roomId);

      if (!room) {
        return { success: false, error: 'Room not found' };
      }

      const user = room.users.get(userId);
      if (user) {
        user.cursor = cursor;
        user.selection = selection;
        room.cursors.set(userId, { cursor, selection, color: user.color, nickname: user.nickname });
        room.lastActivity = new Date();
        userSession.lastActivity = new Date();
      }

      return {
        success: true,
        roomId,
        userId,
        cursor,
        selection,
        cursors: Array.from(room.cursors.entries()).map(([id, data]) => ({
          userId: id,
          ...data
        }))
      };
    } catch (error) {
      logger.error('Failed to update cursor:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Update user nickname
   */
  updateNickname(socketId, nickname) {
    try {
      const userSession = this.userSessions.get(socketId);
      if (!userSession) {
        return { success: false, error: 'User not in any room' };
      }

      const { userId, roomId } = userSession;
      const room = this.rooms.get(roomId);

      if (!room) {
        return { success: false, error: 'Room not found' };
      }

      const user = room.users.get(userId);
      if (user) {
        user.nickname = nickname;
        userSession.nickname = nickname;
        
        // Update cursor info if exists
        if (room.cursors.has(userId)) {
          room.cursors.get(userId).nickname = nickname;
        }
      }

      logger.info(`User ${userId} changed nickname to ${nickname} in room ${roomId}`);

      return {
        success: true,
        roomId,
        userId,
        nickname,
        collaborators: this.getRoomCollaborators(roomId)
      };
    } catch (error) {
      logger.error('Failed to update nickname:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Get room collaborators
   */
  getRoomCollaborators(roomId) {
    const room = this.rooms.get(roomId);
    if (!room) return [];

    return Array.from(room.users.values()).map(user => ({
      userId: user.userId,
      nickname: user.nickname,
      color: user.color,
      joinedAt: user.joinedAt,
      cursor: user.cursor,
      selection: user.selection
    }));
  }

  /**
   * Generate user color based on index
   */
  generateUserColor(index) {
    const colors = [
      '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
      '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9',
      '#FF9F43', '#10AC84', '#5F27CD', '#00D2D3', '#FF3838',
      '#2E86AB', '#A23B72', '#F18F01', '#C73E1D', '#6C5CE7'
    ];
    return colors[index % colors.length];
  }

  /**
   * Generate simple user names
   */
  generateSimpleName(index) {
    return `User ${index + 1}`;
  }

  /**
   * Get user session info
   */
  getUserSession(socketId) {
    return this.userSessions.get(socketId);
  }

  /**
   * Get room info
   */
  getRoom(roomId) {
    return this.rooms.get(roomId);
  }

  /**
   * Clean up inactive rooms
   */
  cleanupInactiveRooms() {
    const now = new Date();
    const inactiveThreshold = 30 * 60 * 1000; // 30 minutes

    for (const [roomId, room] of this.rooms.entries()) {
      if (now - room.lastActivity > inactiveThreshold) {
        this.rooms.delete(roomId);
        logger.info(`Cleaned up inactive collaboration room: ${roomId}`);
      }
    }
  }

  /**
   * Cleanup on service shutdown
   */
  cleanup() {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    this.rooms.clear();
    this.userSessions.clear();
  }
}

module.exports = CollaborationService;
