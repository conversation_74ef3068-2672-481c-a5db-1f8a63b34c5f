const fs = require('fs').promises;
const path = require('path');
const { execSync } = require('child_process');
const logger = require('../utils/logger');
const { config<PERSON>anager, errorHandler, eventBus, EventTypes } = require('../../shared');

class FileSystemService {
  constructor(docker) {
    this.docker = docker;
    this.projectsBasePath = process.env.PROJECTS_PATH || '/tmp/cloud-ide-projects';
    this.initializeProjectsDirectory();
  }

  async initializeProjectsDirectory() {
    try {
      await fs.mkdir(this.projectsBasePath, { recursive: true });
      logger.info(`Projects directory initialized at: ${this.projectsBasePath}`);
    } catch (error) {
      logger.error('Failed to initialize projects directory:', error);
    }
  }

  /**
   * Get the full path for a project
   */
  getProjectPath(projectId) {
    return path.join(this.projectsBasePath, projectId);
  }

  /**
   * Ensure project directory exists
   */
  async ensureProjectDirectory(projectId) {
    const projectPath = this.getProjectPath(projectId);
    try {
      await fs.mkdir(projectPath, { recursive: true });
      logger.debug(`Project directory ensured: ${projectPath}`);
      return projectPath;
    } catch (error) {
      logger.error(`Failed to create project directory ${projectPath}:`, error);
      throw error;
    }
  }

  /**
   * Validate and sanitize file paths to prevent directory traversal
   */
  validatePath(filePath, projectId) {
    const projectPath = this.getProjectPath(projectId);

    // Handle root path and normalize
    let normalizedPath = filePath || '/';
    if (normalizedPath === '/') {
      normalizedPath = '.';
    } else if (normalizedPath.startsWith('/')) {
      normalizedPath = normalizedPath.substring(1);
    }

    const fullPath = path.resolve(projectPath, normalizedPath);

    // Ensure the path is within the project directory
    if (!fullPath.startsWith(projectPath)) {
      throw new Error('Invalid file path: Path traversal not allowed');
    }

    return fullPath;
  }

  /**
   * List directory contents
   */
  async listDirectory(projectId, dirPath = '/') {
    return await errorHandler.executeWithProtection('filesystem.listDirectory', async () => {
      const projectPath = this.getProjectPath(projectId);
      const fullPath = this.validatePath(dirPath, projectId);

      try {
        const stats = await fs.stat(fullPath);
        if (!stats.isDirectory()) {
          throw new Error('Path is not a directory');
        }

        const items = await fs.readdir(fullPath, { withFileTypes: true });
        const files = [];

        for (const item of items) {
          const itemPath = path.join(fullPath, item.name);
          const relativePath = path.relative(projectPath, itemPath);
          const itemStats = await fs.stat(itemPath);

          files.push({
            name: item.name,
            path: '/' + relativePath.replace(/\\/g, '/'),
            type: item.isDirectory() ? 'directory' : 'file',
            size: item.isFile() ? itemStats.size : null,
            modified: itemStats.mtime,
            created: itemStats.birthtime,
            permissions: (itemStats.mode & parseInt('777', 8)).toString(8)
          });
        }

        // Sort: directories first, then files, both alphabetically
        files.sort((a, b) => {
          if (a.type !== b.type) {
            return a.type === 'directory' ? -1 : 1;
          }
          return a.name.localeCompare(b.name);
        });

        return {
          path: '/' + path.relative(projectPath, fullPath).replace(/\\/g, '/'),
          files
        };
      } catch (error) {
        if (error.code === 'ENOENT') {
          throw new Error('Directory not found');
        }
        throw error;
      }
    }, {
      component: 'filesystem',
      operation: 'listDirectory',
      retry: { maxRetries: 2, baseDelay: 500 }
    });
  }

  /**
   * Read file content
   */
  async readFile(projectId, filePath) {
    return await errorHandler.executeWithProtection('filesystem.readFile', async () => {
      const fullPath = this.validatePath(filePath, projectId);

      try {
        const stats = await fs.stat(fullPath);
        if (!stats.isFile()) {
          throw new Error('Path is not a file');
        }

        // Check file size (limit to 10MB for safety)
        if (stats.size > 10 * 1024 * 1024) {
          throw new Error('File too large to read (max 10MB)');
        }

        const content = await fs.readFile(fullPath, 'utf8');
        
        return {
          content,
          size: stats.size,
          modified: stats.mtime,
          encoding: 'utf8'
        };
      } catch (error) {
        if (error.code === 'ENOENT') {
          throw new Error('File not found');
        }
        throw error;
      }
    }, {
      component: 'filesystem',
      operation: 'readFile',
      retry: { maxRetries: 2, baseDelay: 500 }
    });
  }

  /**
   * Write file content
   */
  async writeFile(projectId, filePath, content, options = {}) {
    return await errorHandler.executeWithProtection('filesystem.writeFile', async () => {
      const fullPath = this.validatePath(filePath, projectId);
      const { encoding = 'utf8', createDirectories = true } = options;

      try {
        // Create parent directories if needed
        if (createDirectories) {
          const dir = path.dirname(fullPath);
          await fs.mkdir(dir, { recursive: true });
        }

        await fs.writeFile(fullPath, content, encoding);
        
        const stats = await fs.stat(fullPath);
        
        // Publish file change event
        await eventBus.publish(EventTypes.FILE_CHANGED, {
          projectId,
          filePath,
          action: 'write',
          size: stats.size
        });

        return {
          success: true,
          size: stats.size,
          modified: stats.mtime
        };
      } catch (error) {
        throw new Error(`Failed to write file: ${error.message}`);
      }
    }, {
      component: 'filesystem',
      operation: 'writeFile',
      retry: { maxRetries: 2, baseDelay: 500 }
    });
  }

  /**
   * Create directory
   */
  async createDirectory(projectId, dirPath) {
    return await errorHandler.executeWithProtection('filesystem.createDirectory', async () => {
      const fullPath = this.validatePath(dirPath, projectId);

      try {
        await fs.mkdir(fullPath, { recursive: true });
        
        const stats = await fs.stat(fullPath);
        
        // Publish directory creation event
        await eventBus.publish(EventTypes.FILE_CHANGED, {
          projectId,
          filePath: dirPath,
          action: 'create_directory'
        });

        return {
          success: true,
          created: stats.birthtime
        };
      } catch (error) {
        throw new Error(`Failed to create directory: ${error.message}`);
      }
    }, {
      component: 'filesystem',
      operation: 'createDirectory',
      retry: { maxRetries: 2, baseDelay: 500 }
    });
  }

  /**
   * Delete file or directory
   */
  async deleteItem(projectId, itemPath) {
    return await errorHandler.executeWithProtection('filesystem.deleteItem', async () => {
      const fullPath = this.validatePath(itemPath, projectId);

      try {
        const stats = await fs.stat(fullPath);
        
        if (stats.isDirectory()) {
          await fs.rmdir(fullPath, { recursive: true });
        } else {
          await fs.unlink(fullPath);
        }
        
        // Publish deletion event
        await eventBus.publish(EventTypes.FILE_CHANGED, {
          projectId,
          filePath: itemPath,
          action: 'delete',
          type: stats.isDirectory() ? 'directory' : 'file'
        });

        return {
          success: true,
          deleted: itemPath
        };
      } catch (error) {
        if (error.code === 'ENOENT') {
          throw new Error('File or directory not found');
        }
        throw new Error(`Failed to delete item: ${error.message}`);
      }
    }, {
      component: 'filesystem',
      operation: 'deleteItem',
      retry: { maxRetries: 2, baseDelay: 500 }
    });
  }

  /**
   * Rename/move file or directory
   */
  async renameItem(projectId, oldPath, newPath) {
    return await errorHandler.executeWithProtection('filesystem.renameItem', async () => {
      const fullOldPath = this.validatePath(oldPath, projectId);
      const fullNewPath = this.validatePath(newPath, projectId);

      try {
        // Create parent directory for new path if needed
        const newDir = path.dirname(fullNewPath);
        await fs.mkdir(newDir, { recursive: true });

        await fs.rename(fullOldPath, fullNewPath);
        
        // Publish rename event
        await eventBus.publish(EventTypes.FILE_CHANGED, {
          projectId,
          filePath: oldPath,
          newPath,
          action: 'rename'
        });

        return {
          success: true,
          oldPath,
          newPath
        };
      } catch (error) {
        if (error.code === 'ENOENT') {
          throw new Error('Source file or directory not found');
        }
        throw new Error(`Failed to rename item: ${error.message}`);
      }
    }, {
      component: 'filesystem',
      operation: 'renameItem',
      retry: { maxRetries: 2, baseDelay: 500 }
    });
  }

  /**
   * Get file/directory information
   */
  async getItemInfo(projectId, itemPath) {
    return await errorHandler.executeWithProtection('filesystem.getItemInfo', async () => {
      const fullPath = this.validatePath(itemPath, projectId);

      try {
        const stats = await fs.stat(fullPath);
        
        return {
          path: itemPath,
          type: stats.isDirectory() ? 'directory' : 'file',
          size: stats.size,
          created: stats.birthtime,
          modified: stats.mtime,
          accessed: stats.atime,
          permissions: (stats.mode & parseInt('777', 8)).toString(8),
          isReadable: true, // We'll assume readable for now
          isWritable: true  // We'll assume writable for now
        };
      } catch (error) {
        if (error.code === 'ENOENT') {
          throw new Error('File or directory not found');
        }
        throw error;
      }
    }, {
      component: 'filesystem',
      operation: 'getItemInfo',
      retry: { maxRetries: 2, baseDelay: 500 }
    });
  }
}

module.exports = {
  FileSystemService
};
