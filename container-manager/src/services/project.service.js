const fs = require('fs').promises;
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const logger = require('../utils/logger');
const { config<PERSON>anager, errorHandler, eventBus, EventTypes } = require('../../shared');
const { DockerService } = require('./docker.service');
const { FileSystemService } = require('./filesystem.service');

class ProjectService {
  constructor(docker) {
    this.docker = docker;
    this.dockerService = new DockerService(docker);
    this.fileSystemService = new FileSystemService(docker);
    this.projectsDataPath = process.env.PROJECTS_DATA_PATH || '/tmp/cloud-ide-projects-data';
    this.projectsFile = path.join(this.projectsDataPath, 'projects.json');
    this.initializeProjectsData();
  }

  async initializeProjectsData() {
    try {
      await fs.mkdir(this.projectsDataPath, { recursive: true });
      
      // Initialize projects file if it doesn't exist
      try {
        await fs.access(this.projectsFile);
      } catch (error) {
        await fs.writeFile(this.projectsFile, JSON.stringify([], null, 2));
        logger.info('Projects data file initialized');
      }
    } catch (error) {
      logger.error('Failed to initialize projects data:', error);
    }
  }

  async loadProjects() {
    try {
      const data = await fs.readFile(this.projectsFile, 'utf8');
      return JSON.parse(data);
    } catch (error) {
      logger.error('Failed to load projects:', error);
      return [];
    }
  }

  async saveProjects(projects) {
    try {
      await fs.writeFile(this.projectsFile, JSON.stringify(projects, null, 2));
    } catch (error) {
      logger.error('Failed to save projects:', error);
      throw error;
    }
  }

  /**
   * List all projects
   */
  async listProjects() {
    return await errorHandler.executeWithProtection('project.listProjects', async () => {
      const projects = await this.loadProjects();
      
      // Update project status based on container status
      for (const project of projects) {
        if (project.containerId) {
          try {
            const containers = await this.dockerService.listContainers();
            const container = containers.find(c => c.id === project.containerId);
            project.status = container ? (container.status.includes('Up') ? 'running' : 'stopped') : 'unknown';
          } catch (error) {
            project.status = 'unknown';
          }
        } else {
          project.status = 'not_deployed';
        }
      }

      return projects;
    }, {
      component: 'project',
      operation: 'listProjects',
      retry: { maxRetries: 2, baseDelay: 500 }
    });
  }

  /**
   * Get project by ID
   */
  async getProject(projectId) {
    return await errorHandler.executeWithProtection('project.getProject', async () => {
      const projects = await this.loadProjects();
      const project = projects.find(p => p.id === projectId);
      
      if (!project) {
        throw new Error('Project not found');
      }

      // Update container status
      if (project.containerId) {
        try {
          const containers = await this.dockerService.listContainers();
          const container = containers.find(c => c.id === project.containerId);
          project.status = container ? (container.status.includes('Up') ? 'running' : 'stopped') : 'unknown';
          project.containerInfo = container;
        } catch (error) {
          project.status = 'unknown';
        }
      }

      return project;
    }, {
      component: 'project',
      operation: 'getProject',
      retry: { maxRetries: 2, baseDelay: 500 }
    });
  }

  /**
   * Create a new project
   */
  async createProject(projectData) {
    return await errorHandler.executeWithProtection('project.createProject', async () => {
      const projects = await this.loadProjects();

      const project = {
        id: uuidv4(),
        name: projectData.name,
        description: projectData.description || '',
        type: projectData.type || 'general',
        template: projectData.template || 'blank',
        created: new Date().toISOString(),
        modified: new Date().toISOString(),
        status: 'creating',
        settings: {
          language: projectData.language || 'javascript',
          framework: projectData.framework || 'none',
          nodeVersion: projectData.nodeVersion || '18',
          port: projectData.port || 3000,
          environment: projectData.environment || 'development',
          ...projectData.settings
        },
        containerId: null,
        subdomain: null
      };

      projects.push(project);
      await this.saveProjects(projects);

      // Initialize project files BEFORE starting container
      try {
        await this.initializeProjectFiles(project.id, project.template);
        logger.info(`Project files initialized for ${project.name}`);
      } catch (error) {
        logger.warn('Failed to initialize project files:', error);
      }

      // Publish project creation event
      await eventBus.publish(EventTypes.PROJECT_CREATED, {
        projectId: project.id,
        name: project.name,
        type: project.type
      });

      logger.info(`Project created: ${project.name} (${project.id})`);

      // Automatically start the container for the project
      try {
        logger.info(`Auto-starting container for project: ${project.name} (${project.id})`);
        const startResult = await this.startProject(project.id);
        logger.info(`Container auto-started successfully for project: ${project.name}`);

        // Copy project files into the container after it's started
        try {
          await this.copyFilesToContainer(project.id);
          logger.info(`Project files copied to container for: ${project.name}`);
        } catch (copyError) {
          logger.warn(`Failed to copy files to container for ${project.name}:`, copyError);
        }

        // Return the updated project with container info
        return startResult.project || project;
      } catch (error) {
        logger.error(`Failed to auto-start container for project ${project.name}:`, error);

        // Update project status to indicate container creation failed
        const updatedProjects = await this.loadProjects();
        const projectIndex = updatedProjects.findIndex(p => p.id === project.id);
        if (projectIndex !== -1) {
          updatedProjects[projectIndex].status = 'container_failed';
          updatedProjects[projectIndex].error = error.message;
          updatedProjects[projectIndex].modified = new Date().toISOString();
          await this.saveProjects(updatedProjects);
        }

        // Still return the project, but with error status
        return { ...project, status: 'container_failed', error: error.message };
      }
    }, {
      component: 'project',
      operation: 'createProject',
      retry: { maxRetries: 2, baseDelay: 500 }
    });
  }

  /**
   * Update project
   */
  async updateProject(projectId, updates) {
    return await errorHandler.executeWithProtection('project.updateProject', async () => {
      const projects = await this.loadProjects();
      const projectIndex = projects.findIndex(p => p.id === projectId);
      
      if (projectIndex === -1) {
        throw new Error('Project not found');
      }

      const project = projects[projectIndex];
      
      // Update allowed fields
      const allowedFields = ['name', 'description', 'settings'];
      allowedFields.forEach(field => {
        if (updates[field] !== undefined) {
          if (field === 'settings') {
            project.settings = { ...project.settings, ...updates.settings };
          } else {
            project[field] = updates[field];
          }
        }
      });

      project.modified = new Date().toISOString();
      projects[projectIndex] = project;
      
      await this.saveProjects(projects);

      // Publish project update event
      await eventBus.publish(EventTypes.PROJECT_UPDATED, {
        projectId: project.id,
        name: project.name,
        updates: Object.keys(updates)
      });

      logger.info(`Project updated: ${project.name} (${project.id})`);
      return project;
    }, {
      component: 'project',
      operation: 'updateProject',
      retry: { maxRetries: 2, baseDelay: 500 }
    });
  }

  /**
   * Delete project
   */
  async deleteProject(projectId) {
    return await errorHandler.executeWithProtection('project.deleteProject', async () => {
      const projects = await this.loadProjects();
      const projectIndex = projects.findIndex(p => p.id === projectId);
      
      if (projectIndex === -1) {
        throw new Error('Project not found');
      }

      const project = projects[projectIndex];

      // Stop and remove container if exists
      if (project.containerId) {
        try {
          await this.dockerService.stopContainer(project.containerId);
          await this.dockerService.removeContainer(project.containerId);
        } catch (error) {
          logger.warn(`Failed to clean up container for project ${projectId}:`, error);
        }
      }

      // Remove project files
      try {
        await this.fileSystemService.deleteItem(projectId, '/');
      } catch (error) {
        logger.warn(`Failed to clean up files for project ${projectId}:`, error);
      }

      // Remove from projects list
      projects.splice(projectIndex, 1);
      await this.saveProjects(projects);

      // Publish project deletion event
      await eventBus.publish(EventTypes.PROJECT_DELETED, {
        projectId: project.id,
        name: project.name
      });

      logger.info(`Project deleted: ${project.name} (${project.id})`);
      return { success: true, deletedProject: project };
    }, {
      component: 'project',
      operation: 'deleteProject',
      retry: { maxRetries: 2, baseDelay: 500 }
    });
  }

  /**
   * Start project (deploy container)
   */
  async startProject(projectId) {
    return await errorHandler.executeWithProtection('project.startProject', async () => {
      const project = await this.getProject(projectId);

      if (project.containerId && project.status === 'running') {
        return { message: 'Project is already running', project };
      }

      // Ensure project directory exists
      const projectPath = this.fileSystemService.getProjectPath(projectId);
      await this.fileSystemService.ensureProjectDirectory(projectId);

      let container;
      let containerId = project.containerId;
      let subdomain = project.subdomain;

      // Check if container already exists (from previous stop)
      if (project.containerId) {
        try {
          // Check if container still exists and try to restart it
          logger.info(`Checking existing container: ${project.containerId}`);
          const isRunning = await this.dockerService.isContainerRunning(project.containerId);

          if (isRunning) {
            logger.info(`Container ${project.containerId} is already running`);
            containerId = project.containerId;
            subdomain = project.subdomain;
            container = { id: containerId, subdomain };
          } else {
            // Try to restart existing container
            logger.info(`Attempting to restart existing container: ${project.containerId}`);
            container = await this.dockerService.restartContainer(project.containerId);
            containerId = project.containerId;
            subdomain = project.subdomain;
            logger.info(`Successfully restarted existing container: ${project.containerId}`);
          }
        } catch (error) {
          logger.warn(`Failed to restart existing container ${project.containerId}: ${error.message}`);

          // If restart fails, remove the old container reference and create new one
          try {
            logger.info(`Removing old container ${project.containerId} before creating new one`);
            await this.dockerService.removeContainer(project.containerId);
            logger.info(`Successfully removed old container ${project.containerId}`);
          } catch (removeError) {
            logger.warn(`Failed to remove old container ${project.containerId}: ${removeError.message}`);
            // Continue anyway - we'll try to create a new container
          }

          // Clear the old container ID to avoid conflicts
          const projects = await this.loadProjects();
          const projectIndex = projects.findIndex(p => p.id === projectId);
          if (projectIndex !== -1) {
            projects[projectIndex].containerId = null;
            projects[projectIndex].subdomain = null;
            await this.saveProjects(projects);
          }

          // Create new container
          logger.info(`Creating new container for project: ${project.name}`);
          container = await this.createNewContainer(project, projectId, projectPath);
          containerId = container.id;
          subdomain = container.subdomain;
        }
      } else {
        // No existing container, create new one
        logger.info(`Creating new container for project: ${project.name}`);
        container = await this.createNewContainer(project, projectId, projectPath);
        containerId = container.id;
        subdomain = container.subdomain;
      }

      // Update project with container info
      const projects = await this.loadProjects();
      const projectIndex = projects.findIndex(p => p.id === projectId);
      projects[projectIndex].containerId = containerId;
      projects[projectIndex].subdomain = subdomain;
      projects[projectIndex].status = 'running';
      projects[projectIndex].modified = new Date().toISOString();

      await this.saveProjects(projects);

      // Publish project start event
      await eventBus.publish(EventTypes.PROJECT_STARTED, {
        projectId: project.id,
        containerId: containerId,
        subdomain: subdomain
      });

      logger.info(`Project started: ${project.name} (${project.id})`);
      return {
        message: 'Project started successfully',
        project: projects[projectIndex],
        containerId: containerId,
        subdomain: subdomain
      };
    }, {
      component: 'project',
      operation: 'startProject',
      retry: { maxRetries: 2, baseDelay: 1000 }
    });
  }

  /**
   * Create new container for project
   */
  async createNewContainer(project, projectId, projectPath) {
    const containerName = `project-${projectId}`;

    // Check if a container with this name already exists and remove it
    try {
      const existingContainers = await this.dockerService.listContainers();
      const existingContainer = existingContainers.find(c => c.name === containerName);

      if (existingContainer) {
        logger.warn(`Container with name ${containerName} already exists, removing it`);
        await this.dockerService.removeContainer(existingContainer.id);
        logger.info(`Removed existing container ${containerName}`);
      }
    } catch (error) {
      logger.warn(`Error checking for existing containers: ${error.message}`);
      // Continue anyway
    }

    const containerConfig = {
      name: containerName,
      image: this.getImageForProject(project),
      language: project.settings.language || 'general',
      subdomain: `${project.name.toLowerCase().replace(/[^a-z0-9]/g, '-')}-${projectId.slice(0, 8)}`,
      ports: [project.settings.port || 3000],
      projectId: projectId,
      environment: {
        NODE_ENV: project.settings.environment || 'development',
        PROJECT_ID: projectId,
        PROJECT_NAME: project.name,
        LANGUAGE: project.settings.language || 'general',
        PROJECT_TYPE: project.type || 'general'
      },
      volumes: [
        `${projectPath}:/workspace:rw`
      ],
      workingDir: '/workspace',
      memory: 512 * 1024 * 1024, // 512MB
      cpuShares: 1024
    };

    try {
      // Create and start container
      const container = await this.dockerService.createContainer(containerConfig);

      return {
        id: container.id,
        subdomain: containerConfig.subdomain
      };
    } catch (error) {
      if (error.message.includes('already in use') || error.message.includes('Conflict')) {
        logger.error(`Container name conflict for ${containerName}, attempting to force remove and retry`);

        // Try to force remove any conflicting container
        try {
          const containers = await this.dockerService.listContainers();
          const conflictingContainer = containers.find(c => c.name === containerName);
          if (conflictingContainer) {
            await this.dockerService.removeContainer(conflictingContainer.id);
            logger.info(`Force removed conflicting container ${containerName}`);

            // Retry container creation
            const container = await this.dockerService.createContainer(containerConfig);
            return {
              id: container.id,
              subdomain: containerConfig.subdomain
            };
          }
        } catch (retryError) {
          logger.error(`Failed to resolve container name conflict: ${retryError.message}`);
        }
      }
      throw error;
    }
  }

  /**
   * Stop project
   */
  async stopProject(projectId) {
    return await errorHandler.executeWithProtection('project.stopProject', async () => {
      const project = await this.getProject(projectId);
      
      if (!project.containerId) {
        throw new Error('Project is not deployed');
      }

      // Stop container
      await this.dockerService.stopContainer(project.containerId);

      // Update project status
      const projects = await this.loadProjects();
      const projectIndex = projects.findIndex(p => p.id === projectId);
      projects[projectIndex].status = 'stopped';
      projects[projectIndex].modified = new Date().toISOString();
      
      await this.saveProjects(projects);

      // Publish project stop event
      await eventBus.publish(EventTypes.PROJECT_STOPPED, {
        projectId: project.id,
        containerId: project.containerId
      });

      logger.info(`Project stopped: ${project.name} (${project.id})`);
      return { 
        message: 'Project stopped successfully', 
        project: projects[projectIndex]
      };
    }, {
      component: 'project',
      operation: 'stopProject',
      retry: { maxRetries: 2, baseDelay: 1000 }
    });
  }

  /**
   * Execute a command in the project container
   */
  async executeCommand(projectId, command, options = {}) {
    return await errorHandler.executeWithProtection('project.executeCommand', async () => {
      const project = await this.getProject(projectId);
      if (!project) {
        throw new Error('Project not found');
      }

      // Ensure project container is running
      let containerId = project.containerId;
      if (!containerId || !(await this.dockerService.isContainerRunning(containerId))) {
        logger.info(`Starting container for project ${projectId} to execute command`);
        const startResult = await this.startProject(projectId);
        containerId = startResult.containerId;
      }

      // Execute command in container
      const result = await this.dockerService.executeCommand(containerId, command, {
        workingDir: '/workspace',
        ...options
      });

      // Update project last activity
      const projects = await this.loadProjects();
      const projectIndex = projects.findIndex(p => p.id === projectId);
      if (projectIndex !== -1) {
        projects[projectIndex].modified = new Date().toISOString();
        await this.saveProjects(projects);
      }

      return {
        projectId,
        containerId,
        command,
        ...result
      };
    }, {
      component: 'project',
      operation: 'executeCommand',
      retry: { maxRetries: 1, baseDelay: 1000 }
    });
  }

  /**
   * Run project using language-specific command
   */
  async runProject(projectId, fileName = null, options = {}) {
    return await errorHandler.executeWithProtection('project.runProject', async () => {
      const project = await this.getProject(projectId);
      if (!project) {
        throw new Error('Project not found');
      }

      // Ensure project container is running
      let containerId = project.containerId;
      if (!containerId || !(await this.dockerService.isContainerRunning(containerId))) {
        logger.info(`Starting container for project ${projectId} to run project`);
        const startResult = await this.startProject(projectId);
        containerId = startResult.containerId;
      }

      // Get language-specific run command
      const language = project.settings.language || 'javascript';
      const projectType = project.type || 'general';
      const runCommand = this.dockerService.getRunCommand(language, projectType, fileName);

      logger.info(`Running project ${projectId} with command: ${runCommand}`);

      // Execute run command
      const result = await this.dockerService.executeCommand(containerId, runCommand, {
        workingDir: '/workspace',
        timeout: options.timeout || 60000
      });

      // Update project last activity
      const projects = await this.loadProjects();
      const projectIndex = projects.findIndex(p => p.id === projectId);
      if (projectIndex !== -1) {
        projects[projectIndex].modified = new Date().toISOString();
        await this.saveProjects(projects);
      }

      return {
        projectId,
        containerId,
        language,
        projectType,
        runCommand,
        fileName,
        ...result
      };
    }, {
      component: 'project',
      operation: 'runProject',
      retry: { maxRetries: 1, baseDelay: 1000 }
    });
  }

  /**
   * Get container status for a project
   */
  async getContainerStatus(projectId) {
    return await errorHandler.executeWithProtection('project.getContainerStatus', async () => {
      const project = await this.getProject(projectId);
      if (!project) {
        throw new Error('Project not found');
      }

      if (!project.containerId) {
        return {
          projectId,
          status: 'not_deployed',
          containerId: null,
          containerInfo: null
        };
      }

      try {
        const isRunning = await this.dockerService.isContainerRunning(project.containerId);
        const containerInfo = await this.dockerService.getContainerInfo(project.containerId);

        return {
          projectId,
          status: isRunning ? 'running' : 'stopped',
          containerId: project.containerId,
          containerInfo: {
            id: containerInfo.Id,
            name: containerInfo.Name,
            state: containerInfo.State,
            created: containerInfo.Created,
            image: containerInfo.Config.Image,
            ports: containerInfo.NetworkSettings.Ports
          }
        };
      } catch (error) {
        logger.warn(`Failed to get container status for ${project.containerId}:`, error);
        return {
          projectId,
          status: 'error',
          containerId: project.containerId,
          error: error.message
        };
      }
    }, {
      component: 'project',
      operation: 'getContainerStatus',
      retry: { maxRetries: 2, baseDelay: 1000 }
    });
  }

  /**
   * Copy project files to container using docker cp
   */
  async copyFilesToContainer(projectId) {
    return await errorHandler.executeWithProtection('project.copyFilesToContainer', async () => {
      const projects = await this.loadProjects();
      const project = projects.find(p => p.id === projectId);

      if (!project || !project.containerId) {
        throw new Error('Project or container not found');
      }

      const projectPath = this.fileSystemService.getProjectPath(projectId);

      // Check if project directory exists and has files
      try {
        const files = await fs.readdir(projectPath);
        if (files.length === 0) {
          logger.info(`No files to copy for project ${projectId}`);
          return;
        }

        // Use docker cp to copy the entire project directory
        const { spawn } = require('child_process');

        // Copy each file individually to avoid permission issues
        for (const file of files) {
          const filePath = path.join(projectPath, file);
          const stats = await fs.stat(filePath);

          if (stats.isFile()) {
            await new Promise((resolve, reject) => {
              const dockerCp = spawn('docker', ['cp', filePath, `${project.containerId}:/workspace/${file}`]);

              dockerCp.on('close', (code) => {
                if (code === 0) {
                  logger.debug(`Copied file ${file} to container ${project.containerId}`);
                  resolve();
                } else {
                  reject(new Error(`Failed to copy ${file} to container, exit code: ${code}`));
                }
              });

              dockerCp.on('error', (error) => {
                reject(error);
              });
            });
          }
        }

        logger.info(`Successfully copied ${files.length} files to container for project ${projectId}`);
      } catch (error) {
        if (error.code === 'ENOENT') {
          logger.warn(`Project directory not found: ${projectPath}`);
        } else {
          throw error;
        }
      }
    }, {
      component: 'project',
      operation: 'copyFilesToContainer',
      retry: { maxRetries: 2, baseDelay: 1000 }
    });
  }

  /**
   * Initialize project files based on template
   */
  async initializeProjectFiles(projectId, template = 'blank') {
    const templates = {
      blank: [
        {
          path: '/README.md',
          content: `# New Project\n\nWelcome to your new project!\n\n## Getting Started\n\n1. Start coding in the editor\n2. Use the terminal for commands\n3. Your changes are saved automatically\n\nHappy coding! 🚀`
        }
      ],
      javascript: [
        {
          path: '/package.json',
          content: JSON.stringify({
            name: 'cloud-ide-project',
            version: '1.0.0',
            description: 'A project created in Cloud IDE',
            main: 'index.js',
            scripts: {
              start: 'node index.js',
              dev: 'node index.js'
            }
          }, null, 2)
        },
        {
          path: '/index.js',
          content: `console.log('Hello from Cloud IDE!');\n\n// Start coding here...\nfunction main() {\n    console.log('Project initialized successfully!');\n}\n\nmain();`
        }
      ],
      react: [
        {
          path: '/package.json',
          content: JSON.stringify({
            name: 'react-cloud-ide-project',
            version: '1.0.0',
            private: true,
            dependencies: {
              react: '^18.2.0',
              'react-dom': '^18.2.0',
              'react-scripts': '5.0.1'
            },
            scripts: {
              start: 'react-scripts start',
              build: 'react-scripts build',
              test: 'react-scripts test',
              eject: 'react-scripts eject'
            }
          }, null, 2)
        },
        {
          path: '/public/index.html',
          content: `<!DOCTYPE html>\n<html lang="en">\n  <head>\n    <meta charset="utf-8" />\n    <meta name="viewport" content="width=device-width, initial-scale=1" />\n    <title>React Cloud IDE App</title>\n  </head>\n  <body>\n    <noscript>You need to enable JavaScript to run this app.</noscript>\n    <div id="root"></div>\n  </body>\n</html>`
        },
        {
          path: '/src/App.js',
          content: `import React from 'react';\nimport './App.css';\n\nfunction App() {\n  return (\n    <div className="App">\n      <header className="App-header">\n        <h1>Welcome to Cloud IDE</h1>\n        <p>Start building your React app!</p>\n        <p>Edit <code>src/App.js</code> and save to reload.</p>\n      </header>\n    </div>\n  );\n}\n\nexport default App;`
        },
        {
          path: '/src/App.css',
          content: `.App {\n  text-align: center;\n}\n\n.App-header {\n  background-color: #282c34;\n  padding: 20px;\n  color: white;\n  min-height: 100vh;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  font-size: calc(10px + 2vmin);\n}\n\ncode {\n  background-color: #f1f1f1;\n  padding: 2px 4px;\n  border-radius: 3px;\n  color: #333;\n}`
        },
        {
          path: '/src/index.js',
          content: `import React from 'react';\nimport ReactDOM from 'react-dom/client';\nimport './index.css';\nimport App from './App';\n\nconst root = ReactDOM.createRoot(document.getElementById('root'));\nroot.render(\n  <React.StrictMode>\n    <App />\n  </React.StrictMode>\n);`
        },
        {
          path: '/src/index.css',
          content: `body {\n  margin: 0;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\n    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\n    sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n\ncode {\n  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',\n    monospace;\n}`
        }
      ],
      vue: [
        {
          path: '/package.json',
          content: JSON.stringify({
            name: 'vue-cloud-ide-project',
            version: '1.0.0',
            private: true,
            scripts: {
              serve: 'vue-cli-service serve',
              build: 'vue-cli-service build',
              lint: 'vue-cli-service lint'
            },
            dependencies: {
              'core-js': '^3.8.3',
              'vue': '^3.2.13'
            },
            devDependencies: {
              '@vue/cli-service': '^5.0.0'
            }
          }, null, 2)
        },
        {
          path: '/public/index.html',
          content: `<!DOCTYPE html>\n<html lang="">\n  <head>\n    <meta charset="utf-8">\n    <meta http-equiv="X-UA-Compatible" content="IE=edge">\n    <meta name="viewport" content="width=device-width,initial-scale=1.0">\n    <title>Vue Cloud IDE App</title>\n  </head>\n  <body>\n    <noscript>\n      <strong>We're sorry but this app doesn't work properly without JavaScript enabled. Please enable it to continue.</strong>\n    </noscript>\n    <div id="app"></div>\n  </body>\n</html>`
        },
        {
          path: '/src/main.js',
          content: `import { createApp } from 'vue'\nimport App from './App.vue'\n\ncreateApp(App).mount('#app')`
        },
        {
          path: '/src/App.vue',
          content: `<template>\n  <div id="app">\n    <header class="app-header">\n      <h1>Welcome to Cloud IDE</h1>\n      <p>Start building your Vue.js app!</p>\n      <p>Edit <code>src/App.vue</code> and save to reload.</p>\n    </header>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'App'\n}\n</script>\n\n<style>\n#app {\n  font-family: Avenir, Helvetica, Arial, sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  text-align: center;\n  color: #2c3e50;\n}\n\n.app-header {\n  background-color: #42b883;\n  padding: 20px;\n  color: white;\n  min-height: 100vh;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n}\n\ncode {\n  background-color: #f1f1f1;\n  padding: 2px 4px;\n  border-radius: 3px;\n  color: #333;\n}\n</style>`
        }
      ],
      python: [
        {
          path: '/main.py',
          content: `#!/usr/bin/env python3\n"""\nCloud IDE Python Project\n\nWelcome to your Python project in Cloud IDE!\nThis is a starter template to get you going.\n"""\n\ndef main():\n    """Main function - entry point of the application."""\n    print("Hello from Cloud IDE!")\n    print("Welcome to your Python project!")\n    \n    # Example: Basic calculator\n    print("\\n--- Simple Calculator Example ---")\n    a = 10\n    b = 5\n    \n    print(f"Addition: {a} + {b} = {a + b}")\n    print(f"Subtraction: {a} - {b} = {a - b}")\n    print(f"Multiplication: {a} * {b} = {a * b}")\n    print(f"Division: {a} / {b} = {a / b}")\n    \n    # TODO: Add your code here\n    print("\\nStart coding your amazing Python project!")\n\nif __name__ == "__main__":\n    main()`
        },
        {
          path: '/requirements.txt',
          content: `# Python dependencies for your Cloud IDE project\n# Add your required packages here\n\n# Example packages (uncomment as needed):\n# requests>=2.28.0\n# numpy>=1.21.0\n# pandas>=1.3.0\n# flask>=2.0.0\n# django>=4.0.0`
        },
        {
          path: '/README.md',
          content: `# Python Cloud IDE Project\n\nWelcome to your Python project in Cloud IDE!\n\n## Getting Started\n\n1. **Run your application:**\n   \`\`\`bash\n   python main.py\n   \`\`\`\n\n2. **Install dependencies:**\n   \`\`\`bash\n   pip install -r requirements.txt\n   \`\`\`\n\n3. **Project Structure:**\n   - \`main.py\` - Main application file\n   - \`requirements.txt\` - Python dependencies\n   - \`README.md\` - This file\n\n## Features\n\n- ✅ Python 3.11+ ready\n- ✅ Virtual environment support\n- ✅ Package management with pip\n- ✅ Example code included\n\n## Next Steps\n\n1. Edit \`main.py\` to build your application\n2. Add dependencies to \`requirements.txt\`\n3. Use the terminal to run Python commands\n4. Happy coding! 🐍\n\n## Resources\n\n- [Python Documentation](https://docs.python.org/3/)\n- [Python Package Index (PyPI)](https://pypi.org/)\n- [Python Tutorial](https://docs.python.org/3/tutorial/)`
        }
      ],
      java: [
        {
          path: '/pom.xml',
          content: `<?xml version="1.0" encoding="UTF-8"?>\n<project xmlns="http://maven.apache.org/POM/4.0.0"\n         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"\n         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">\n    <modelVersion>4.0.0</modelVersion>\n\n    <groupId>com.cloudide</groupId>\n    <artifactId>java-project</artifactId>\n    <version>1.0.0</version>\n    <packaging>jar</packaging>\n\n    <name>Cloud IDE Java Project</name>\n    <description>A Java project created in Cloud IDE</description>\n\n    <properties>\n        <maven.compiler.source>17</maven.compiler.source>\n        <maven.compiler.target>17</maven.compiler.target>\n        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>\n    </properties>\n\n    <dependencies>\n        <!-- JUnit for testing -->\n        <dependency>\n            <groupId>org.junit.jupiter</groupId>\n            <artifactId>junit-jupiter</artifactId>\n            <version>5.9.2</version>\n            <scope>test</scope>\n        </dependency>\n    </dependencies>\n\n    <build>\n        <plugins>\n            <plugin>\n                <groupId>org.apache.maven.plugins</groupId>\n                <artifactId>maven-compiler-plugin</artifactId>\n                <version>3.11.0</version>\n            </plugin>\n            <plugin>\n                <groupId>org.apache.maven.plugins</groupId>\n                <artifactId>maven-surefire-plugin</artifactId>\n                <version>3.0.0-M9</version>\n            </plugin>\n        </plugins>\n    </build>\n</project>`
        },
        {
          path: '/src/main/java/com/cloudide/App.java',
          content: `package com.cloudide;\n\n/**\n * Cloud IDE Java Application\n * \n * Welcome to your Java project in Cloud IDE!\n * This is a starter template to get you going.\n */\npublic class App {\n    \n    public static void main(String[] args) {\n        System.out.println("Hello from Cloud IDE!");\n        System.out.println("Welcome to your Java project!");\n        \n        // Example: Simple calculator\n        System.out.println("\\n--- Simple Calculator Example ---");\n        Calculator calc = new Calculator();\n        \n        int a = 10;\n        int b = 5;\n        \n        System.out.println("Addition: " + a + " + " + b + " = " + calc.add(a, b));\n        System.out.println("Subtraction: " + a + " - " + b + " = " + calc.subtract(a, b));\n        System.out.println("Multiplication: " + a + " * " + b + " = " + calc.multiply(a, b));\n        System.out.println("Division: " + a + " / " + b + " = " + calc.divide(a, b));\n        \n        System.out.println("\\nStart coding your amazing Java project!");\n    }\n    \n    /**\n     * Simple calculator class for demonstration\n     */\n    static class Calculator {\n        public int add(int a, int b) {\n            return a + b;\n        }\n        \n        public int subtract(int a, int b) {\n            return a - b;\n        }\n        \n        public int multiply(int a, int b) {\n            return a * b;\n        }\n        \n        public double divide(int a, int b) {\n            if (b == 0) {\n                throw new IllegalArgumentException("Division by zero!");\n            }\n            return (double) a / b;\n        }\n    }\n}`
        },
        {
          path: '/src/test/java/com/cloudide/AppTest.java',
          content: `package com.cloudide;\n\nimport org.junit.jupiter.api.Test;\nimport static org.junit.jupiter.api.Assertions.*;\n\n/**\n * Unit tests for the App class\n */\nclass AppTest {\n    \n    @Test\n    void testCalculatorAdd() {\n        App.Calculator calc = new App.Calculator();\n        assertEquals(15, calc.add(10, 5));\n    }\n    \n    @Test\n    void testCalculatorSubtract() {\n        App.Calculator calc = new App.Calculator();\n        assertEquals(5, calc.subtract(10, 5));\n    }\n    \n    @Test\n    void testCalculatorMultiply() {\n        App.Calculator calc = new App.Calculator();\n        assertEquals(50, calc.multiply(10, 5));\n    }\n    \n    @Test\n    void testCalculatorDivide() {\n        App.Calculator calc = new App.Calculator();\n        assertEquals(2.0, calc.divide(10, 5), 0.001);\n    }\n    \n    @Test\n    void testCalculatorDivideByZero() {\n        App.Calculator calc = new App.Calculator();\n        assertThrows(IllegalArgumentException.class, () -> {\n            calc.divide(10, 0);\n        });\n    }\n}`
        },
        {
          path: '/README.md',
          content: `# Java Cloud IDE Project\n\nWelcome to your Java project in Cloud IDE!\n\n## Getting Started\n\n1. **Compile and run your application:**\n   \`\`\`bash\n   mvn compile exec:java -Dexec.mainClass="com.cloudide.App"\n   \`\`\`\n\n2. **Run tests:**\n   \`\`\`bash\n   mvn test\n   \`\`\`\n\n3. **Build JAR file:**\n   \`\`\`bash\n   mvn package\n   \`\`\`\n\n## Project Structure\n\n- \`src/main/java/\` - Main Java source code\n- \`src/test/java/\` - Unit tests\n- \`pom.xml\` - Maven configuration\n- \`README.md\` - This file\n\n## Features\n\n- ✅ Java 17 ready\n- ✅ Maven build system\n- ✅ JUnit 5 for testing\n- ✅ Example code and tests included\n\n## Next Steps\n\n1. Edit \`src/main/java/com/cloudide/App.java\` to build your application\n2. Add dependencies to \`pom.xml\`\n3. Write tests in \`src/test/java/\`\n4. Use the terminal to run Maven commands\n5. Happy coding! ☕\n\n## Resources\n\n- [Java Documentation](https://docs.oracle.com/en/java/)\n- [Maven Documentation](https://maven.apache.org/guides/)\n- [JUnit 5 Documentation](https://junit.org/junit5/docs/current/user-guide/)`
        }
      ],
      python: [
        {
          path: '/main.py',
          content: `#!/usr/bin/env python3
"""
Cloud IDE Python Project
"""

def main():
    print("Hello from Cloud IDE Python!")
    print("Project initialized successfully!")

    # Start coding here...
    name = input("What's your name? ")
    print(f"Nice to meet you, {name}!")

if __name__ == "__main__":
    main()
`
        },
        {
          path: '/requirements.txt',
          content: `# Python dependencies
# Add your packages here
# Example:
# requests>=2.28.0
# flask>=2.2.0
`
        },
        {
          path: '/README.md',
          content: `# Python Cloud IDE Project

Welcome to your Python project in Cloud IDE!

## Getting Started

1. Edit \`main.py\` to start coding
2. Add dependencies to \`requirements.txt\`
3. Use the terminal to run: \`python main.py\`

## Installing Dependencies

\`\`\`bash
pip install -r requirements.txt
\`\`\`

## Running the Project

\`\`\`bash
python main.py
\`\`\`

Happy coding! 🐍
`
        }
      ],
      java: [
        {
          path: '/Main.java',
          content: `/**
 * Cloud IDE Java Project
 */
public class Main {
    public static void main(String[] args) {
        System.out.println("Hello from Cloud IDE Java!");
        System.out.println("Project initialized successfully!");

        // Start coding here...
        System.out.println("Welcome to your Java project!");
    }
}
`
        },
        {
          path: '/README.md',
          content: `# Java Cloud IDE Project

Welcome to your Java project in Cloud IDE!

## Getting Started

1. Edit \`Main.java\` to start coding
2. Use the terminal to compile: \`javac Main.java\`
3. Run with: \`java Main\`

## Compiling and Running

\`\`\`bash
# Compile
javac Main.java

# Run
java Main
\`\`\`

Happy coding! ☕
`
        }
      ]
    };

    const templateFiles = templates[template] || templates.blank;
    
    for (const file of templateFiles) {
      try {
        await this.fileSystemService.writeFile(projectId, file.path, file.content);
      } catch (error) {
        logger.warn(`Failed to create template file ${file.path}:`, error);
      }
    }
  }

  /**
   * Get Docker image for project based on type/language
   */
  getImageForProject(project) {
    const imageMap = {
      // JavaScript/Node.js variants
      javascript: 'cloud-ide/nodejs:latest',
      react: 'cloud-ide/nodejs:latest',
      nodejs: 'cloud-ide/nodejs:latest',
      vue: 'cloud-ide/nodejs:latest',
      typescript: 'cloud-ide/nodejs:latest',

      // Python
      python: 'cloud-ide/python:latest',

      // Java
      java: 'cloud-ide/java:latest',

      // Web technologies (use Node.js container for web development)
      html: 'cloud-ide/nodejs:latest',
      css: 'cloud-ide/nodejs:latest',
      web: 'cloud-ide/nodejs:latest',

      // General/fallback
      general: 'cloud-ide/general:latest',
      blank: 'cloud-ide/general:latest'
    };

    const language = project.settings.language || 'general';
    const selectedImage = imageMap[language] || imageMap.general;

    logger.info(`Selected Docker image for project ${project.name} (language: ${language}): ${selectedImage}`);
    return selectedImage;
  }
}

module.exports = {
  ProjectService
};
