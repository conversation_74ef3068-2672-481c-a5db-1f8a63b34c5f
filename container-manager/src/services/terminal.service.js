const { spawn } = require('child_process');
const { v4: uuidv4 } = require('uuid');
const logger = require('../utils/logger');
const { configManager, errorHandler, eventBus, EventTypes } = require('../../shared');

class TerminalService {
  constructor(docker, projectService = null) {
    this.docker = docker;
    this.projectService = projectService;
    this.sessions = new Map(); // sessionId -> session data
    this.containerSessions = new Map(); // containerId -> Set of sessionIds
  }

  /**
   * Create a new terminal session
   */
  async createSession(options = {}) {
    return await errorHandler.executeWithProtection('terminal.createSession', async () => {
      const sessionId = uuidv4();
      const {
        projectId = 'default',
        containerId = null,
        shell = 'bash', // Use bash for better terminal experience
        cwd = process.cwd(),
        environment = {}
      } = options;

      let actualContainerId = containerId;

      // If no container ID provided but we have a project ID, try to find the container
      if (!actualContainerId && projectId && projectId !== 'default' && this.projectService) {
        try {
          const project = await this.projectService.getProject(projectId);
          if (project && project.containerId) {
            actualContainerId = project.containerId;
            logger.info(`Found container ${actualContainerId} for project ${projectId}`);
          }
        } catch (error) {
          logger.warn(`Failed to lookup container for project ${projectId}:`, error);
        }
      }

      // If still no container ID, try to find any running container for the project
      if (!actualContainerId && projectId && projectId !== 'default') {
        try {
          const containers = await this.docker.listContainers();
          logger.info(`Looking for container for project ${projectId} among ${containers.length} containers`);

          // Try multiple matching strategies
          let projectContainer = containers.find(c => {
            // Strategy 1: Check subdomain
            if (c.subdomain && c.subdomain.includes(projectId.toLowerCase())) {
              return true;
            }
            // Strategy 2: Check container name
            if (c.name && c.name.includes(projectId.toLowerCase())) {
              return true;
            }
            // Strategy 3: Check labels
            if (c.labels && c.labels['project.id'] === projectId) {
              return true;
            }
            return false;
          });

          // If no exact match, try partial matching on container names
          if (!projectContainer) {
            projectContainer = containers.find(c =>
              c.name && c.name.includes('project-') && c.status && c.status.includes('Up')
            );
          }

          if (projectContainer) {
            actualContainerId = projectContainer.id;
            logger.info(`Found running container for project ${projectId}: ${actualContainerId} (${projectContainer.name})`);
          } else {
            logger.warn(`No running container found for project ${projectId}. Available containers:`,
              containers.map(c => ({ name: c.name, id: c.id.substring(0, 12), status: c.status }))
            );
          }
        } catch (error) {
          logger.warn(`Could not find running container for project ${projectId}:`, error.message);
        }
      }

      let terminalProcess;
      let sessionType = 'local';

      if (actualContainerId) {
        // Create terminal session inside container
        sessionType = 'container';
        terminalProcess = await this.createContainerTerminal(actualContainerId, shell);
      } else {
        // Fallback: throw error instead of creating local terminal on Windows
        const errorMsg = projectId && projectId !== 'default'
          ? `No running container found for project "${projectId}". Please start the project first.`
          : 'No container available for terminal session. Please ensure the project is started.';
        throw new Error(errorMsg);
      }

      const session = {
        id: sessionId,
        projectId,
        containerId: actualContainerId,
        type: sessionType,
        shell,
        process: terminalProcess,
        created: new Date(),
        lastActivity: new Date(),
        isActive: true,
        buffer: '', // Store recent output for reconnection
        maxBufferSize: 10000 // Max characters to keep in buffer
      };

      this.sessions.set(sessionId, session);

      // Track container sessions
      if (actualContainerId) {
        if (!this.containerSessions.has(actualContainerId)) {
          this.containerSessions.set(actualContainerId, new Set());
        }
        this.containerSessions.get(actualContainerId).add(sessionId);
      }

      // Set up process event handlers IMMEDIATELY to catch all output
      this.setupProcessHandlers(session);

      // Log that handlers are set up
      logger.info(`Event handlers set up for terminal session: ${sessionId}`);

      // Publish session creation event
      await eventBus.publish(EventTypes.TERMINAL_SESSION_CREATED, {
        sessionId,
        projectId,
        containerId: actualContainerId,
        type: sessionType
      });

      logger.info(`Terminal session created: ${sessionId} (${sessionType})`);
      
      return {
        sessionId,
        type: sessionType,
        shell,
        created: session.created
      };
    }, {
      component: 'terminal',
      operation: 'createSession',
      retry: { maxRetries: 2, baseDelay: 1000 }
    });
  }

  /**
   * Create terminal session inside a Docker container
   */
  async createContainerTerminal(containerId, shell = 'sh') {
    try {
      const container = this.docker.getContainer(containerId);
      
      // Check if container is running
      const containerInfo = await container.inspect();
      if (!containerInfo.State.Running) {
        throw new Error('Container is not running');
      }

      // Create exec instance for interactive terminal
      const exec = await container.exec({
        Cmd: [shell, '-i'], // Interactive shell (not login shell to avoid issues)
        AttachStdin: true,
        AttachStdout: true,
        AttachStderr: true,
        Tty: true,
        WorkingDir: '/workspace',
        User: 'developer', // Use the developer user created in the container
        Env: [
          'TERM=xterm-256color',
          'HOME=/workspace',
          'PS1=/workspace # ', // Simple prompt without escape sequences initially
          'PATH=/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin',
          'SHELL=' + shell
        ]
      });

      // Start the exec instance
      const stream = await exec.start({
        hijack: true,
        stdin: true,
        Tty: true
      });

      // Send initial command to set up proper shell prompt and echo
      setTimeout(() => {
        try {
          // Set up proper shell environment
          const setupCommands = [
            'stty echo', // Ensure echo is enabled
            'export PS1="/workspace # "', // Set simple prompt
            'clear', // Clear screen
            'echo "Terminal connected successfully"', // Welcome message
            ''
          ].join('\n');

          stream.write(setupCommands);
        } catch (error) {
          logger.warn('Failed to send initial setup commands:', error);
        }
      }, 100);

      // Create the terminal process object with event handling capability
      const terminalProcess = {
        type: 'container',
        stream,
        exec,
        write: (data) => {
          try {
            // Validate input data
            if (data === undefined || data === null) {
              logger.warn('Attempted to write undefined/null data to container terminal');
              return;
            }

            // Convert to string first, then to Buffer to avoid type issues
            const stringData = typeof data === 'string' ? data : String(data);

            if (stringData.length === 0) {
              logger.debug('Empty string input to container terminal, ignoring');
              return;
            }

            logger.debug(`Writing to container terminal: ${JSON.stringify(stringData)}`);

            // Write directly as string to the stream for proper TTY handling
            stream.write(stringData);
          } catch (error) {
            logger.error('Error writing to container terminal:', error);
            throw error;
          }
        },
        resize: async (cols, rows) => {
          try {
            await exec.resize({ h: rows, w: cols });
            logger.debug(`Resized container terminal to ${cols}x${rows}`);
          } catch (error) {
            logger.warn('Failed to resize container terminal:', error);
          }
        },
        kill: () => {
          try {
            stream.destroy();
            logger.debug('Container terminal stream destroyed');
          } catch (error) {
            logger.warn('Failed to kill container terminal:', error);
          }
        }
      };

      // Send initial commands to set up the terminal properly
      setTimeout(() => {
        try {
          // Set up proper terminal environment using the validated write method
          terminalProcess.write('clear\r\n'); // Clear screen first
          terminalProcess.write('cd /workspace\r\n'); // Ensure we're in workspace
          terminalProcess.write('export PS1="\\[\\033[32m\\]/workspace #\\[\\033[0m\\] "\r\n'); // Set colored prompt
          terminalProcess.write('echo "\\033[36mTerminal ready - type commands below:\\033[0m"\r\n'); // Welcome message in cyan
        } catch (error) {
          logger.warn('Failed to send initial terminal commands:', error);
        }
      }, 500); // Increased delay to ensure container is ready

      return terminalProcess;
    } catch (error) {
      logger.error('Failed to create container terminal:', error);
      throw new Error(`Failed to create container terminal: ${error.message}`);
    }
  }

  /**
   * Create local terminal session
   */
  async createLocalTerminal(shell, cwd, environment) {
    try {
      const env = { ...process.env, ...environment };
      
      // Determine shell command and args for Alpine Linux container
      let shellCmd, shellArgs;
      // Always use sh in Alpine Linux containers
      shellCmd = 'sh';
      shellArgs = ['-i']; // Interactive mode

      const terminalProcess = spawn(shellCmd, shellArgs, {
        cwd,
        env,
        stdio: ['pipe', 'pipe', 'pipe']
      });

      return {
        type: 'local',
        process: terminalProcess,
        write: (data) => {
          try {
            // Validate input data
            if (data === undefined || data === null) {
              logger.warn('Attempted to write undefined/null data to local terminal');
              return;
            }

            // Convert to string if not already
            const stringData = typeof data === 'string' ? data : String(data);

            if (stringData.length === 0) {
              logger.debug('Empty string input to local terminal, ignoring');
              return;
            }

            terminalProcess.stdin.write(stringData);
          } catch (error) {
            logger.error('Error writing to local terminal:', error);
            throw error;
          }
        },
        resize: (cols, rows) => {
          // Local terminals don't support resize in this simple implementation
          logger.debug(`Terminal resize requested: ${cols}x${rows}`);
        },
        kill: () => terminalProcess.kill()
      };
    } catch (error) {
      logger.error('Failed to create local terminal:', error);
      throw new Error(`Failed to create local terminal: ${error.message}`);
    }
  }

  /**
   * Set up event handlers for terminal process
   */
  setupProcessHandlers(session) {
    const { process: terminalProcess } = session;

    if (terminalProcess.type === 'container') {
      // Handle container terminal stream
      // For TTY streams, we don't need to demux - data comes directly
      terminalProcess.stream.on('data', (data) => {
        try {
          // Convert buffer to string for logging but keep original data for output
          const output = data.toString('utf8');
          logger.debug(`[TERMINAL] Received output from container stream for session ${session.id}: ${JSON.stringify(output.substring(0, 200))}${output.length > 200 ? '...' : ''}`);

          // Handle the raw data directly to preserve formatting
          if (data && data.length > 0) {
            this.handleTerminalOutput(session.id, data);
          }
        } catch (error) {
          logger.error(`Error processing container stream data for session ${session.id}:`, error);
        }
      });

      // Handle stream errors
      terminalProcess.stream.on('error', (error) => {
        logger.error(`Container terminal stream error for session ${session.id}:`, error);
        this.handleTerminalOutput(session.id, `\r\n\x1b[31mTerminal error: ${error.message}\x1b[0m\r\n`);
      });

      // Handle stream close
      terminalProcess.stream.on('close', () => {
        logger.info(`Container terminal stream closed for session ${session.id}`);
        this.handleTerminalOutput(session.id, '\r\n\x1b[33mTerminal session ended\x1b[0m\r\n');
      });

      terminalProcess.stream.on('end', () => {
        logger.info(`Container terminal stream ended for session ${session.id}`);
        this.handleTerminalExit(session.id, 0);
      });

      terminalProcess.stream.on('error', (error) => {
        logger.error(`Container terminal error for session ${session.id}:`, error);
        this.handleTerminalExit(session.id, 1);
      });

      terminalProcess.stream.on('close', () => {
        logger.info(`Container terminal stream closed for session ${session.id}`);
        this.handleTerminalExit(session.id, 0);
      });

      // Add connection event handling
      terminalProcess.stream.on('connect', () => {
        logger.info(`Container terminal stream connected for session ${session.id}`);
      });
    } else {
      // Handle local terminal process
      terminalProcess.process.stdout.on('data', (data) => {
        this.handleTerminalOutput(session.id, data);
      });

      terminalProcess.process.stderr.on('data', (data) => {
        this.handleTerminalOutput(session.id, data);
      });

      terminalProcess.process.on('exit', (code) => {
        this.handleTerminalExit(session.id, code);
      });

      terminalProcess.process.on('error', (error) => {
        logger.error(`Local terminal error for session ${session.id}:`, error);
        this.handleTerminalExit(session.id, 1);
      });
    }
  }

  /**
   * Handle terminal output
   */
  handleTerminalOutput(sessionId, data) {
    try {
      const session = this.sessions.get(sessionId);
      if (!session) {
        logger.warn(`Attempted to handle output for non-existent session: ${sessionId}`);
        return;
      }

      // Handle both Buffer and string data
      let output;
      if (Buffer.isBuffer(data)) {
        output = data.toString('utf8');
      } else {
        output = String(data);
      }

      // Skip empty output
      if (!output || output.length === 0) {
        return;
      }

      session.lastActivity = new Date();

      logger.debug(`[TERMINAL] Output for session ${sessionId} (${output.length} chars): ${JSON.stringify(output.substring(0, 100))}${output.length > 100 ? '...' : ''}`);

      // Add to buffer for reconnection
      session.buffer += output;
      if (session.buffer.length > session.maxBufferSize) {
        session.buffer = session.buffer.slice(-session.maxBufferSize);
      }

      // Emit to connected clients immediately (synchronously for better performance)
      logger.debug(`[TERMINAL] Publishing output event for session ${sessionId}, data length: ${output.length}`);

      eventBus.publish(EventTypes.TERMINAL_OUTPUT, {
        sessionId,
        data: output
      });
    } catch (error) {
      logger.error(`Error handling terminal output for session ${sessionId}:`, error);
    }
  }

  /**
   * Handle terminal exit
   */
  async handleTerminalExit(sessionId, exitCode) {
    const session = this.sessions.get(sessionId);
    if (!session) return;

    session.isActive = false;
    
    // Clean up container session tracking
    if (session.containerId) {
      const containerSessions = this.containerSessions.get(session.containerId);
      if (containerSessions) {
        containerSessions.delete(sessionId);
        if (containerSessions.size === 0) {
          this.containerSessions.delete(session.containerId);
        }
      }
    }

    // Publish session end event
    await eventBus.publish(EventTypes.TERMINAL_SESSION_ENDED, {
      sessionId,
      exitCode,
      projectId: session.projectId
    });

    logger.info(`Terminal session ended: ${sessionId} (exit code: ${exitCode})`);
  }

  /**
   * Write data to terminal session
   */
  async writeToSession(sessionId, data) {
    return await errorHandler.executeWithProtection('terminal.writeToSession', async () => {
      const session = this.sessions.get(sessionId);
      if (!session) {
        throw new Error('Terminal session not found');
      }

      if (!session.isActive) {
        throw new Error('Terminal session is not active');
      }

      // Validate input data
      if (data === undefined || data === null) {
        logger.warn(`Attempted to write undefined/null data to session ${sessionId}`);
        return { success: false, error: 'Invalid input data' };
      }

      // Convert to string if not already
      const stringData = typeof data === 'string' ? data : String(data);

      if (stringData.length === 0) {
        logger.debug(`Empty string input for session ${sessionId}, ignoring`);
        return { success: true };
      }

      logger.info(`[DEBUG] Writing to terminal session ${sessionId}: ${JSON.stringify(stringData)}`);
      session.process.write(stringData);
      session.lastActivity = new Date();

      return { success: true };
    }, {
      component: 'terminal',
      operation: 'writeToSession',
      retry: { maxRetries: 1, baseDelay: 100 }
    });
  }

  /**
   * Resize terminal session
   */
  async resizeSession(sessionId, cols, rows) {
    return await errorHandler.executeWithProtection('terminal.resizeSession', async () => {
      const session = this.sessions.get(sessionId);
      if (!session) {
        throw new Error('Terminal session not found');
      }

      if (!session.isActive) {
        throw new Error('Terminal session is not active');
      }

      await session.process.resize(cols, rows);
      session.lastActivity = new Date();

      return { success: true };
    }, {
      component: 'terminal',
      operation: 'resizeSession',
      retry: { maxRetries: 1, baseDelay: 100 }
    });
  }

  /**
   * Execute command in terminal session (for Run button integration)
   */
  async executeCommandInSession(projectId, command, options = {}) {
    return await errorHandler.executeWithProtection('terminal.executeCommandInSession', async () => {
      // Find active terminal session for the project
      let targetSession = null;
      for (const [sessionId, session] of this.sessions.entries()) {
        if (session.projectId === projectId && session.isActive && session.type === 'container') {
          targetSession = session;
          break;
        }
      }

      // If no active session exists, create one
      if (!targetSession) {
        logger.info(`No active terminal session found for project ${projectId}, creating new session`);
        const newSession = await this.createSession({
          projectId,
          shell: options.shell || 'sh',
          cwd: options.cwd || '/workspace'
        });
        targetSession = this.sessions.get(newSession.sessionId);
      }

      if (!targetSession) {
        throw new Error('Failed to create or find terminal session for command execution');
      }

      // Clear terminal and show command being executed
      const clearCommand = '\x1b[2J\x1b[H'; // Clear screen and move cursor to top
      const commandHeader = `\x1b[32m$ ${command}\x1b[0m\r\n`; // Green command text

      // Write clear screen, command header, then the actual command
      await this.writeToSession(targetSession.id, clearCommand);
      await this.writeToSession(targetSession.id, commandHeader);
      await this.writeToSession(targetSession.id, command + '\r\n');

      return {
        success: true,
        sessionId: targetSession.id,
        projectId,
        command,
        message: 'Command executed in terminal session'
      };
    }, {
      component: 'terminal',
      operation: 'executeCommandInSession',
      retry: { maxRetries: 1, baseDelay: 500 }
    });
  }

  /**
   * Get session information
   */
  getSession(sessionId) {
    const session = this.sessions.get(sessionId);
    if (!session) {
      return null;
    }

    return {
      id: session.id,
      projectId: session.projectId,
      containerId: session.containerId,
      type: session.type,
      shell: session.shell,
      created: session.created,
      lastActivity: session.lastActivity,
      isActive: session.isActive,
      buffer: session.buffer
    };
  }

  /**
   * List all sessions
   */
  listSessions(projectId = null) {
    const sessions = Array.from(this.sessions.values());
    
    if (projectId) {
      return sessions
        .filter(session => session.projectId === projectId)
        .map(session => ({
          id: session.id,
          projectId: session.projectId,
          containerId: session.containerId,
          type: session.type,
          shell: session.shell,
          created: session.created,
          lastActivity: session.lastActivity,
          isActive: session.isActive
        }));
    }

    return sessions.map(session => ({
      id: session.id,
      projectId: session.projectId,
      containerId: session.containerId,
      type: session.type,
      shell: session.shell,
      created: session.created,
      lastActivity: session.lastActivity,
      isActive: session.isActive
    }));
  }

  /**
   * Kill terminal session
   */
  async killSession(sessionId) {
    return await errorHandler.executeWithProtection('terminal.killSession', async () => {
      const session = this.sessions.get(sessionId);
      if (!session) {
        throw new Error('Terminal session not found');
      }

      session.process.kill();
      this.sessions.delete(sessionId);

      // Clean up container session tracking
      if (session.containerId) {
        const containerSessions = this.containerSessions.get(session.containerId);
        if (containerSessions) {
          containerSessions.delete(sessionId);
          if (containerSessions.size === 0) {
            this.containerSessions.delete(session.containerId);
          }
        }
      }

      // Publish session kill event
      await eventBus.publish(EventTypes.TERMINAL_SESSION_KILLED, {
        sessionId,
        projectId: session.projectId
      });

      logger.info(`Terminal session killed: ${sessionId}`);
      return { success: true };
    }, {
      component: 'terminal',
      operation: 'killSession',
      retry: { maxRetries: 1, baseDelay: 100 }
    });
  }

  /**
   * Clean up inactive sessions
   */
  cleanupInactiveSessions(maxAge = 3600000) { // 1 hour default
    const now = new Date();
    const sessionsToClean = [];

    for (const [sessionId, session] of this.sessions) {
      if (!session.isActive || (now - session.lastActivity) > maxAge) {
        sessionsToClean.push(sessionId);
      }
    }

    sessionsToClean.forEach(sessionId => {
      this.killSession(sessionId).catch(error => {
        logger.warn(`Failed to cleanup session ${sessionId}:`, error);
      });
    });

    if (sessionsToClean.length > 0) {
      logger.info(`Cleaned up ${sessionsToClean.length} inactive terminal sessions`);
    }
  }
}

module.exports = {
  TerminalService
};
