const express = require('express');
const http = require('http');
const { Server } = require('socket.io');
const cors = require('cors');
const helmet = require('helmet');
const { setupContainerRoutes } = require('./routes/container.routes');
const { setupMonitoringRoutes } = require('./routes/monitoring.routes');
const { setupFileSystemRoutes } = require('./routes/filesystem.routes');
const { setupProjectRoutes } = require('./routes/project.routes');
const { setupTerminalRoutes, setupTerminalWebSocket } = require('./routes/terminal.routes');
const { initializeDocker } = require('./services/docker.service');
const logger = require('./utils/logger');
const { configManager, integrationService, eventBus, EventTypes } = require('../shared');

async function startServer() {
  try {
    // Initialize shared services first
    logger.info('Initializing shared services...');
    await configManager.initialize();
    await integrationService.initialize();

    // Create Express app and HTTP server
    const app = express();
    const server = http.createServer(app);
    const io = new Server(server, {
      cors: {
        origin: "*",
        methods: ["GET", "POST"]
      }
    });

    const config = configManager.get('containerManager');
    const PORT = process.env.PORT || config.port;

    // Middleware
    app.use(helmet());
    app.use(cors());
    app.use(express.json());

    // Initialize Docker connection
    const docker = await initializeDocker();

    // Create shared project service
    const { ProjectService } = require('./services/project.service');
    const projectService = new ProjectService(docker);

    // Setup routes
    setupContainerRoutes(app, docker);
    setupMonitoringRoutes(app, docker);
    setupFileSystemRoutes(app, docker);
    setupProjectRoutes(app, docker);

    // Setup terminal routes and WebSocket with project service
    const terminalService = setupTerminalRoutes(app, docker, projectService);
    setupTerminalWebSocket(io, terminalService);

    // Add health check endpoint
    app.get('/health', async (req, res) => {
      try {
        const dockerService = new (require('./services/docker.service').DockerService)(docker);
        const health = await dockerService.healthCheck();
        const systemHealth = integrationService.getSystemHealth();

        res.json({
          status: health.status === 'healthy' && systemHealth.integrationService.status === 'healthy' ? 'healthy' : 'unhealthy',
          timestamp: new Date().toISOString(),
          services: {
            docker: health,
            integration: systemHealth.integrationService,
            eventBus: systemHealth.eventBus
          }
        });
      } catch (error) {
        res.status(500).json({
          status: 'unhealthy',
          timestamp: new Date().toISOString(),
          error: error.message
        });
      }
    });

    // Error handling middleware with event publishing
    app.use(async (err, req, res, next) => {
      logger.error('Unhandled error:', err);

      // Publish error event
      await eventBus.publish(EventTypes.SYSTEM_ERROR, {
        component: 'container-manager',
        operation: 'http_request',
        error: err.message,
        path: req.path,
        method: req.method
      });

      res.status(500).json({ error: 'Internal server error' });
    });

    // Graceful shutdown handling
    const gracefulShutdown = async (signal) => {
      logger.info(`Received ${signal}. Starting graceful shutdown...`);

      // Publish shutdown event
      await eventBus.publish(EventTypes.SYSTEM_ERROR, {
        component: 'container-manager',
        action: 'shutdown',
        signal
      });

      // Close server and cleanup
      process.exit(0);
    };

    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));

    // Start server
    server.listen(PORT, () => {
      logger.info(`Container Manager service running on port ${PORT} with WebSocket support`);

      // Publish startup event
      eventBus.publish(EventTypes.SYSTEM_RECOVERY, {
        component: 'container-manager',
        action: 'startup_complete',
        port: PORT,
        message: 'Container Manager service started successfully with WebSocket support'
      });
    });

  } catch (error) {
    logger.error('Failed to start server:', error);

    // Publish startup failure event
    await eventBus.publish(EventTypes.SYSTEM_ERROR, {
      component: 'container-manager',
      action: 'startup_failed',
      error: error.message
    });

    process.exit(1);
  }
}

// Start the server
startServer();