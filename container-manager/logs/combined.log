{"level":"info","message":"Container Manager service running on port 3000","timestamp":"2025-06-09T13:19:54.066Z"}
{"level":"info","message":"Container Manager service running on port 3000","timestamp":"2025-06-09T13:24:22.051Z"}
{"address":"/var/run/docker.sock","code":"ENOENT","errno":-4058,"level":"error","message":"Error creating container test-ide-container: connect ENOENT /var/run/docker.sock","stack":"Error: connect ENOENT /var/run/docker.sock\n    at PipeConnectWrap.afterConnect [as oncomplete] (node:net:1634:16)","syscall":"connect","timestamp":"2025-06-09T13:24:28.125Z"}
{"address":"/var/run/docker.sock","code":"ENOENT","errno":-4058,"level":"error","message":"Error listing containers: connect ENOENT /var/run/docker.sock","stack":"Error: connect ENOENT /var/run/docker.sock\n    at PipeConnectWrap.afterConnect [as oncomplete] (node:net:1634:16)","syscall":"connect","timestamp":"2025-06-09T13:29:23.511Z"}
{"level":"info","message":"Docker connection initialized successfully","timestamp":"2025-06-09T13:36:48.698Z"}
{"level":"info","message":"Container Manager service running on port 3000","timestamp":"2025-06-09T13:36:48.702Z"}
{"json":{"message":"No such image: node:18-alpine"},"level":"error","message":"Error creating container test-ide-container: (HTTP code 404) no such container - No such image: node:18-alpine ","reason":"no such container","stack":"Error: (HTTP code 404) no such container - No such image: node:18-alpine \n    at D:\\cursor projects\\Latest\\cloud-ide-project\\container-manager\\node_modules\\docker-modem\\lib\\modem.js:383:17\n    at getCause (D:\\cursor projects\\Latest\\cloud-ide-project\\container-manager\\node_modules\\docker-modem\\lib\\modem.js:418:7)\n    at Modem.buildPayload (D:\\cursor projects\\Latest\\cloud-ide-project\\container-manager\\node_modules\\docker-modem\\lib\\modem.js:379:5)\n    at IncomingMessage.<anonymous> (D:\\cursor projects\\Latest\\cloud-ide-project\\container-manager\\node_modules\\docker-modem\\lib\\modem.js:347:16)\n    at IncomingMessage.emit (node:events:530:35)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)","statusCode":404,"timestamp":"2025-06-09T13:36:58.425Z"}
{"json":{"data":[80,111,114,116,115,32,97,114,101,32,110,111,116,32,97,118,97,105,108,97,98,108,101,58,32,101,120,112,111,115,105,110,103,32,112,111,114,116,32,84,67,80,32,48,46,48,46,48,46,48,58,51,48,48,48,32,45,62,32,49,50,55,46,48,46,48,46,49,58,48,58,32,108,105,115,116,101,110,32,116,99,112,32,48,46,48,46,48,46,48,58,51,48,48,48,58,32,98,105,110,100,58,32,79,110,108,121,32,111,110,101,32,117,115,97,103,101,32,111,102,32,101,97,99,104,32,115,111,99,107,101,116,32,97,100,100,114,101,115,115,32,40,112,114,111,116,111,99,111,108,47,110,101,116,119,111,114,107,32,97,100,100,114,101,115,115,47,112,111,114,116,41,32,105,115,32,110,111,114,109,97,108,108,121,32,112,101,114,109,105,116,116,101,100,46,10],"type":"Buffer"},"level":"error","message":"Error creating container test-ide-container: (HTTP code 500) server error - Ports are not available: exposing port TCP 0.0.0.0:3000 -> 127.0.0.1:0: listen tcp 0.0.0.0:3000: bind: Only one usage of each socket address (protocol/network address/port) is normally permitted.\n ","reason":"server error","stack":"Error: (HTTP code 500) server error - Ports are not available: exposing port TCP 0.0.0.0:3000 -> 127.0.0.1:0: listen tcp 0.0.0.0:3000: bind: Only one usage of each socket address (protocol/network address/port) is normally permitted.\n \n    at D:\\cursor projects\\Latest\\cloud-ide-project\\container-manager\\node_modules\\docker-modem\\lib\\modem.js:383:17\n    at getCause (D:\\cursor projects\\Latest\\cloud-ide-project\\container-manager\\node_modules\\docker-modem\\lib\\modem.js:418:7)\n    at Modem.buildPayload (D:\\cursor projects\\Latest\\cloud-ide-project\\container-manager\\node_modules\\docker-modem\\lib\\modem.js:379:5)\n    at IncomingMessage.<anonymous> (D:\\cursor projects\\Latest\\cloud-ide-project\\container-manager\\node_modules\\docker-modem\\lib\\modem.js:347:16)\n    at IncomingMessage.emit (node:events:530:35)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)","statusCode":500,"timestamp":"2025-06-09T13:38:37.000Z"}
{"level":"info","message":"Docker connection initialized successfully","timestamp":"2025-06-09T13:39:28.116Z"}
{"level":"info","message":"Container Manager service running on port 3000","timestamp":"2025-06-09T13:39:28.120Z"}
{"json":{"message":"Conflict. The container name \"/test-ide-container\" is already in use by container \"b24ba08ba10b5a95da2190b17b14c070856df1aec4f7f1c8ec4bd7104da35962\". You have to remove (or rename) that container to be able to reuse that name."},"level":"error","message":"Error creating container test-ide-container: (HTTP code 409) unexpected - Conflict. The container name \"/test-ide-container\" is already in use by container \"b24ba08ba10b5a95da2190b17b14c070856df1aec4f7f1c8ec4bd7104da35962\". You have to remove (or rename) that container to be able to reuse that name. ","stack":"Error: (HTTP code 409) unexpected - Conflict. The container name \"/test-ide-container\" is already in use by container \"b24ba08ba10b5a95da2190b17b14c070856df1aec4f7f1c8ec4bd7104da35962\". You have to remove (or rename) that container to be able to reuse that name. \n    at D:\\cursor projects\\Latest\\cloud-ide-project\\container-manager\\node_modules\\docker-modem\\lib\\modem.js:383:17\n    at getCause (D:\\cursor projects\\Latest\\cloud-ide-project\\container-manager\\node_modules\\docker-modem\\lib\\modem.js:418:7)\n    at Modem.buildPayload (D:\\cursor projects\\Latest\\cloud-ide-project\\container-manager\\node_modules\\docker-modem\\lib\\modem.js:379:5)\n    at IncomingMessage.<anonymous> (D:\\cursor projects\\Latest\\cloud-ide-project\\container-manager\\node_modules\\docker-modem\\lib\\modem.js:347:16)\n    at IncomingMessage.emit (node:events:530:35)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)","statusCode":409,"timestamp":"2025-06-09T13:39:34.875Z"}
{"level":"info","message":"Container test-ide-container222 created and started successfully","timestamp":"2025-06-09T13:40:05.095Z"}
{"level":"info","message":"Container b61bb3726344321ca2056e0d3c1b292fc3a41c9accfb91b6a6ff773a74422793 stopped successfully","timestamp":"2025-06-09T13:40:17.921Z"}
{"level":"info","message":"Container b61bb3726344321ca2056e0d3c1b292fc3a41c9accfb91b6a6ff773a74422793 removed successfully","timestamp":"2025-06-09T13:40:17.953Z"}
{"level":"info","message":"Docker connection initialized successfully","timestamp":"2025-06-09T13:42:24.651Z"}
{"level":"info","message":"Container Manager service running on port 3000","timestamp":"2025-06-09T13:42:24.656Z"}
{"level":"info","message":"Container test-ide-container created and started successfully","timestamp":"2025-06-09T13:42:36.899Z"}
{"level":"info","message":"Container 9494898e507c80058407c8ac1d4d90a72fec97fb64f66cecb49c25ed3ea5cd91 stopped successfully","timestamp":"2025-06-09T13:42:54.567Z"}
{"level":"info","message":"Container 9494898e507c80058407c8ac1d4d90a72fec97fb64f66cecb49c25ed3ea5cd91 removed successfully","timestamp":"2025-06-09T13:42:54.600Z"}
{"level":"info","message":"Container test-ide-container created and started successfully","timestamp":"2025-06-09T13:43:58.865Z"}
{"level":"info","message":"Container 444a2f4f16e38cc7f629d9da7027b70179b5264b98ee2d5732dbf9080e27e398 stopped successfully","timestamp":"2025-06-09T13:44:16.498Z"}
{"level":"info","message":"Container 444a2f4f16e38cc7f629d9da7027b70179b5264b98ee2d5732dbf9080e27e398 removed successfully","timestamp":"2025-06-09T13:44:16.537Z"}
{"level":"info","message":"Docker connection initialized successfully","timestamp":"2025-06-09T13:47:58.199Z"}
{"level":"info","message":"Container Manager service running on port 3000","timestamp":"2025-06-09T13:47:58.204Z"}
{"level":"info","message":"Container test-ide-container created and started successfully","timestamp":"2025-06-09T13:48:05.186Z"}
{"level":"info","message":"Container 073699dcbb8a717f0febe077f23b1535a1a483700a28f4e83866f60338212e9d stopped successfully","timestamp":"2025-06-09T13:48:22.771Z"}
{"level":"info","message":"Container 073699dcbb8a717f0febe077f23b1535a1a483700a28f4e83866f60338212e9d removed successfully","timestamp":"2025-06-09T13:48:22.811Z"}
{"level":"info","message":"Container test-ide-container222 created and started successfully","timestamp":"2025-06-09T13:50:51.509Z"}
{"level":"info","message":"Container e05bdc801c37fcffb1ca92f447f3ab583f492a7bc4270df9c894d97b55bad84c stopped successfully","timestamp":"2025-06-09T13:51:05.055Z"}
{"level":"info","message":"Container e05bdc801c37fcffb1ca92f447f3ab583f492a7bc4270df9c894d97b55bad84c removed successfully","timestamp":"2025-06-09T13:51:05.088Z"}
{"level":"info","message":"Initializing shared services...","timestamp":"2025-06-13T22:25:37.409Z"}
{"level":"info","message":"Initializing shared services...","timestamp":"2025-06-13T22:26:03.180Z"}
{"level":"info","message":"Initializing shared services...","timestamp":"2025-06-13T22:30:00.254Z"}
{"level":"info","message":"Docker connection initialized successfully","timestamp":"2025-06-13T22:30:00.278Z"}
{"level":"info","message":"Container Manager service running on port 3000 with WebSocket support","timestamp":"2025-06-13T22:30:00.284Z"}
{"level":"info","message":"Projects directory initialized at: /tmp/cloud-ide-projects","timestamp":"2025-06-13T22:30:00.286Z"}
{"level":"info","message":"Projects directory initialized at: /tmp/cloud-ide-projects","timestamp":"2025-06-13T22:30:00.287Z"}
{"level":"info","message":"Projects directory initialized at: /tmp/cloud-ide-projects","timestamp":"2025-06-13T22:30:00.287Z"}
{"level":"info","message":"Projects data file initialized","timestamp":"2025-06-13T22:30:00.288Z"}
{"level":"info","message":"Received SIGINT. Starting graceful shutdown...","timestamp":"2025-06-13T22:30:13.847Z"}
{"level":"info","message":"Created new collaboration room: project1:/test.js","timestamp":"2025-06-19T18:47:15.218Z"}
{"level":"info","message":"User 804f3f04-e1a0-46f0-a4f3-45b354b68a49 (Alice) joined room project1:/test.js","timestamp":"2025-06-19T18:47:15.220Z"}
{"level":"info","message":"User 4ce34b91-3568-409b-8c93-6e7ba6ea6b95 (Bob) joined room project1:/test.js","timestamp":"2025-06-19T18:47:15.221Z"}
{"level":"info","message":"User 804f3f04-e1a0-46f0-a4f3-45b354b68a49 changed nickname to Alice Smith in room project1:/test.js","timestamp":"2025-06-19T18:47:15.224Z"}
{"level":"info","message":"User 804f3f04-e1a0-46f0-a4f3-45b354b68a49 left room project1:/test.js","timestamp":"2025-06-19T18:47:15.225Z"}
{"level":"info","message":"User 4ce34b91-3568-409b-8c93-6e7ba6ea6b95 left room project1:/test.js","timestamp":"2025-06-19T18:47:15.226Z"}
{"level":"info","message":"Deleted empty collaboration room: project1:/test.js","timestamp":"2025-06-19T18:47:15.226Z"}
{"level":"info","message":"Created new collaboration room: project1:/test.js","timestamp":"2025-06-19T19:20:17.835Z"}
{"level":"info","message":"User 0539115c-7979-442d-9fb0-a527e47b5d03 (Alice) joined room project1:/test.js","timestamp":"2025-06-19T19:20:17.837Z"}
{"level":"info","message":"User 11bc3e27-2375-4f53-bf96-398210a62966 (Bob) joined room project1:/test.js","timestamp":"2025-06-19T19:20:17.838Z"}
{"level":"info","message":"User 0539115c-7979-442d-9fb0-a527e47b5d03 changed nickname to Alice Smith in room project1:/test.js","timestamp":"2025-06-19T19:20:17.842Z"}
{"level":"info","message":"User 0539115c-7979-442d-9fb0-a527e47b5d03 left room project1:/test.js","timestamp":"2025-06-19T19:20:17.843Z"}
{"level":"info","message":"User 11bc3e27-2375-4f53-bf96-398210a62966 left room project1:/test.js","timestamp":"2025-06-19T19:20:17.845Z"}
{"level":"info","message":"Deleted empty collaboration room: project1:/test.js","timestamp":"2025-06-19T19:20:17.845Z"}
{"level":"info","message":"Created new collaboration room: project1:/test.js","timestamp":"2025-06-19T19:20:52.666Z"}
{"level":"info","message":"User 9e71b5eb-337f-41c8-a2f2-3288efaaea8e (Anonymous Panda) joined room project1:/test.js","timestamp":"2025-06-19T19:20:52.670Z"}
{"level":"info","message":"User e82c67d7-545c-4426-8964-1a1a156a8919 (Anonymous Koala) joined room project1:/test.js","timestamp":"2025-06-19T19:20:52.671Z"}
{"level":"info","message":"User 20558543-ad2f-4fad-9bd0-de18311cf4a7 (Anonymous Tiger) joined room project1:/test.js","timestamp":"2025-06-19T19:20:52.672Z"}
{"level":"info","message":"Created new collaboration room: project1:/test.js","timestamp":"2025-06-19T19:34:25.828Z"}
{"level":"info","message":"User de3283cc-742f-4530-8cc8-c1ba6ddd31ac (User 1) joined room project1:/test.js","timestamp":"2025-06-19T19:34:25.832Z"}
{"level":"info","message":"User 07b02453-a665-4446-9edb-a0e814a1e39b (User 2) joined room project1:/test.js","timestamp":"2025-06-19T19:34:25.833Z"}
{"level":"info","message":"User b5c1e80c-4599-44cb-9c1b-f2dfc709176c (User 3) joined room project1:/test.js","timestamp":"2025-06-19T19:34:25.834Z"}
{"level":"info","message":"Created new collaboration room: project1:/test.js","timestamp":"2025-06-19T19:34:38.857Z"}
{"level":"info","message":"User 2a27eec7-a266-4d9e-8bd6-ca718ab42abf (Alice) joined room project1:/test.js","timestamp":"2025-06-19T19:34:38.859Z"}
{"level":"info","message":"User 685462e5-0e57-41a1-96ee-1d2b3c267dab (Bob) joined room project1:/test.js","timestamp":"2025-06-19T19:34:38.861Z"}
{"level":"info","message":"User 2a27eec7-a266-4d9e-8bd6-ca718ab42abf changed nickname to Alice Smith in room project1:/test.js","timestamp":"2025-06-19T19:34:38.866Z"}
{"level":"info","message":"User 2a27eec7-a266-4d9e-8bd6-ca718ab42abf left room project1:/test.js","timestamp":"2025-06-19T19:34:38.868Z"}
{"level":"info","message":"User 685462e5-0e57-41a1-96ee-1d2b3c267dab left room project1:/test.js","timestamp":"2025-06-19T19:34:38.869Z"}
{"level":"info","message":"Deleted empty collaboration room: project1:/test.js","timestamp":"2025-06-19T19:34:38.869Z"}
