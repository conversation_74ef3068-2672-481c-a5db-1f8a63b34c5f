{"address":"/var/run/docker.sock","code":"ENOENT","errno":-4058,"level":"error","message":"Error creating container test-ide-container: connect ENOENT /var/run/docker.sock","stack":"Error: connect ENOENT /var/run/docker.sock\n    at PipeConnectWrap.afterConnect [as oncomplete] (node:net:1634:16)","syscall":"connect","timestamp":"2025-06-09T13:24:28.125Z"}
{"address":"/var/run/docker.sock","code":"ENOENT","errno":-4058,"level":"error","message":"Error listing containers: connect ENOENT /var/run/docker.sock","stack":"Error: connect ENOENT /var/run/docker.sock\n    at PipeConnectWrap.afterConnect [as oncomplete] (node:net:1634:16)","syscall":"connect","timestamp":"2025-06-09T13:29:23.511Z"}
{"json":{"message":"No such image: node:18-alpine"},"level":"error","message":"Error creating container test-ide-container: (HTTP code 404) no such container - No such image: node:18-alpine ","reason":"no such container","stack":"Error: (HTTP code 404) no such container - No such image: node:18-alpine \n    at D:\\cursor projects\\Latest\\cloud-ide-project\\container-manager\\node_modules\\docker-modem\\lib\\modem.js:383:17\n    at getCause (D:\\cursor projects\\Latest\\cloud-ide-project\\container-manager\\node_modules\\docker-modem\\lib\\modem.js:418:7)\n    at Modem.buildPayload (D:\\cursor projects\\Latest\\cloud-ide-project\\container-manager\\node_modules\\docker-modem\\lib\\modem.js:379:5)\n    at IncomingMessage.<anonymous> (D:\\cursor projects\\Latest\\cloud-ide-project\\container-manager\\node_modules\\docker-modem\\lib\\modem.js:347:16)\n    at IncomingMessage.emit (node:events:530:35)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)","statusCode":404,"timestamp":"2025-06-09T13:36:58.425Z"}
{"json":{"data":[80,111,114,116,115,32,97,114,101,32,110,111,116,32,97,118,97,105,108,97,98,108,101,58,32,101,120,112,111,115,105,110,103,32,112,111,114,116,32,84,67,80,32,48,46,48,46,48,46,48,58,51,48,48,48,32,45,62,32,49,50,55,46,48,46,48,46,49,58,48,58,32,108,105,115,116,101,110,32,116,99,112,32,48,46,48,46,48,46,48,58,51,48,48,48,58,32,98,105,110,100,58,32,79,110,108,121,32,111,110,101,32,117,115,97,103,101,32,111,102,32,101,97,99,104,32,115,111,99,107,101,116,32,97,100,100,114,101,115,115,32,40,112,114,111,116,111,99,111,108,47,110,101,116,119,111,114,107,32,97,100,100,114,101,115,115,47,112,111,114,116,41,32,105,115,32,110,111,114,109,97,108,108,121,32,112,101,114,109,105,116,116,101,100,46,10],"type":"Buffer"},"level":"error","message":"Error creating container test-ide-container: (HTTP code 500) server error - Ports are not available: exposing port TCP 0.0.0.0:3000 -> 127.0.0.1:0: listen tcp 0.0.0.0:3000: bind: Only one usage of each socket address (protocol/network address/port) is normally permitted.\n ","reason":"server error","stack":"Error: (HTTP code 500) server error - Ports are not available: exposing port TCP 0.0.0.0:3000 -> 127.0.0.1:0: listen tcp 0.0.0.0:3000: bind: Only one usage of each socket address (protocol/network address/port) is normally permitted.\n \n    at D:\\cursor projects\\Latest\\cloud-ide-project\\container-manager\\node_modules\\docker-modem\\lib\\modem.js:383:17\n    at getCause (D:\\cursor projects\\Latest\\cloud-ide-project\\container-manager\\node_modules\\docker-modem\\lib\\modem.js:418:7)\n    at Modem.buildPayload (D:\\cursor projects\\Latest\\cloud-ide-project\\container-manager\\node_modules\\docker-modem\\lib\\modem.js:379:5)\n    at IncomingMessage.<anonymous> (D:\\cursor projects\\Latest\\cloud-ide-project\\container-manager\\node_modules\\docker-modem\\lib\\modem.js:347:16)\n    at IncomingMessage.emit (node:events:530:35)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)","statusCode":500,"timestamp":"2025-06-09T13:38:37.000Z"}
{"json":{"message":"Conflict. The container name \"/test-ide-container\" is already in use by container \"b24ba08ba10b5a95da2190b17b14c070856df1aec4f7f1c8ec4bd7104da35962\". You have to remove (or rename) that container to be able to reuse that name."},"level":"error","message":"Error creating container test-ide-container: (HTTP code 409) unexpected - Conflict. The container name \"/test-ide-container\" is already in use by container \"b24ba08ba10b5a95da2190b17b14c070856df1aec4f7f1c8ec4bd7104da35962\". You have to remove (or rename) that container to be able to reuse that name. ","stack":"Error: (HTTP code 409) unexpected - Conflict. The container name \"/test-ide-container\" is already in use by container \"b24ba08ba10b5a95da2190b17b14c070856df1aec4f7f1c8ec4bd7104da35962\". You have to remove (or rename) that container to be able to reuse that name. \n    at D:\\cursor projects\\Latest\\cloud-ide-project\\container-manager\\node_modules\\docker-modem\\lib\\modem.js:383:17\n    at getCause (D:\\cursor projects\\Latest\\cloud-ide-project\\container-manager\\node_modules\\docker-modem\\lib\\modem.js:418:7)\n    at Modem.buildPayload (D:\\cursor projects\\Latest\\cloud-ide-project\\container-manager\\node_modules\\docker-modem\\lib\\modem.js:379:5)\n    at IncomingMessage.<anonymous> (D:\\cursor projects\\Latest\\cloud-ide-project\\container-manager\\node_modules\\docker-modem\\lib\\modem.js:347:16)\n    at IncomingMessage.emit (node:events:530:35)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)","statusCode":409,"timestamp":"2025-06-09T13:39:34.875Z"}
{"code":"MODULE_NOT_FOUND","level":"error","message":"Failed to start server: Cannot find module '../../shared'\nRequire stack:\n- D:\\owais's_projects\\Latest\\cloud-ide-project\\container-manager\\src\\routes\\terminal.routes.js\n- D:\\owais's_projects\\Latest\\cloud-ide-project\\container-manager\\src\\index.js\n- D:\\owais's_projects\\Latest\\cloud-ide-project\\[eval]","requireStack":["D:\\owais's_projects\\Latest\\cloud-ide-project\\container-manager\\src\\routes\\terminal.routes.js","D:\\owais's_projects\\Latest\\cloud-ide-project\\container-manager\\src\\index.js","D:\\owais's_projects\\Latest\\cloud-ide-project\\[eval]"],"stack":"Error: Cannot find module '../../shared'\nRequire stack:\n- D:\\owais's_projects\\Latest\\cloud-ide-project\\container-manager\\src\\routes\\terminal.routes.js\n- D:\\owais's_projects\\Latest\\cloud-ide-project\\container-manager\\src\\index.js\n- D:\\owais's_projects\\Latest\\cloud-ide-project\\[eval]\n    at Function._resolveFilename (node:internal/modules/cjs/loader:1225:15)\n    at Function._load (node:internal/modules/cjs/loader:1055:27)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at setupTerminalWebSocket (D:\\owais's_projects\\Latest\\cloud-ide-project\\container-manager\\src\\routes\\terminal.routes.js:466:36)\n    at startServer (D:\\owais's_projects\\Latest\\cloud-ide-project\\container-manager\\src\\index.js:55:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-06-13T22:25:37.437Z"}
