#!/usr/bin/env node

/**
 * Performance Test Runner
 * Checks services and runs comprehensive performance tests
 */

const axios = require('axios');
const { spawn } = require('child_process');
const PerformanceTestSuite = require('./performance-test-suite');

class PerformanceTestRunner {
  constructor() {
    this.services = {
      containerManager: 'http://localhost:3000',
      dnsServer: 'http://localhost:8053'
    };
  }

  async checkServices() {
    console.log('🔍 Checking service availability...');
    
    const serviceChecks = [];
    
    for (const [name, url] of Object.entries(this.services)) {
      try {
        const response = await axios.get(`${url}/health`, { timeout: 5000 });
        if (response.status === 200) {
          console.log(`  ✅ ${name} is running`);
          serviceChecks.push({ name, status: 'running', url });
        } else {
          console.log(`  ⚠️  ${name} responded with status ${response.status}`);
          serviceChecks.push({ name, status: 'warning', url });
        }
      } catch (error) {
        console.log(`  ❌ ${name} is not accessible: ${error.message}`);
        serviceChecks.push({ name, status: 'error', url, error: error.message });
      }
    }
    
    return serviceChecks;
  }

  async startServices() {
    console.log('🚀 Starting services with docker-compose...');
    
    return new Promise((resolve, reject) => {
      const dockerCompose = spawn('docker-compose', ['-f', 'docker-compose.simplified.yml', 'up', '-d'], {
        stdio: 'inherit'
      });
      
      dockerCompose.on('close', (code) => {
        if (code === 0) {
          console.log('✅ Services started successfully');
          resolve();
        } else {
          reject(new Error(`docker-compose exited with code ${code}`));
        }
      });
      
      dockerCompose.on('error', (error) => {
        reject(error);
      });
    });
  }

  async waitForServices(maxWaitTime = 60000) {
    console.log('⏳ Waiting for services to be ready...');
    
    const startTime = Date.now();
    const checkInterval = 5000; // 5 seconds
    
    while (Date.now() - startTime < maxWaitTime) {
      const serviceChecks = await this.checkServices();
      const runningServices = serviceChecks.filter(s => s.status === 'running');
      
      if (runningServices.length === Object.keys(this.services).length) {
        console.log('✅ All services are ready!');
        return true;
      }
      
      console.log(`⏳ ${runningServices.length}/${Object.keys(this.services).length} services ready, waiting...`);
      await new Promise(resolve => setTimeout(resolve, checkInterval));
    }
    
    console.log('⚠️  Timeout waiting for services to be ready');
    return false;
  }

  async runPerformanceTests() {
    console.log('\n🚀 Starting Performance Test Suite...');
    console.log('=====================================\n');
    
    const suite = new PerformanceTestSuite();
    await suite.runAllTests();
  }

  async run() {
    try {
      // Check if services are already running
      const serviceChecks = await this.checkServices();
      const runningServices = serviceChecks.filter(s => s.status === 'running');
      
      if (runningServices.length === 0) {
        console.log('\n🚀 No services detected, starting them...');
        await this.startServices();
        
        // Wait for services to be ready
        const servicesReady = await this.waitForServices();
        if (!servicesReady) {
          throw new Error('Services failed to start within timeout period');
        }
      } else if (runningServices.length < Object.keys(this.services).length) {
        console.log('\n⚠️  Some services are not running. Please ensure all services are started.');
        console.log('You can start them with: docker-compose -f docker-compose.simplified.yml up -d');
        
        const proceed = await this.askUserToProceed();
        if (!proceed) {
          console.log('❌ Performance tests cancelled');
          return;
        }
      } else {
        console.log('\n✅ All services are running, proceeding with tests...');
      }
      
      // Run performance tests
      await this.runPerformanceTests();
      
    } catch (error) {
      console.error('❌ Error running performance tests:', error.message);
      process.exit(1);
    }
  }

  async askUserToProceed() {
    const readline = require('readline');
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });
    
    return new Promise((resolve) => {
      rl.question('Do you want to proceed with performance tests anyway? (y/N): ', (answer) => {
        rl.close();
        resolve(answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes');
      });
    });
  }
}

// Run if called directly
if (require.main === module) {
  const args = process.argv.slice(2);
  
  if (args.includes('--help') || args.includes('-h')) {
    console.log(`
🚀 Cloud IDE Performance Test Runner

This script checks service availability and runs comprehensive performance tests.

Usage: node run-performance-tests.js [options]

Options:
  --help, -h          Show this help message
  --check-only, -c    Only check service status, don't run tests
  --force-start, -f   Force start services even if some are running
  --no-start, -n      Don't attempt to start services automatically

Examples:
  node run-performance-tests.js              # Check services and run tests
  node run-performance-tests.js --check-only # Only check service status
  node run-performance-tests.js --force-start # Force restart all services
    `);
    process.exit(0);
  }
  
  const runner = new PerformanceTestRunner();
  
  if (args.includes('--check-only') || args.includes('-c')) {
    runner.checkServices().then((checks) => {
      console.log('\n📊 Service Status Summary:');
      checks.forEach(check => {
        const status = check.status === 'running' ? '✅' : 
                      check.status === 'warning' ? '⚠️' : '❌';
        console.log(`  ${status} ${check.name}: ${check.status}`);
      });
    }).catch(console.error);
  } else {
    runner.run().catch(console.error);
  }
}

module.exports = PerformanceTestRunner;
